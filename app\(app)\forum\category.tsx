import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  TextInput,
  Animated,
  Image,
  ActivityIndicator,
  RefreshControl,
  Platform,
  Alert,
} from 'react-native';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { ForumTopic, useForumData } from './hooks/useForumData';
import { format } from 'date-fns';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { supabase } from '../../../lib/supabase';

export default function CategoryScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const router = useRouter();
  const params = useLocalSearchParams();
  const insets = useSafeAreaInsets();
  
  const categoryId = params.id as string;
  const categoryName = params.name as string;
  const categoryColor = params.color as string || theme.colors.primary;
  const categoryIcon = params.icon as string || 'folder-outline';

  const [searchQuery, setSearchQuery] = useState('');
  const [topics, setTopics] = useState<ForumTopic[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreTopics, setHasMoreTopics] = useState(true);
  const [sortOption, setSortOption] = useState<'newest' | 'popular' | 'active'>('newest');
  const [filterOption, setFilterOption] = useState<'all' | 'pinned' | 'answered' | 'unanswered'>('all');
  const [showFilters, setShowFilters] = useState(false);

  const { addReaction, removeReaction } = useForumData();
  
  const fetchTopics = async (categoryId?: string, page = 1) => {
    const pageSize = 10;
    try {
      let query = supabase
        .from('forum_topics')
        .select(`
          id, title, content, created_at, updated_at, 
          user_id, category_id, is_anonymous, is_pinned, is_locked, moderation_status,
          category:forum_categories(id, name, icon, color)
        `)
        .order('is_pinned', { ascending: false })
        .order('created_at', { ascending: false });
      
      if (categoryId && categoryId !== 'undefined') {
        query = query.eq('category_id', categoryId);
      }
      
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);
      
      const { data: topicsData, error: topicsError } = await query;

      if (topicsError) throw topicsError;

      const formattedData = (topicsData || []).map(topic => {
        const categoryData = topic.category && topic.category.length > 0 
          ? topic.category[0] 
          : null;
        
        return {
          id: topic.id,
          title: topic.title,
          content: topic.content,
          created_at: topic.created_at,
          updated_at: topic.updated_at,
          user_id: topic.user_id,
          category_id: topic.category_id,
          is_anonymous: topic.is_anonymous,
          is_pinned: topic.is_pinned,
          is_locked: topic.is_locked,
          moderation_status: topic.moderation_status || null,
          has_reacted: false,
          _count: {
            reactions: 0,
            replies: 0
          },
          user: null,
          category: categoryData ? {
            id: categoryData.id,
            name: categoryData.name,
            icon: categoryData.icon,
            color: categoryData.color
          } : null
        } as ForumTopic;
      });

      for (const topic of formattedData) {
        const { count: reactionsCount } = await supabase
          .from('forum_reactions')
          .select('*', { count: 'exact' })
          .eq('target_id', topic.id)
          .eq('target_type', 'topic');
        
        const { count: repliesCount } = await supabase
          .from('forum_replies')
          .select('*', { count: 'exact' })
          .eq('topic_id', topic.id);
        
        topic._count.reactions = reactionsCount || 0;
        topic._count.replies = repliesCount || 0;
        
        if (user) {
          const { data: reactionData } = await supabase
            .from('forum_reactions')
            .select('id')
            .eq('target_id', topic.id)
            .eq('user_id', user.id)
            .eq('target_type', 'topic')
            .maybeSingle();
          
          topic.has_reacted = !!reactionData;
        }
      }

      const userIds = formattedData
        .filter(topic => !topic.is_anonymous)
        .map(topic => topic.user_id);

      if (userIds.length > 0) {
        const { data: userData, error: userError } = await supabase
          .from('profiles')
          .select('id, first_name, last_name, avatar_url')
          .in('id', userIds);

        if (!userError && userData) {
          const userMap = new Map(userData.map(u => [u.id, u]));
          
          formattedData.forEach(topic => {
            if (!topic.is_anonymous) {
              topic.user = userMap.get(topic.user_id) || null;
            }
          });
        }
      }

      return formattedData;
    } catch (err) {
      console.error('Error fetching topics:', err);
      throw err;
    }
  };

  useEffect(() => {
    loadTopics();
  }, [categoryId, sortOption, filterOption]);

  const loadTopics = async (page = 1, isRefresh = true) => {
    try {
      if (isRefresh) {
      setLoading(true);
      } else if (refreshing) {
        return;
      }
      
      setError(null);

      const data = await fetchTopics(categoryId, page);
      
      // Appliquer les filtres
      let filteredData = [...data];
      
      // Filter par recherche
      if (searchQuery) {
        filteredData = filteredData.filter(topic => 
          topic.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          topic.content.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }
      
      // Filtrer par option
      switch (filterOption) {
        case 'pinned':
          filteredData = filteredData.filter(topic => topic.is_pinned);
          break;
        case 'answered':
          filteredData = filteredData.filter(topic => topic._count?.replies > 0);
          break;
        case 'unanswered':
          filteredData = filteredData.filter(topic => !topic._count?.replies || topic._count.replies === 0);
          break;
      }
      
      // Tri
      filteredData.sort((a, b) => {
        if (a.is_pinned && !b.is_pinned) return -1;
        if (!a.is_pinned && b.is_pinned) return 1;
        
        switch (sortOption) {
          case 'newest':
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          case 'popular':
            const aPopularity = (a._count?.reactions || 0) + (a._count?.replies || 0);
            const bPopularity = (b._count?.reactions || 0) + (b._count?.replies || 0);
            return bPopularity - aPopularity;
          case 'active':
            return new Date(b.updated_at || b.created_at).getTime() - 
                  new Date(a.updated_at || a.created_at).getTime();
          default:
            return 0;
        }
      });
      
      if (isRefresh) {
        setTopics(filteredData);
      } else {
        setTopics(prev => [...prev, ...filteredData]);
      }
      
      setHasMoreTopics(data.length === 10); // Supposant que 10 est la taille de page
      setCurrentPage(page);
    } catch (err) {
      console.error('Error loading topics:', err);
      setError('Erreur lors du chargement des discussions');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };
  
  const handleRefresh = () => {
    setRefreshing(true);
    loadTopics(1, true);
  };
  
  const handleLoadMore = () => {
    if (!loading && hasMoreTopics) {
      loadTopics(currentPage + 1, false);
    }
  };
  
  const handleReaction = async (topicId: string, hasReacted: boolean) => {
    if (!user) {
      router.push('/');
      return;
    }

    try {
      // Mise à jour optimiste de l'UI
      setTopics(prev => prev.map(topic => {
        if (topic.id === topicId) {
          return {
            ...topic,
            has_reacted: !hasReacted,
            _count: {
              ...topic._count,
              reactions: hasReacted 
                ? Math.max((topic._count?.reactions || 0) - 1, 0) 
                : (topic._count?.reactions || 0) + 1
            }
          };
        }
        return topic;
      }));

      // Appel à l'API pour la mise à jour réelle
      if (hasReacted) {
        await removeReaction(topicId, user.id);
      } else {
        await addReaction(topicId, 'like', user.id);
      }
    } catch (err) {
      console.error('Error reacting to topic:', err);
      Alert.alert('Erreur', 'Impossible de réagir à cette discussion');
      loadTopics(); // Recharger en cas d'erreur
    }
  };
  
  const handleSearch = (text: string) => {
    setSearchQuery(text);
    // Filtrer localement pour une réponse instantanée
    if (text) {
      const filtered = topics.filter(topic => 
        topic.title.toLowerCase().includes(text.toLowerCase()) ||
        topic.content.toLowerCase().includes(text.toLowerCase())
      );
      setTopics(filtered);
    } else {
      // Recharger les données si la recherche est effacée
      loadTopics();
    }
  };

  const handleNewTopic = () => {
    if (!user) {
      router.push('/');
      return;
    }
    
    router.push({
      pathname: '/forum/new-topic',
      params: { 
        categoryId: categoryId,
        categoryName: categoryName
      }
    });
  };

  const renderTopicItem = ({ item }: { item: ForumTopic }) => (
    <View 
      style={[
        styles.topicCard, 
        { backgroundColor: theme.colors.surface }
      ]}
    >
      <TouchableOpacity
        style={styles.topicContainer}
        onPress={() => router.push({ 
      pathname: '/forum/topic',
          params: { id: item.id } 
        })}
      >
        <View style={styles.topicHeader}>
          <Image 
            source={{ 
              uri: item.is_anonymous 
                ? 'https://via.placeholder.com/40' 
                : (item.user?.avatar_url || 'https://via.placeholder.com/40')
            }} 
            style={styles.avatar} 
          />
          <View style={styles.topicInfo}>
            <Text 
              style={[styles.topicTitle, { color: theme.colors.text }]}
              numberOfLines={2}
            >
              {item.title}
            </Text>
            <View style={styles.topicMeta}>
              <Text style={[styles.authorName, { color: theme.colors.gray[500] }]}>
                {item.is_anonymous ? 'Anonyme' : `${item.user?.first_name || ''} ${item.user?.last_name || ''}`}
              </Text>
              <Text style={[styles.dot, { color: theme.colors.gray[400] }]}>•</Text>
              <Text style={[styles.date, { color: theme.colors.gray[500] }]}>
                {format(new Date(item.created_at), 'dd/MM/yyyy')}
              </Text>
            </View>
          </View>
          
          {item.is_pinned && (
            <View style={styles.pinnedBadge}>
              <Ionicons name="pin" size={14} color={categoryColor} />
            </View>
          )}
        </View>
          
        <View style={styles.topicPreview}>
          <Text 
            style={[styles.topicContent, { color: theme.colors.text + 'CC' }]}
            numberOfLines={2}
          >
            {item.content}
        </Text>
      </View>

        <View style={styles.topicFooter}>
          <View style={styles.badges}>
            {item.is_locked && (
              <View style={[styles.badge, { backgroundColor: theme.colors.gray[200] }]}>
                <Ionicons name="lock-closed" size={12} color={theme.colors.gray[600]} />
                <Text style={[styles.badgeText, { color: theme.colors.gray[600] }]}>
                  Verrouillé
                </Text>
              </View>
            )}
            
            {item._count?.replies > 0 && (
              <View 
                style={[
                  styles.badge, 
                  { backgroundColor: categoryColor + '20' }
                ]}
              >
                <Text style={[styles.badgeText, { color: categoryColor }]}>
                  {item._count.replies} réponse{item._count.replies > 1 ? 's' : ''}
                </Text>
              </View>
            )}
          </View>
            
          <View style={styles.actionButtons}>
      <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleReaction(item.id, item.has_reacted === true)}
            >
              <Ionicons
                name={item.has_reacted ? "heart" : "heart-outline"} 
                size={16} 
                color={item.has_reacted ? "#FF3B30" : theme.colors.gray[500]} 
              />
              <Text 
                style={[
                  styles.actionText, 
                  { color: item.has_reacted ? "#FF3B30" : theme.colors.gray[500] }
                ]}
              >
                {item._count?.reactions || 0}
        </Text>
      </TouchableOpacity>

            <View style={styles.actionButton}>
              <Ionicons name="chatbubble-outline" size={16} color={theme.colors.gray[500]} />
              <Text style={[styles.actionText, { color: theme.colors.gray[500] }]}>
                {item._count?.replies || 0}
          </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
  
  return (
    <>
      <Stack.Screen
        options={{
          headerStyle: {
            backgroundColor: categoryColor,
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
          headerTitle: () => (
            <Text style={styles.headerTitle}>
              {categoryName}
            </Text>
          ),
        }}
      />
    
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View 
          style={[
            styles.categoryHeader,
            { 
              backgroundColor: categoryColor,
              height: 180,
              opacity: 1
            }
          ]}
        >
          <View style={styles.categoryIcon}>
            <Ionicons name={categoryIcon as any} size={40} color="#fff" />
          </View>
          <Text style={styles.categoryName}>{categoryName}</Text>
          <Text style={styles.topicsCount}>
            {topics.length} discussion{topics.length !== 1 ? 's' : ''}
          </Text>
        </View>
        
        <View style={styles.searchContainer}>
          <View style={[styles.searchBar, { backgroundColor: theme.colors.surface }]}>
            <Ionicons name="search" size={20} color={theme.colors.gray[400]} />
            <TextInput
              style={[styles.searchInput, { color: theme.colors.text }]}
              placeholder="Rechercher dans cette catégorie..."
              placeholderTextColor={theme.colors.gray[400]}
              value={searchQuery}
              onChangeText={handleSearch}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => handleSearch('')}>
                <Ionicons name="close-circle" size={20} color={theme.colors.gray[400]} />
              </TouchableOpacity>
            ) : null}
          </View>
          
          <TouchableOpacity
            style={[styles.filterButton, { backgroundColor: categoryColor }]}
            onPress={() => setShowFilters(!showFilters)}
          >
            <Ionicons name="options" size={22} color="#fff" />
          </TouchableOpacity>
        </View>
        
        {showFilters && (
          <View 
            style={[styles.filtersContainer, { backgroundColor: theme.colors.surface }]}
          >
            <View style={styles.filtersSection}>
              <Text style={[styles.filtersTitle, { color: theme.colors.text }]}>
                Trier par
              </Text>
              <View style={styles.filtersOptions}>
            <TouchableOpacity
              style={[
                    styles.filterOption,
                    sortOption === 'newest' && { backgroundColor: categoryColor + '20' }
                  ]}
                  onPress={() => setSortOption('newest')}
                >
                  <Ionicons 
                    name="time-outline" 
                    size={18} 
                    color={sortOption === 'newest' ? categoryColor : theme.colors.text} 
                  />
                  <Text style={[
                    styles.filterOptionText,
                    { color: sortOption === 'newest' ? categoryColor : theme.colors.text }
                  ]}>
                    Plus récents
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.filterOption,
                    sortOption === 'popular' && { backgroundColor: categoryColor + '20' }
                  ]}
                  onPress={() => setSortOption('popular')}
                >
                  <Ionicons 
                    name="flame-outline" 
                    size={18} 
                    color={sortOption === 'popular' ? categoryColor : theme.colors.text} 
                  />
                  <Text style={[
                    styles.filterOptionText,
                    { color: sortOption === 'popular' ? categoryColor : theme.colors.text }
                  ]}>
                    Populaires
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.filterOption,
                    sortOption === 'active' && { backgroundColor: categoryColor + '20' }
                  ]}
                  onPress={() => setSortOption('active')}
                >
                  <Ionicons
                    name="pulse-outline" 
                    size={18} 
                    color={sortOption === 'active' ? categoryColor : theme.colors.text} 
                  />
                  <Text style={[
                    styles.filterOptionText,
                    { color: sortOption === 'active' ? categoryColor : theme.colors.text }
                  ]}>
                    Plus actifs
                  </Text>
                </TouchableOpacity>
              </View>
                </View>

            <View style={styles.filtersSection}>
              <Text style={[styles.filtersTitle, { color: theme.colors.text }]}>
                Filtrer
              </Text>
              <View style={styles.filtersOptions}>
                <TouchableOpacity 
                  style={[
                    styles.filterOption,
                    filterOption === 'all' && { backgroundColor: categoryColor + '20' }
                  ]}
                  onPress={() => setFilterOption('all')}
                >
                  <Ionicons
                    name="apps-outline" 
                    size={18} 
                    color={filterOption === 'all' ? categoryColor : theme.colors.text} 
                  />
                  <Text style={[
                    styles.filterOptionText,
                    { color: filterOption === 'all' ? categoryColor : theme.colors.text }
                  ]}>
                    Tous
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.filterOption,
                    filterOption === 'pinned' && { backgroundColor: categoryColor + '20' }
                  ]}
                  onPress={() => setFilterOption('pinned')}
                >
                  <Ionicons 
                    name="pin-outline" 
                    size={18} 
                    color={filterOption === 'pinned' ? categoryColor : theme.colors.text} 
                  />
                  <Text style={[
                    styles.filterOptionText,
                    { color: filterOption === 'pinned' ? categoryColor : theme.colors.text }
                  ]}>
                    Épinglés
                </Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.filterOption,
                    filterOption === 'answered' && { backgroundColor: categoryColor + '20' }
                  ]}
                  onPress={() => setFilterOption('answered')}
                >
                  <Ionicons
                    name="chatbox-outline" 
                    size={18} 
                    color={filterOption === 'answered' ? categoryColor : theme.colors.text} 
                  />
                  <Text style={[
                    styles.filterOptionText,
                    { color: filterOption === 'answered' ? categoryColor : theme.colors.text }
                  ]}>
                    Avec réponses
                  </Text>
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={[
                    styles.filterOption,
                    filterOption === 'unanswered' && { backgroundColor: categoryColor + '20' }
                  ]}
                  onPress={() => setFilterOption('unanswered')}
                >
                  <Ionicons
                    name="help-circle-outline" 
                    size={18} 
                    color={filterOption === 'unanswered' ? categoryColor : theme.colors.text} 
                  />
                  <Text style={[
                    styles.filterOptionText,
                    { color: filterOption === 'unanswered' ? categoryColor : theme.colors.text }
                  ]}>
                    Sans réponse
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
        
        <FlatList
          data={topics}
          renderItem={renderTopicItem}
          keyExtractor={(item: ForumTopic) => item.id}
          contentContainerStyle={[
            styles.topicList,
            { paddingBottom: insets.bottom + 80 }
          ]}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[categoryColor]}
              tintColor={categoryColor}
            />
          }
          onScroll={(event) => {
            const offsetY = event.nativeEvent.contentOffset.y;
          }}
          scrollEventThrottle={16}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListEmptyComponent={
            loading ? (
              <View style={styles.centerContent}>
                <ActivityIndicator size="large" color={categoryColor} />
              </View>
            ) : error ? (
              <View style={styles.centerContent}>
                <Ionicons name="alert-circle" size={50} color={theme.colors.error} />
                <Text style={[styles.errorText, { color: theme.colors.error }]}>
                  {error}
                </Text>
                <TouchableOpacity
                  style={[styles.retryButton, { backgroundColor: categoryColor }]}
                  onPress={handleRefresh}
                >
                  <Text style={styles.retryButtonText}>
                    Réessayer
                  </Text>
                </TouchableOpacity>
              </View>
            ) : (
              <View style={styles.centerContent}>
                <Ionicons name="chatbubbles-outline" size={50} color={theme.colors.gray[400]} />
                <Text style={[styles.emptyText, { color: theme.colors.gray[500] }]}>
                  Aucune discussion dans cette catégorie
                </Text>
                <Text style={[styles.emptySubtext, { color: theme.colors.gray[400] }]}>
                  Soyez le premier à démarrer une conversation
                  </Text>
                </View>
            )
          }
          ListFooterComponent={
            loading && !refreshing && topics.length > 0 ? (
              <View style={styles.footer}>
                <ActivityIndicator size="small" color={categoryColor} />
              </View>
            ) : null
          }
        />
        
        <TouchableOpacity
          style={[styles.newTopicButton, { backgroundColor: categoryColor }]}
          onPress={handleNewTopic}
        >
          <Ionicons name="add" size={24} color="white" />
            </TouchableOpacity>
    </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    color: '#fff',
    fontWeight: 'bold',
  },
  categoryHeader: {
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    zIndex: 10,
  },
  categoryIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  categoryName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
  },
  topicsCount: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginTop: 180,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    zIndex: 20,
  },
  searchBar: {
    flex: 1,
    flexDirection: 'row',
    padding: 10,
    borderRadius: 20,
    alignItems: 'center',
    gap: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filtersContainer: {
    padding: 16,
    marginHorizontal: 16,
    borderRadius: 12,
    marginBottom: 10,
  },
  filtersSection: {
    marginBottom: 16,
  },
  filtersTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  filtersOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    gap: 6,
    marginBottom: 8,
  },
  filterOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  topicList: {
    padding: 16,
    paddingTop: 0,
  },
  topicCard: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 12,
  },
  topicContainer: {
    padding: 16,
  },
  topicHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  topicInfo: {
    flex: 1,
  },
  topicTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  topicMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  authorName: {
    fontSize: 14,
  },
  dot: {
    marginHorizontal: 6,
  },
  date: {
    fontSize: 14,
  },
  pinnedBadge: {
    padding: 4,
    borderRadius: 4,
  },
  topicPreview: {
    marginBottom: 12,
  },
  topicContent: {
    fontSize: 14,
    lineHeight: 20,
  },
  topicFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  badges: {
    flexDirection: 'row',
    gap: 8,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
    gap: 4,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  actionText: {
    fontSize: 14,
  },
  centerContent: {
    padding: 40,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
  },
  retryButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  footer: {
    padding: 16,
    alignItems: 'center',
  },
  newTopicButton: {
    position: 'absolute',
    bottom: 30,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
