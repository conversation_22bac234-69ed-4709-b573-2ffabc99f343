/*
  # Create users view to fix compatibility issues

  1. New View
    - `users` - View that maps to profiles table
    - This solves the issue with code expecting a public.users table
*/

-- Create a view named 'users' that maps to the profiles table
CREATE OR REPLACE VIEW users AS
SELECT 
  id,
  email,
  first_name,
  last_name,
  role,
  phone,
  address,
  created_at,
  updated_at
FROM profiles;

-- Create a function to handle updates to the users view
CREATE OR REPLACE FUNCTION update_users_view()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE profiles
  SET 
    email = NEW.email,
    first_name = NEW.first_name,
    last_name = NEW.last_name,
    role = NEW.role,
    phone = NEW.phone,
    address = NEW.address,
    updated_at = now()
  WHERE id = NEW.id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to handle updates through the view
CREATE TRIGGER update_users_view_trigger
INSTEAD OF UPDATE ON users
FOR EACH ROW
EXECUTE FUNCTION update_users_view();

-- Create a function to handle inserts to the users view
CREATE OR REPLACE FUNCTION insert_users_view()
<PERSON><PERSON><PERSON>NS TRIGGER AS $$
BEGIN
  INSERT INTO profiles (
    id,
    email,
    first_name,
    last_name,
    role,
    phone,
    address,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.email,
    NEW.first_name,
    NEW.last_name,
    NEW.role,
    NEW.phone,
    NEW.address,
    COALESCE(NEW.created_at, now()),
    COALESCE(NEW.updated_at, now())
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to handle inserts through the view
CREATE TRIGGER insert_users_view_trigger
INSTEAD OF INSERT ON users
FOR EACH ROW
EXECUTE FUNCTION insert_users_view();

-- Note: RLS policies cannot be applied to views, only to tables
-- The security is inherited from the underlying profiles table

-- Create appointment_status enum type
-- CREATE TYPE appointment_status AS ENUM ('pending', 'confirmed', 'cancelled', 'completed', 'requested');

-- Mise à jour du type enum appointment_status pour inclure 'requested'
ALTER TYPE appointment_status ADD VALUE IF NOT EXISTS 'requested';

-- Create a function to get user appointments with professional details
CREATE OR REPLACE FUNCTION get_user_appointments(user_uuid uuid)
RETURNS TABLE (
  id uuid,
  date_time timestamptz,
  status appointment_status,
  type appointment_type,
  professional_id uuid,
  professional_type text,
  professional_speciality text,
  professional_user_id uuid,
  professional_user_first_name text,
  professional_user_last_name text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id,
    a.date_time,
    a.status,
    a.type,
    p.id as professional_id,
    p.type as professional_type,
    p.speciality as professional_speciality,
    u.id as professional_user_id,
    u.first_name as professional_user_first_name,
    u.last_name as professional_user_last_name
  FROM 
    appointments a
  JOIN 
    professionals p ON a.professional_id = p.id
  LEFT JOIN 
    profiles u ON p.user_id = u.id
  WHERE 
    a.user_id = user_uuid
  ORDER BY 
    a.date_time ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 