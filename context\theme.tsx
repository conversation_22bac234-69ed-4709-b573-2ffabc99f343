import React, { createContext, useContext, useState, useEffect } from 'react';
import { useColorScheme, Appearance } from 'react-native';
import { spacing, typography } from '../constants/theme';

// Définition des couleurs pour le thème clair
const lightColors = {
  primary: '#6366F1',
  primaryLight: '#A78BFA',
  secondary: '#EC4899',
  background: '#FFFFFF',
  surface: '#F3F4F6',
  text: '#1F2937',
  textSecondary: '#4B5563',
  cardBackground: '#FFFFFF',
  cardBorder: '#E5E7EB',
  inputBackground: '#F9FAFB',
  inputBorder: '#D1D5DB',
  error: '#EF4444',
  success: '#10B981',
  warning: '#F59E0B',
  white: '#FFFFFF',
  black: '#000000',
  bomoko: {
    blue: '#3A8BFF',
    lightBlue: '#8BB8FF',
    purple: '#7959D4',
    pink: '#FF7AAA',
    lightPink: '#E571A0',
    accent: '#5284E0',
    green: '#4CAF50',
    gradient: ['#3A8BFF', '#8BB8FF', '#FFFFFF'] as readonly [string, string, string],
    avatarGradient: ['#FF7AAA', '#E571A0', '#3A8BFF', '#5284E0'] as readonly [string, string, string, string],
    appGradient: ['#4382E8', '#5D6DE0', '#7959D4'] as readonly [string, string, string],
    logoGradient: ['#FF8EB4', '#E571A0', '#9B6ED6', '#5284E0'] as readonly [string, string, string, string]
  },
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },
};

// Définition des couleurs pour le thème sombre
const darkColors = {
  primary: '#8284FF',
  primaryLight: '#B79DFF',
  secondary: '#F17EB8',
  background: '#111827',
  surface: '#1F2937',
  text: '#F9FAFB',
  textSecondary: '#D1D5DB',
  cardBackground: '#1F2937',
  cardBorder: '#374151',
  inputBackground: '#374151',
  inputBorder: '#4B5563',
  error: '#F87171',
  success: '#34D399',
  warning: '#FBBF24',
  white: '#FFFFFF',
  black: '#000000',
  bomoko: {
    blue: '#5A9CFF',
    lightBlue: '#9CC3FF',
    purple: '#9579E4',
    pink: '#FF8AB7',
    lightPink: '#F591B7',
    accent: '#6E97E8',
    green: '#6ECF73',
    gradient: ['#5A9CFF', '#9CC3FF', '#1F2937'] as readonly [string, string, string],
    avatarGradient: ['#FF8AB7', '#F591B7', '#5A9CFF', '#6E97E8'] as readonly [string, string, string, string],
    appGradient: ['#5A9CFF', '#6E8AE8', '#9579E4'] as readonly [string, string, string],
    logoGradient: ['#FF8EB4', '#F591B7', '#9B6ED6', '#6E97E8'] as readonly [string, string, string, string]
  },
  gray: {
    50: '#111827',
    100: '#1F2937',
    200: '#374151',
    300: '#4B5563',
    400: '#6B7280',
    500: '#9CA3AF',
    600: '#D1D5DB',
    700: '#E5E7EB',
    800: '#F3F4F6',
    900: '#F9FAFB',
  },
};

// Types pour le thème
type ThemeColors = typeof lightColors;

export type Theme = {
  dark: boolean;
  colors: ThemeColors;
  spacing: typeof spacing;
  typography: typeof typography;
};

type ThemeMode = 'light' | 'dark' | 'system';

type ThemeContextType = {
  theme: Theme;
  themeMode: ThemeMode;
  setThemeMode: (mode: ThemeMode) => void;
  toggleTheme: () => void;
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  // Utiliser le mode de couleur du système comme valeur initiale
  const systemColorScheme = useColorScheme() || 'light';
  const [themeMode, setThemeMode] = useState<ThemeMode>('system');
  const [isDark, setIsDark] = useState(systemColorScheme === 'dark');

  // Mettre à jour le thème lorsque le mode change
  useEffect(() => {
    const updateTheme = () => {
      if (themeMode === 'system') {
        const systemTheme = Appearance.getColorScheme() || 'light';
        setIsDark(systemTheme === 'dark');
      } else {
        setIsDark(themeMode === 'dark');
      }
    };

    updateTheme();

    // Écouter les changements de thème du système
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      if (themeMode === 'system') {
        setIsDark(colorScheme === 'dark');
      }
    });

    return () => {
      subscription.remove();
    };
  }, [themeMode]);

  // Fonction pour basculer entre les thèmes
  const toggleTheme = () => {
    if (themeMode === 'light') {
      setThemeMode('dark');
    } else if (themeMode === 'dark') {
      setThemeMode('system');
    } else {
      setThemeMode('light');
    }
  };

  // Construire le thème actuel
  const theme: Theme = {
    dark: isDark,
    colors: isDark ? darkColors : lightColors,
    spacing,
    typography,
  };

  return (
    <ThemeContext.Provider value={{ theme, themeMode, setThemeMode, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}