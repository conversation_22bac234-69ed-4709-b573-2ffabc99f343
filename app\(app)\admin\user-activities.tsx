import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  TextInput,
  Alert,
  ScrollView,
} from 'react-native';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { supabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';

// Types pour les données
interface UserProfile {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  activity_metrics: {
    total_activities: number;
    activity_summary: Record<string, number>;
    first_activity: string;
    last_activity: string;
    activity_frequency: {
      daily: number;
      weekly: number;
    };
    engagement_score: number;
  };
}

interface UserActivity {
  id: string;
  user_id: string;
  activity_type: string;
  activity_data: any;
  metadata: any;
  created_at: string;
}

export default function UserActivitiesScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserProfile[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [userActivities, setUserActivities] = useState<UserActivity[]>([]);
  const [loadingActivities, setLoadingActivities] = useState(false);
  const [activityFilter, setActivityFilter] = useState<string | null>(null);

  // Vérifier si l'utilisateur est un administrateur
  useEffect(() => {
    const checkAdminAccess = async () => {
      if (!user) {
        Alert.alert('Accès refusé', 'Vous devez être connecté pour accéder à cette page.');
        router.replace('/');
        return;
      }

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', user.id)
          .single();

        if (error || !data || data.role !== 'admin') {
          Alert.alert('Accès refusé', 'Vous n\'avez pas les droits d\'administrateur nécessaires.');
          router.replace('/');
          return;
        }

        // L'utilisateur est un administrateur, charger les données
        loadUserProfiles();
      } catch (error) {
        console.error('Erreur lors de la vérification des droits:', error);
        Alert.alert('Erreur', 'Une erreur est survenue lors de la vérification de vos droits d\'accès.');
        router.replace('/');
      }
    };

    checkAdminAccess();
  }, [user]);

  // Charger les profils utilisateurs avec leurs métriques d'activité
  const loadUserProfiles = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .rpc('get_user_activity_analysis')
        .order('last_name', { ascending: true });

      if (error) {
        throw error;
      }

      setUsers(data || []);
      setFilteredUsers(data || []);
    } catch (error) {
      console.error('Erreur lors du chargement des profils:', error);
      Alert.alert('Erreur', 'Impossible de charger les profils utilisateurs.');
    } finally {
      setLoading(false);
    }
  };

  // Filtrer les utilisateurs en fonction de la recherche
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredUsers(users);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = users.filter(user => 
      (user.first_name && user.first_name.toLowerCase().includes(query)) ||
      (user.last_name && user.last_name.toLowerCase().includes(query)) ||
      (user.email && user.email.toLowerCase().includes(query))
    );
    
    setFilteredUsers(filtered);
  }, [searchQuery, users]);

  // Charger les activités d'un utilisateur spécifique
  const loadUserActivities = async (userId: string) => {
    try {
      setLoadingActivities(true);
      
      let query = supabase
        .from('user_activities')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(100);
      
      // Appliquer le filtre si nécessaire
      if (activityFilter) {
        query = query.eq('activity_type', activityFilter);
      }
      
      const { data, error } = await query;

      if (error) {
        throw error;
      }

      setUserActivities(data || []);
    } catch (error) {
      console.error('Erreur lors du chargement des activités:', error);
      Alert.alert('Erreur', 'Impossible de charger les activités de l\'utilisateur.');
    } finally {
      setLoadingActivities(false);
    }
  };

  // Sélectionner un utilisateur et charger ses activités
  const handleSelectUser = (user: UserProfile) => {
    setSelectedUser(user);
    loadUserActivities(user.id);
  };

  // Retourner à la liste des utilisateurs
  const handleBackToList = () => {
    setSelectedUser(null);
    setUserActivities([]);
    setActivityFilter(null);
  };

  // Filtrer les activités par type
  const handleFilterActivities = (type: string | null) => {
    setActivityFilter(type);
    if (selectedUser) {
      loadUserActivities(selectedUser.id);
    }
  };

  // Formater la date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return `${date.getDate().toString().padStart(2, '0')} ${
        ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'][date.getMonth()]
      } ${date.getFullYear()} à ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    } catch (e) {
      return 'Date inconnue';
    }
  };

  // Obtenir une couleur pour le type d'activité
  const getActivityColor = (type: string) => {
    const colors: Record<string, string> = {
      login: '#4CAF50',
      logout: '#F44336',
      profile_update: '#2196F3',
      forum_view: '#9C27B0',
      forum_post: '#673AB7',
      forum_reply: '#3F51B5',
      chatbot_interaction: '#00BCD4',
      appointment_created: '#009688',
      appointment_updated: '#8BC34A',
      appointment_cancelled: '#FF5722',
      alert_created: '#E91E63',
      alert_responded: '#CDDC39',
      document_uploaded: '#795548',
      document_viewed: '#607D8B',
      resource_accessed: '#FFC107',
      assessment_completed: '#FF9800',
      location_updated: '#9E9E9E',
      notification_received: '#03A9F4',
      notification_clicked: '#00BCD4'
    };
    
    return colors[type] || '#757575';
  };

  // Traduire le type d'activité
  const translateActivityType = (type: string) => {
    const translations: Record<string, string> = {
      login: 'Connexion',
      logout: 'Déconnexion',
      profile_update: 'Mise à jour du profil',
      forum_view: 'Consultation du forum',
      forum_post: 'Publication sur le forum',
      forum_reply: 'Réponse sur le forum',
      chatbot_interaction: 'Interaction avec le chatbot',
      appointment_created: 'Rendez-vous créé',
      appointment_updated: 'Rendez-vous modifié',
      appointment_cancelled: 'Rendez-vous annulé',
      alert_created: 'Alerte créée',
      alert_responded: 'Réponse à une alerte',
      document_uploaded: 'Document téléchargé',
      document_viewed: 'Document consulté',
      resource_accessed: 'Ressource consultée',
      assessment_completed: 'Évaluation complétée',
      location_updated: 'Position mise à jour',
      notification_received: 'Notification reçue',
      notification_clicked: 'Notification cliquée'
    };
    
    return translations[type] || type;
  };

  // Obtenir une icône pour le type d'activité
  const getActivityIcon = (type: string): string => {
    const icons: Record<string, string> = {
      login: 'log-in-outline',
      logout: 'log-out-outline',
      profile_update: 'person-outline',
      forum_view: 'eye-outline',
      forum_post: 'create-outline',
      forum_reply: 'chatbubble-outline',
      chatbot_interaction: 'chatbubbles-outline',
      appointment_created: 'calendar-outline',
      appointment_updated: 'calendar-outline',
      appointment_cancelled: 'calendar-outline',
      alert_created: 'alert-circle-outline',
      alert_responded: 'checkmark-circle-outline',
      document_uploaded: 'cloud-upload-outline',
      document_viewed: 'document-text-outline',
      resource_accessed: 'book-outline',
      assessment_completed: 'clipboard-outline',
      location_updated: 'location-outline',
      notification_received: 'notifications-outline',
      notification_clicked: 'notifications-outline'
    };
    
    return icons[type] || 'ellipsis-horizontal-outline';
  };

  // Rendu d'un élément de la liste des utilisateurs
  const renderUserItem = ({ item }: { item: UserProfile }) => (
    <TouchableOpacity
      style={[
        styles.userCard,
        { backgroundColor: theme.colors.surface }
      ]}
      onPress={() => handleSelectUser(item)}
    >
      <View style={styles.userHeader}>
        <View style={styles.userInfo}>
          <Text style={[styles.userName, { color: theme.colors.text }]}>
            {item.first_name} {item.last_name}
          </Text>
          <Text style={[styles.userEmail, { color: theme.colors.text }]}>
            {item.email}
          </Text>
          <View style={styles.roleBadge}>
            <Text style={styles.roleText}>
              {item.role === 'admin' ? 'Admin' : 
               item.role === 'doctor' ? 'Médecin' : 
               item.role === 'lawyer' ? 'Avocat' : 'Utilisateur'}
            </Text>
          </View>
        </View>
        <View style={styles.userMetrics}>
          <View style={styles.metricItem}>
            <Text style={styles.metricValue}>
              {item.activity_metrics?.total_activities || 0}
            </Text>
            <Text style={styles.metricLabel}>Activités</Text>
          </View>
          <View style={styles.metricItem}>
            <Text style={styles.metricValue}>
              {item.activity_metrics?.engagement_score || 0}
            </Text>
            <Text style={styles.metricLabel}>Engagement</Text>
          </View>
        </View>
      </View>
      
      <View style={styles.userFooter}>
        <Text style={styles.lastActivity}>
          Dernière activité: {item.activity_metrics?.last_activity ? 
            formatDate(item.activity_metrics.last_activity) : 'Jamais'}
        </Text>
        <Ionicons name="chevron-forward" size={20} color={theme.colors.primary} />
      </View>
    </TouchableOpacity>
  );

  // Rendu d'un élément de la liste des activités
  const renderActivityItem = ({ item }: { item: UserActivity }) => (
    <View
      style={[
        styles.activityCard,
        { backgroundColor: theme.colors.surface }
      ]}
    >
      <View style={styles.activityHeader}>
        <View style={[
          styles.activityTypeTag,
          { backgroundColor: getActivityColor(item.activity_type) }
        ]}>
          <Ionicons 
            name={getActivityIcon(item.activity_type) as any} 
            size={16} 
            color="#fff" 
          />
          <Text style={styles.activityTypeText}>
            {translateActivityType(item.activity_type)}
          </Text>
        </View>
        <Text style={[styles.activityDate, { color: theme.colors.text }]}>
          {formatDate(item.created_at)}
        </Text>
      </View>
      
      <View style={styles.activityContent}>
        {item.activity_data && Object.keys(item.activity_data).length > 0 && (
          <View style={styles.dataSection}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Données
            </Text>
            <ScrollView 
              horizontal 
              showsHorizontalScrollIndicator={false}
              style={styles.jsonScrollView}
            >
              <Text style={[styles.jsonText, { color: theme.colors.text }]}>
                {JSON.stringify(item.activity_data, null, 2)}
              </Text>
            </ScrollView>
          </View>
        )}
        
        {item.metadata && Object.keys(item.metadata).length > 0 && (
          <View style={styles.metadataSection}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
              Métadonnées
            </Text>
            <View style={styles.metadataList}>
              {Object.entries(item.metadata).map(([key, value]) => (
                <View key={key} style={styles.metadataItem}>
                  <Text style={[styles.metadataKey, { color: theme.colors.text }]}>
                    {key}:
                  </Text>
                  <Text style={[styles.metadataValue, { color: theme.colors.text }]}>
                    {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}
      </View>
    </View>
  );

  // Rendu de la liste des utilisateurs
  const renderUsersList = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Suivi des Activités Utilisateurs
        </Text>
        <TouchableOpacity 
          style={[styles.refreshButton, { backgroundColor: theme.colors.primary }]}
          onPress={loadUserProfiles}
        >
          <Ionicons name="refresh" size={20} color="#fff" />
        </TouchableOpacity>
      </View>
      
      <View style={[styles.searchContainer, { backgroundColor: theme.colors.surface }]}>
        <Ionicons name="search" size={20} color={theme.colors.text} />
        <TextInput
          style={[styles.searchInput, { color: theme.colors.text }]}
          placeholder="Rechercher un utilisateur..."
          placeholderTextColor={theme.colors.gray[400]}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery ? (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color={theme.colors.text} />
          </TouchableOpacity>
        ) : null}
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Chargement des utilisateurs...
          </Text>
        </View>
      ) : (
        <>
          <Text style={[styles.resultsCount, { color: theme.colors.text }]}>
            {filteredUsers.length} utilisateur{filteredUsers.length !== 1 ? 's' : ''} trouvé{filteredUsers.length !== 1 ? 's' : ''}
          </Text>
          
          <FlatList
            data={filteredUsers}
            renderItem={renderUserItem}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="people" size={50} color={theme.colors.gray[400]} />
                <Text style={[styles.emptyText, { color: theme.colors.text }]}>
                  Aucun utilisateur trouvé
                </Text>
              </View>
            }
          />
        </>
      )}
    </View>
  );

  // Rendu du détail des activités d'un utilisateur
  const renderUserActivities = () => {
    if (!selectedUser) return null;
    
    const activityTypes = userActivities.reduce((types, activity) => {
      if (!types.includes(activity.activity_type)) {
        types.push(activity.activity_type);
      }
      return types;
    }, [] as string[]);
    
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={handleBackToList}
          >
            <Ionicons name="arrow-back" size={24} color={theme.colors.primary} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Activités de {selectedUser.first_name} {selectedUser.last_name}
          </Text>
        </View>
        
        <View style={styles.userSummary}>
          <Text style={[styles.userEmail, { color: theme.colors.text }]}>
            {selectedUser.email}
          </Text>
          <View style={styles.summaryMetrics}>
            <View style={styles.summaryMetric}>
              <Text style={styles.metricValue}>
                {selectedUser.activity_metrics?.total_activities || 0}
              </Text>
              <Text style={styles.metricLabel}>Total</Text>
            </View>
            <View style={styles.summaryMetric}>
              <Text style={styles.metricValue}>
                {selectedUser.activity_metrics?.activity_frequency?.daily.toFixed(1) || 0}
              </Text>
              <Text style={styles.metricLabel}>Par jour</Text>
            </View>
            <View style={styles.summaryMetric}>
              <Text style={styles.metricValue}>
                {selectedUser.activity_metrics?.engagement_score || 0}
              </Text>
              <Text style={styles.metricLabel}>Engagement</Text>
            </View>
          </View>
        </View>
        
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.filterContainer}
        >
          <TouchableOpacity
            style={[
              styles.filterChip,
              !activityFilter && styles.activeFilterChip
            ]}
            onPress={() => handleFilterActivities(null)}
          >
            <Text style={[
              styles.filterChipText,
              !activityFilter && styles.activeFilterChipText
            ]}>
              Toutes
            </Text>
          </TouchableOpacity>
          
          {activityTypes.map(type => (
            <TouchableOpacity
              key={type}
              style={[
                styles.filterChip,
                { borderColor: getActivityColor(type) },
                activityFilter === type && { 
                  backgroundColor: getActivityColor(type),
                  borderColor: getActivityColor(type)
                }
              ]}
              onPress={() => handleFilterActivities(type)}
            >
              <Ionicons 
                name={getActivityIcon(type) as any} 
                size={16} 
                color={activityFilter === type ? '#fff' : getActivityColor(type)} 
                style={styles.filterChipIcon}
              />
              <Text style={[
                styles.filterChipText,
                { color: activityFilter === type ? '#fff' : getActivityColor(type) }
              ]}>
                {translateActivityType(type)}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
        
        {loadingActivities ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={[styles.loadingText, { color: theme.colors.text }]}>
              Chargement des activités...
            </Text>
          </View>
        ) : (
          <FlatList
            data={userActivities}
            renderItem={renderActivityItem}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="analytics" size={50} color={theme.colors.gray[400]} />
                <Text style={[styles.emptyText, { color: theme.colors.text }]}>
                  Aucune activité trouvée
                </Text>
                {activityFilter && (
                  <TouchableOpacity
                    style={[styles.clearFilterButton, { backgroundColor: theme.colors.primary }]}
                    onPress={() => handleFilterActivities(null)}
                  >
                    <Text style={styles.clearFilterText}>Effacer le filtre</Text>
                  </TouchableOpacity>
                )}
              </View>
            }
          />
        )}
      </View>
    );
  };

  return selectedUser ? renderUserActivities() : renderUsersList();
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
  },
  refreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 16,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  resultsCount: {
    marginBottom: 8,
    fontSize: 14,
  },
  listContainer: {
    paddingBottom: 16,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  userCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  userEmail: {
    fontSize: 14,
    marginTop: 4,
  },
  roleBadge: {
    backgroundColor: '#E0E0E0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginTop: 8,
  },
  roleText: {
    fontSize: 12,
    color: '#424242',
  },
  userMetrics: {
    flexDirection: 'row',
  },
  metricItem: {
    alignItems: 'center',
    marginLeft: 16,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  metricLabel: {
    fontSize: 12,
    color: '#757575',
  },
  userFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    paddingTop: 12,
  },
  lastActivity: {
    fontSize: 12,
    color: '#757575',
  },
  userSummary: {
    marginBottom: 16,
  },
  summaryMetrics: {
    flexDirection: 'row',
    marginTop: 12,
  },
  summaryMetric: {
    flex: 1,
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F5F5F5',
    marginRight: 8,
  },
  filterContainer: {
    marginBottom: 16,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
  },
  activeFilterChip: {
    backgroundColor: '#2196F3',
    borderColor: '#2196F3',
  },
  filterChipIcon: {
    marginRight: 4,
  },
  filterChipText: {
    fontSize: 12,
  },
  activeFilterChipText: {
    color: '#FFFFFF',
  },
  clearFilterButton: {
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  clearFilterText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  activityCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  activityTypeTag: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  activityTypeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  activityDate: {
    fontSize: 12,
  },
  activityContent: {
    marginTop: 8,
  },
  dataSection: {
    marginBottom: 12,
  },
  metadataSection: {
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  jsonScrollView: {
    maxHeight: 100,
  },
  jsonText: {
    fontFamily: 'monospace',
    fontSize: 12,
  },
  metadataList: {
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    padding: 8,
  },
  metadataItem: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  metadataKey: {
    fontSize: 12,
    fontWeight: 'bold',
    marginRight: 4,
  },
  metadataValue: {
    fontSize: 12,
    flex: 1,
  },
}); 