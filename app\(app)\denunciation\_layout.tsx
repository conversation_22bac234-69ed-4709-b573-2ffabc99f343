import React from 'react';
import { Stack } from 'expo-router';
import { Platform } from 'react-native';

export default function DenunciationLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: true,
        headerStyle: {
          backgroundColor: '#fff',
        },
        headerTintColor: '#000',
        headerTitleStyle: {
          fontWeight: '600',
        },
        animation: Platform.OS === 'ios' ? 'default' : 'slide_from_right',
        presentation: 'card',
      }}
    >
      <Stack.Screen 
        name="index" 
        options={{
          title: "Signaler une violence",
          headerLargeTitle: Platform.OS === 'ios',
        }}
      />
      <Stack.Screen 
        name="success" 
        options={{
          title: "Signalement envoyé",
          animation: 'slide_from_right',
        }}
      />
    </Stack>
  );
}
