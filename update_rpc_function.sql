-- Modification de la fonction get_user_appointments pour gérer la conversion de type appointment_status en text
CREATE OR REPLACE FUNCTION public.get_user_appointments(user_uuid uuid)
RETURNS TABLE(
  id uuid, 
  date_time timestamp with time zone, 
  status text, 
  type text, 
  professional_id uuid, 
  professional_type text, 
  professional_speciality text, 
  professional_user_id uuid, 
  professional_user_first_name text, 
  professional_user_last_name text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
  RETURN QUERY
  SELECT 
    a.id,
    a.date_time,
    a.status::text, -- Conversion explicite du type appointment_status en text
    a.type,
    p.id as professional_id,
    p.type as professional_type,
    p.speciality as professional_speciality,
    u.id as professional_user_id,
    u.first_name as professional_user_first_name,
    u.last_name as professional_user_last_name
  FROM 
    appointments a
  LEFT JOIN 
    professionals p ON a.professional_id = p.id
  LEFT JOIN 
    profiles u ON p.user_id = u.id
  WHERE 
    a.user_id = user_uuid
  ORDER BY 
    a.date_time ASC;
END;
$function$ 