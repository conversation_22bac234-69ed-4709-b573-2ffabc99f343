import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Stack } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../../context/theme';

type EmergencyInstruction = {
  title: string;
  steps: string[];
  icon: any;
};

export default function FirstAidScreen() {
  const { theme } = useTheme();
  
  const emergencyInstructions: EmergencyInstruction[] = [
    {
      title: "Arrêt cardiaque",
      steps: [
        "Appelez ou faites appeler les secours (112 ou 15)",
        "Allongez la victime sur le dos sur une surface dure",
        "Placez vos mains au milieu du thorax",
        "Comprimez fortement, bras tendus, 100-120 fois par minute",
        "Si possible, utilisez un défibrillateur en suivant les instructions",
        "Continuez jusqu'à l'arrivée des secours ou jusqu'à ce que la victime respire normalement"
      ],
      icon: "heart",
    },
    {
      title: "Hémorragie",
      steps: [
        "Appelez ou faites appeler les secours (112 ou 15)",
        "Allongez la victime",
        "Appuyez directement sur la plaie avec un linge propre ou votre main (utilisez des gants si disponibles)",
        "Maintenez une pression constante jusqu'à l'arrivée des secours",
        "Si le saignement persiste, appliquez un second linge sans retirer le premier"
      ],
      icon: "water",
    },
    {
      title: "Étouffement",
      steps: [
        "Donnez 5 claques dans le dos entre les omoplates",
        "Si inefficace, effectuez 5 compressions abdominales (méthode de Heimlich)",
        "Placez-vous derrière la victime, entourez sa taille de vos bras",
        "Placez un poing fermé entre le nombril et le sternum",
        "Tirez brusquement vers vous et vers le haut",
        "Alternez 5 claques dans le dos et 5 compressions jusqu'à l'expulsion du corps étranger"
      ],
      icon: "medical",
    },
    {
      title: "Brûlure",
      steps: [
        "Refroidissez la brûlure avec de l'eau à 15-25°C pendant 15 minutes",
        "Ne percez pas les cloques",
        "Retirez les vêtements sauf s'ils collent à la peau",
        "Couvrez avec un linge propre",
        "Consultez un médecin pour toute brûlure grave ou étendue"
      ],
      icon: "flame",
    },
    {
      title: "Position latérale de sécurité",
      steps: [
        "Vérifiez que la victime respire",
        "Placez le bras proche de vous à angle droit avec la paume vers le haut",
        "Placez l'autre bras sur la poitrine, main contre la joue",
        "Pliez la jambe éloignée de vous",
        "Faites rouler la victime vers vous en maintenant sa main contre sa joue",
        "Stabilisez en pliant la jambe supérieure à angle droit",
        "Assurez-vous que les voies respiratoires sont dégagées"
      ],
      icon: "body",
    }
  ];

  return (
    <>
      <Stack.Screen 
        options={{
          title: 'Premiers secours',
          headerStyle: {
            backgroundColor: '#FF3B30',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }} 
      />
      <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.header}>
          <Text style={styles.title}>GESTES QUI SAUVENT</Text>
          <Text style={styles.subtitle}>Techniques de premiers secours</Text>
        </View>

        <Text style={styles.disclaimer}>
          Ces instructions sont fournies à titre informatif uniquement et ne remplacent pas une formation aux premiers secours. En cas d'urgence, contactez toujours les services d'urgence (112).
        </Text>

        {emergencyInstructions.map((instruction, index) => (
          <View key={index} style={styles.card}>
            <View style={styles.cardHeader}>
              <Ionicons name={instruction.icon} size={24} color="#FF3B30" />
              <Text style={styles.cardTitle}>{instruction.title}</Text>
            </View>
            <View style={styles.cardContent}>
              {instruction.steps.map((step, stepIndex) => (
                <View key={stepIndex} style={styles.step}>
                  <Text style={styles.stepNumber}>{stepIndex + 1}</Text>
                  <Text style={styles.stepText}>{step}</Text>
                </View>
              ))}
            </View>
          </View>
        ))}
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    backgroundColor: '#FFF5F5',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#FF3B30',
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  disclaimer: {
    margin: 15,
    padding: 10,
    backgroundColor: '#FFF9E5',
    borderRadius: 5,
    borderLeftWidth: 4,
    borderLeftColor: '#FFD700',
    fontSize: 12,
    fontStyle: 'italic',
    color: '#666',
  },
  card: {
    margin: 10,
    backgroundColor: '#F8F8F8',
    borderRadius: 10,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#FFE5E5',
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  cardContent: {
    padding: 15,
  },
  step: {
    flexDirection: 'row',
    marginBottom: 10,
    alignItems: 'flex-start',
  },
  stepNumber: {
    backgroundColor: '#FF3B30',
    color: 'white',
    width: 22,
    height: 22,
    borderRadius: 11,
    textAlign: 'center',
    lineHeight: 22,
    marginRight: 10,
    fontWeight: 'bold',
    fontSize: 12,
  },
  stepText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 22,
  },
}); 