// TypeScript definitions for ws polyfill

declare module 'ws' {
  export interface WebSocketEventMap {
    close: CloseEvent;
    error: Event;
    message: MessageEvent;
    open: Event;
  }

  export class WebSocket {
    static readonly CLOSED: number;
    static readonly CLOSING: number;
    static readonly CONNECTING: number;
    static readonly OPEN: number;

    readonly CLOSED: number;
    readonly CLOSING: number;
    readonly CONNECTING: number;
    readonly OPEN: number;

    binaryType: string;
    readonly bufferedAmount: number;
    readonly extensions: string;
    onclose: ((this: WebSocket, ev: CloseEvent) => any) | null;
    onerror: ((this: WebSocket, ev: Event) => any) | null;
    onmessage: ((this: WebSocket, ev: MessageEvent) => any) | null;
    onopen: ((this: WebSocket, ev: Event) => any) | null;
    readonly protocol: string;
    readonly readyState: number;
    readonly url: string;

    constructor(url: string, protocols?: string | string[], options?: any);
    close(code?: number, reason?: string): void;
    send(data: any): void;
    addEventListener<K extends keyof WebSocketEventMap>(
      type: K,
      listener: (this: WebSocket, ev: WebSocketEventMap[K]) => any,
      options?: boolean | AddEventListenerOptions
    ): void;
    removeEventListener<K extends keyof WebSocketEventMap>(
      type: K,
      listener: (this: WebSocket, ev: WebSocketEventMap[K]) => any,
      options?: boolean | EventListenerOptions
    ): void;
    dispatchEvent(event: Event): boolean;
  }

  export class WebSocketServer {
    constructor(options?: any, callback?: () => void);
    close(callback?: () => void): void;
    handleUpgrade(request: any, socket: any, head: any, callback: any): void;
    shouldHandle(request: any): boolean;
  }

  export function createWebSocketStream(ws: WebSocket, options?: any): any;

  export const CONNECTING: number;
  export const OPEN: number;
  export const CLOSING: number;
  export const CLOSED: number;

  export default WebSocket;
}
