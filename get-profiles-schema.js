const { createClient } = require('@supabase/supabase-js');

// Créer un client Supabase avec les informations correctes
const supabaseUrl = 'https://tbenxstjfoviabhvcppg.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRiZW54c3RqZm92aWFiaHZjcHBnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDAxMzI0NjEsImV4cCI6MjA1NTcwODQ2MX0.FdAZxTxnRqthkf_FjeEUIx2OjDjVmxVxDFADUbd6wZI';
const supabase = createClient(supabaseUrl, supabaseKey);

async function getProfilesSchema() {
  try {
    // Essayer d'insérer un enregistrement fictif pour voir la structure
    const { error: insertError } = await supabase
      .from('profiles')
      .insert({
        id: '00000000-0000-0000-0000-000000000000', // UUID fictif
        email: '<EMAIL>',
        role: 'victim',
        first_name: 'Test',
        last_name: 'User',
        phone: '1234567890',
        address: '123 Test St',
        date_of_birth: '01/01/2000',
        gender_identity: 'other',
        emergency_contact: '0987654321',
        language_preference: 'fr',
        notification_preferences: { email: true, push: true, sms: false },
        profile_completed: false,
        avatar_url: null
      })
      .select();
    
    if (insertError) {
      console.log('Erreur lors de l\'insertion (attendue):', insertError.message);
      
      // Analyser le message d'erreur pour obtenir des informations sur la structure
      if (insertError.message.includes('violates foreign key constraint')) {
        console.log('La colonne id est une clé étrangère référençant auth.users');
      }
      
      if (insertError.message.includes('violates not-null constraint')) {
        const match = insertError.message.match(/column "([^"]+)"/);
        if (match) {
          console.log(`La colonne ${match[1]} ne peut pas être nulle`);
        }
      }
      
      if (insertError.message.includes('violates check constraint')) {
        console.log('Une contrainte de vérification a été violée');
      }
    }
    
    // Exécuter une requête SQL pour obtenir la structure de la table
    const { data: columns, error: columnsError } = await supabase
      .rpc('get_table_columns', { table_name: 'profiles' });
    
    if (columnsError) {
      console.error('Erreur lors de la récupération des colonnes:', columnsError);
    } else if (columns) {
      console.log('Colonnes de la table profiles:');
      columns.forEach(column => {
        console.log(`${column.column_name}: ${column.data_type} ${column.is_nullable === 'NO' ? '(NOT NULL)' : ''}`);
      });
    }
    
    // Essayer de récupérer un enregistrement existant
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('Erreur lors de la récupération des données:', error);
      return;
    }
    
    if (data && data.length > 0) {
      console.log('\nStructure basée sur un enregistrement existant:');
      const profile = data[0];
      
      // Afficher les noms des colonnes et leurs types
      Object.keys(profile).forEach(key => {
        const value = profile[key];
        const type = typeof value;
        console.log(`${key}: ${type} ${value === null ? '(nullable)' : ''}`);
      });
    } else {
      console.log('\nAucun enregistrement trouvé dans la table profiles.');
    }
    
    // Essayer de récupérer la structure à partir des migrations
    console.log('\nAnalyse des fichiers de migration pour la structure de la table:');
    console.log('id: uuid (PRIMARY KEY, REFERENCES auth.users)');
    console.log('email: text (NOT NULL, UNIQUE)');
    console.log('role: text (NOT NULL, DEFAULT \'victim\', CHECK IN (\'victim\', \'doctor\', \'lawyer\', \'admin\'))');
    console.log('created_at: timestamptz (DEFAULT now())');
    console.log('updated_at: timestamptz (DEFAULT now())');
    console.log('first_name: text (nullable)');
    console.log('last_name: text (nullable)');
    console.log('phone: text (nullable)');
    console.log('address: text (nullable)');
    console.log('date_of_birth: text (nullable)');
    console.log('gender_identity: text (nullable)');
    console.log('emergency_contact: text (nullable)');
    console.log('language_preference: text (DEFAULT \'fr\')');
    console.log('notification_preferences: jsonb (DEFAULT \'{"email": true, "push": true, "sms": false}\')');
    console.log('profile_completed: boolean (DEFAULT false)');
    console.log('avatar_url: text (nullable)');
    
  } catch (err) {
    console.error('Erreur:', err);
  }
}

getProfilesSchema(); 