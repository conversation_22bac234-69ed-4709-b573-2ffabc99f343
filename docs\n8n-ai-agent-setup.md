# Configuration de l'Agent AI N8N pour les Rendez-vous

Ce document explique comment configurer l'agent AI N8N pour traiter les demandes de rendez-vous dans l'application Bomoko Mobile.

## Prérequis

- Une instance N8N fonctionnelle
- Un accès à la base de données Supabase
- Un compte OpenAI pour l'API GPT (ou autre modèle de langage)

## Vue d'ensemble du processus

1. L'utilisateur remplit un formulaire de demande de rendez-vous dans l'application
2. L'application envoie les données au webhook N8N
3. N8N traite la demande avec l'aide d'un modèle de langage (GPT)
4. L'agent AI pose des questions supplémentaires à l'utilisateur si nécessaire
5. L'agent AI trouve un professionnel disponible et crée un rendez-vous
6. L'application affiche les détails du rendez-vous à l'utilisateur

## Configuration de la base de données

Assurez-vous que les fonctions RPC suivantes sont disponibles dans votre base de données Supabase :

- `create_appointment_by_n8n` : Permet à N8N de créer un rendez-vous
- `find_available_professionals` : Permet à N8N de trouver des professionnels disponibles

Ces fonctions sont créées par la migration `20250301000001_add_n8n_appointment_functions.sql`.

## Configuration du webhook dans Supabase

1. Dans la table `webhook_configurations`, assurez-vous d'avoir une entrée avec :
   - `name` : "RDV AI WEBHOOK URL"
   - `url` : URL de votre webhook N8N
   - `backup_url` : URL de secours (optionnel)
   - `is_active` : true
   - `api_key` : Clé API pour l'authentification
   - `backup_api_key` : Clé API de secours (optionnel)

## Configuration du workflow N8N

### 1. Créer un nouveau workflow

- Nom : "Bomoko - Agent AI pour Rendez-vous"
- Description : "Traite les demandes de rendez-vous et crée des rendez-vous avec des professionnels adaptés"

### 2. Configurer le webhook

- Ajouter un nœud "Webhook"
- Méthode : POST
- Chemin : /appointment-ai-agent
- Authentification : Header (X-App-Token)
- Valeur du token : bomoko-app-token-123

### 3. Analyser la demande initiale

- Ajouter un nœud "Switch"
- Condition : `{{$json.action}}`
- Cas "initialize_conversation" : Traiter l'initialisation
- Cas "send_message" : Traiter les messages
- Cas "cancel_conversation" : Traiter l'annulation

### 4. Initialisation de la conversation

Pour le cas "initialize_conversation" :

1. Ajouter un nœud "OpenAI"
   - Modèle : gpt-4 ou gpt-3.5-turbo
   - Système : Instructions pour l'agent (voir ci-dessous)
   - Utilisateur : Formatage de la demande initiale

2. Ajouter un nœud "Supabase"
   - Opération : Exécuter une fonction
   - Fonction : create_appointment_conversation
   - Paramètres :
     - p_conversation_id : `{{$json.conversation_id}}`
     - p_request_id : `{{$json.request_id}}`
     - p_metadata : Métadonnées de la demande

3. Ajouter un nœud "Respond to Webhook"
   - Données : 
     ```json
     {
       "message": "{{$node['OpenAI'].json.choices[0].message.content}}",
       "conversation_id": "{{$json.conversation_id}}"
     }
     ```

### 5. Traitement des messages

Pour le cas "send_message" :

1. Ajouter un nœud "Supabase"
   - Opération : Exécuter une fonction
   - Fonction : record_conversation_message
   - Paramètres :
     - p_conversation_id : `{{$json.conversation_id}}`
     - p_message_type : "user"
     - p_content : `{{$json.message}}`

2. Ajouter un nœud "Function"
   - Code : Logique pour déterminer si suffisamment d'informations ont été collectées

3. Ajouter un nœud "Switch"
   - Condition : `{{$node['Function'].json.readyToCreateAppointment}}`
   - Cas true : Créer un rendez-vous
   - Cas false : Continuer la conversation

4. Pour le cas "Continuer la conversation" :
   - Ajouter un nœud "OpenAI"
   - Ajouter un nœud "Supabase" pour enregistrer la réponse
   - Ajouter un nœud "Respond to Webhook"

5. Pour le cas "Créer un rendez-vous" :
   - Ajouter un nœud "Supabase" pour trouver des professionnels disponibles
   - Ajouter un nœud "Function" pour sélectionner le meilleur professionnel
   - Ajouter un nœud "Supabase" pour créer le rendez-vous
   - Ajouter un nœud "Respond to Webhook" avec les détails du rendez-vous

### 6. Annulation de la conversation

Pour le cas "cancel_conversation" :

1. Ajouter un nœud "Supabase"
   - Opération : Exécuter une fonction
   - Fonction : cancel_appointment_conversation
   - Paramètres :
     - p_conversation_id : `{{$json.conversation_id}}`

2. Ajouter un nœud "Respond to Webhook"
   - Données : 
     ```json
     {
       "message": "Conversation annulée",
       "success": true
     }
     ```

## Instructions pour l'agent AI

Voici un exemple d'instructions système pour l'agent AI :

```
Vous êtes un assistant spécialisé dans la prise de rendez-vous pour l'application Bomoko Mobile, une plateforme qui aide les victimes de violences à trouver de l'aide auprès de professionnels.

Votre rôle est de :
1. Comprendre le besoin de la personne
2. Poser des questions pertinentes pour clarifier sa situation
3. Déterminer quel type de professionnel serait le plus adapté (médecin, psychologue, avocat, etc.)
4. Aider à planifier un rendez-vous avec ce professionnel

Soyez empathique, patient et respectueux. Les personnes qui vous contactent peuvent être en situation de détresse.

Ne demandez jamais de détails explicites sur les violences subies. Si la personne les partage d'elle-même, accusez réception avec empathie mais ne creusez pas davantage.

Utilisez un langage simple et clair. Évitez le jargon technique.

Lorsque vous avez suffisamment d'informations, vous pourrez créer un rendez-vous en fournissant :
- Le type de professionnel requis
- La spécialité si nécessaire
- La date et l'heure préférées
- Des notes pour le professionnel (sans détails sensibles)

Commencez par vous présenter et demander en quoi vous pouvez aider.
```

## Sécurité et confidentialité

- Assurez-vous que toutes les communications sont chiffrées (HTTPS)
- Ne stockez pas de détails sensibles sur les violences subies
- Utilisez l'API key pour authentifier les requêtes
- Limitez les permissions de l'agent N8N dans la base de données

## Test et déploiement

1. Testez le workflow avec des demandes simulées
2. Vérifiez que les rendez-vous sont correctement créés
3. Testez les cas d'erreur et les annulations
4. Déployez le workflow en production
5. Configurez la surveillance et les alertes

## Maintenance

- Surveillez les logs pour détecter les erreurs
- Mettez à jour régulièrement les instructions de l'agent AI
- Ajustez les paramètres en fonction des retours utilisateurs
- Vérifiez régulièrement les performances du système
