import { useState, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../context/auth';

export type ForumCategory = {
  id: string;
  name: string;
  description: string | null;
  slug: string;
  icon: string;
  color: string;
  order_index: number;
  is_private: boolean;
};

export type ForumTopic = {
  id: string;
  title: string;
  content: string;
  created_at: string;
  updated_at: string | null;
  user_id: string;
  category_id: string;
  is_anonymous: boolean;
  is_pinned: boolean;
  is_locked: boolean;
  moderation_status: string | null;
  user: {
    first_name: string | null;
    last_name: string | null;
    avatar_url: string | null;
  } | null;
  category: {
    id: string;
    name: string;
    icon: string;
    color: string;
  } | null;
  _count: {
    reactions: number;
    replies: number;
  };
  has_reacted: boolean;
};

export type ForumReply = {
  id: string;
  content: string;
  created_at: string;
  updated_at: string | null;
  user_id: string;
  topic_id: string;
  parent_id: string | null;
  is_anonymous: boolean;
  user: {
    first_name: string | null;
    last_name: string | null;
    avatar_url: string | null;
  } | null;
  _count?: {
    reactions: number;
  };
  has_reacted?: boolean;
};

export const useForumData = () => {
  const { user } = useAuth();
  const [categories, setCategories] = useState<ForumCategory[]>([]);
  const [topics, setTopics] = useState<ForumTopic[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastFetch, setLastFetch] = useState(0);

  const fetchCategories = useCallback(async () => {
    try {
      const { data, error: categoriesError } = await supabase
        .from('forum_categories')
        .select(`
          id, name, description, slug, icon, color, order_index, is_private,
          topic_count: forum_topics(count)
        `)
        .order('order_index');

      if (categoriesError) throw categoriesError;

      const formattedData = data?.map(category => ({
        id: category.id,
        name: category.name,
        description: category.description,
        slug: category.slug,
        icon: category.icon,
        color: category.color,
        order_index: category.order_index,
        is_private: category.is_private,
        topic_count: typeof category.topic_count === 'object' && Array.isArray(category.topic_count) && category.topic_count[0]?.count 
          ? Number(category.topic_count[0].count) 
          : 0
      })) || [];

      setCategories(formattedData);
      return formattedData;
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError('Erreur lors du chargement des catégories');
      return [];
    }
  }, []);

  const fetchTopics = useCallback(async (categoryId?: string, page = 1, shouldSetLoading = true) => {
    const pageSize = 10;
    
    try {
      if (shouldSetLoading) {
        setLoading(true);
      }
      setError(null);
      
      // Éviter de recharger les données trop fréquemment
      const now = Date.now();
      if (now - lastFetch < 5000 && topics.length > 0 && page === 1) {
        return topics;
      }
      setLastFetch(now);

      // Requête modifiée pour VRAIMENT éviter toute jointure problématique
      let query = supabase
        .from('forum_topics')
        .select('id, title, content, created_at, updated_at, user_id, category_id, is_anonymous, is_pinned, is_locked, moderation_status')
        .order('is_pinned', { ascending: false })
        .order('created_at', { ascending: false });
      
      if (categoryId && categoryId !== 'undefined') {
        query = query.eq('category_id', categoryId);
      }
      
      // Pagination
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);
      
      const { data: topicsData, error: topicsError } = await query;

      if (topicsError) throw topicsError;

      // Initialiser les objets topic
      const formattedTopics: ForumTopic[] = (topicsData || []).map(topic => ({
        ...topic,
        has_reacted: false,
        _count: {
          reactions: 0,
          replies: 0
        },
        user: null,
        category: null
      }));

      // Récupérer les catégories pour les topics
      if (formattedTopics.length > 0) {
        const categoryIds = [...new Set(formattedTopics.map(topic => topic.category_id))];
        const { data: categoriesData } = await supabase
          .from('forum_categories')
          .select('id, name, icon, color')
          .in('id', categoryIds);
        
        if (categoriesData) {
          const categoryMap = new Map(categoriesData.map(c => [c.id, c]));
          formattedTopics.forEach(topic => {
            const categoryData = categoryMap.get(topic.category_id);
            if (categoryData) {
              topic.category = {
                id: categoryData.id,
                name: categoryData.name,
                icon: categoryData.icon,
                color: categoryData.color
              };
            }
          });
        }
      }

      // Récupérer les compteurs de réactions et réponses
      for (const topic of formattedTopics) {
        // Compter les réactions
        const { count: reactionsCount } = await supabase
          .from('forum_reactions')
          .select('*', { count: 'exact' })
          .eq('target_id', topic.id)
          .eq('target_type', 'topic');
        
        // Compter les réponses
        const { count: repliesCount } = await supabase
          .from('forum_replies')
          .select('*', { count: 'exact' })
          .eq('topic_id', topic.id);
        
        // Mettre à jour les compteurs
        topic._count.reactions = reactionsCount || 0;
        topic._count.replies = repliesCount || 0;
      }
      
      // Récupérer les réactions de l'utilisateur connecté
      if (user && formattedTopics.length > 0) {
        const topicIds = formattedTopics.map(topic => topic.id);
        const { data: userReactions } = await supabase
          .from('forum_reactions')
          .select('target_id')
          .eq('user_id', user.id)
          .eq('target_type', 'topic')
          .in('target_id', topicIds);

        if (userReactions) {
          const reactedTopicIds = new Set(userReactions.map(r => r.target_id));
          formattedTopics.forEach(topic => {
            topic.has_reacted = reactedTopicIds.has(topic.id);
          });
        }
      }

      // Récupérer les informations des utilisateurs
      if (formattedTopics.length > 0) {
        const userIds = formattedTopics
          .filter(topic => !topic.is_anonymous)
          .map(topic => topic.user_id);
        
        if (userIds.length > 0) {
          const { data: usersData } = await supabase
            .from('profiles')
            .select('id, first_name, last_name, avatar_url')
            .in('id', userIds);
          
          if (usersData) {
            const userMap = new Map(usersData.map(u => [u.id, u]));
            
            formattedTopics.forEach(topic => {
              if (!topic.is_anonymous) {
                topic.user = userMap.get(topic.user_id) || null;
              }
            });
          }
        }
      }

      // Mettre à jour l'état en fonction de la page
      if (page === 1) {
        setTopics(formattedTopics);
      } else {
        // Fusionner avec les topics existants, en évitant les doublons
        const existingIds = new Set(topics.map(t => t.id));
        const newTopics = [
          ...topics,
          ...formattedTopics.filter(t => !existingIds.has(t.id))
        ];
        setTopics(newTopics);
      }

      return formattedTopics;
    } catch (err) {
      console.error('Error fetching topics:', err);
      setError('Erreur lors du chargement des sujets');
      return [];
    } finally {
      if (shouldSetLoading) {
        setLoading(false);
      }
    }
  }, [user, topics, lastFetch]);

  const fetchTopicDetails = useCallback(async (topicId: string) => {
    try {
      setLoading(true);
      setError(null);

      // Récupérer les détails du sujet sans jointure problématique
      const { data: topicData, error: topicError } = await supabase
        .from('forum_topics')
        .select(`
          id, title, content, created_at, updated_at, 
          user_id, category_id, is_anonymous, is_pinned, is_locked, moderation_status
        `)
        .eq('id', topicId)
        .single();

      if (topicError) throw topicError;

      // Récupérer la catégorie séparément
      const { data: categoryData, error: categoryError } = await supabase
        .from('forum_categories')
        .select('id, name, icon, color')
        .eq('id', topicData.category_id)
        .single();

      if (categoryError) throw categoryError;

      // Compter les réactions pour le topic
      const { count: reactionsCount } = await supabase
        .from('forum_reactions')
        .select('*', { count: 'exact' })
        .eq('target_id', topicId)
        .eq('target_type', 'topic');
      
      // Récupérer les réponses
      const { data: repliesData, error: repliesError } = await supabase
        .from('forum_replies')
        .select('*')
        .eq('topic_id', topicId)
        .order('created_at', { ascending: true });

      if (repliesError) throw repliesError;

      // Assertion de type pour résoudre les erreurs ParserError
      const typedRepliesData = repliesData as any[];

      // Compter les réactions pour chaque réponse
      for (const reply of typedRepliesData) {
        const { count: replyReactionsCount } = await supabase
          .from('forum_reactions')
          .select('*', { count: 'exact' })
          .eq('target_id', reply.id)
          .eq('target_type', 'reply');
        
        reply.reactions_count = replyReactionsCount || 0;
      }

      // Récupérer les informations des utilisateurs
      // Pour le topic
      let topicUser = null;
      if (!topicData.is_anonymous) {
        const { data: userData } = await supabase
          .from('profiles')
          .select('id, first_name, last_name, avatar_url')
          .eq('id', topicData.user_id)
          .maybeSingle();
        
        topicUser = userData;
      }
      
      // Pour les réponses
      if (typedRepliesData.length > 0) {
        const userIds = typedRepliesData
          .filter(reply => !reply.is_anonymous)
          .map(reply => reply.user_id);
        
        if (userIds.length > 0) {
          const { data: usersData } = await supabase
            .from('profiles')
            .select('id, first_name, last_name, avatar_url')
            .in('id', [...new Set(userIds)]);
          
          if (usersData) {
            const userMap = new Map(usersData.map(u => [u.id, u]));
            
            typedRepliesData.forEach(reply => {
              if (!reply.is_anonymous) {
                reply.user = userMap.get(reply.user_id) || null;
              } else {
                reply.user = null;
              }
            });
          }
        }
      }

      // Vérifier si l'utilisateur a réagi au sujet ou aux réponses
      let hasReactedToTopic = false;
      let reactedReplyIds: string[] = [];
      
      if (user) {
        // Réaction au sujet
        const { data: topicReaction } = await supabase
          .from('forum_reactions')
          .select('id')
          .eq('target_id', topicId)
          .eq('user_id', user.id)
          .eq('target_type', 'topic')
          .maybeSingle();
        
        hasReactedToTopic = !!topicReaction;
        
        // Réactions aux réponses
        if (repliesData && repliesData.length > 0) {
          const replyIds = repliesData.map(reply => reply.id);
          
          const { data: replyReactions } = await supabase
            .from('forum_reactions')
            .select('target_id')
            .eq('user_id', user.id)
            .eq('target_type', 'reply')
            .in('target_id', replyIds);
          
          reactedReplyIds = replyReactions?.map(r => r.target_id) || [];
        }
      }

      // Formater les données
      const formattedTopic = {
        ...topicData,
        has_reacted: hasReactedToTopic,
        _count: {
          reactions: reactionsCount || 0,
          replies: typedRepliesData?.length || 0
        },
        user: topicUser,
        category: categoryData
      };

      const formattedReplies = typedRepliesData?.map(reply => ({
        ...reply,
        has_reacted: reactedReplyIds.includes(reply.id),
        _count: {
          reactions: reply.reactions_count || 0
        }
      })) || [];

      return {
        topic: formattedTopic as ForumTopic,
        replies: formattedReplies as ForumReply[]
      };
    } catch (err) {
      console.error('Error fetching topic details:', err);
      throw new Error('Erreur lors du chargement des détails');
    } finally {
      setLoading(false);
    }
  }, [user]);

  const addReaction = useCallback(async (targetId: string, reactionType: string, userId: string) => {
    try {
      const { error } = await supabase
        .from('forum_reactions')
        .insert([
          {
            target_id: targetId,
            target_type: 'topic', // Par défaut, c'est un topic
            reaction_type: reactionType,
            user_id: userId
          }
        ]);

      if (error) throw error;

      // Mettre à jour localement pour éviter de recharger
      setTopics(prevTopics => prevTopics.map(topic => {
        if (topic.id === targetId) {
          return {
            ...topic,
            has_reacted: true,
            _count: {
              ...topic._count,
              reactions: (topic._count?.reactions || 0) + 1
            }
          };
        }
        return topic;
      }));
    } catch (err) {
      console.error('Error adding reaction:', err);
      throw err;
    }
  }, []);

  const removeReaction = useCallback(async (targetId: string, userId: string) => {
    try {
      const { error } = await supabase
        .from('forum_reactions')
        .delete()
        .match({ 
          target_id: targetId,
          user_id: userId
        });

      if (error) throw error;

      // Mettre à jour localement pour éviter de recharger
      setTopics(prevTopics => prevTopics.map(topic => {
        if (topic.id === targetId) {
          return {
            ...topic,
            has_reacted: false,
            _count: {
              ...topic._count,
              reactions: Math.max((topic._count?.reactions || 0) - 1, 0)
            }
          };
        }
        return topic;
      }));
    } catch (err) {
      console.error('Error removing reaction:', err);
      throw err;
    }
  }, []);

  const createTopic = useCallback(async (topicData: {
    title: string;
    content: string;
    category_id: string;
    user_id: string;
    is_anonymous: boolean;
  }) => {
    try {
      const { data, error } = await supabase
        .from('forum_topics')
        .insert([topicData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (err) {
      console.error('Error creating topic:', err);
      throw err;
    }
  }, []);

  const createReply = useCallback(async (replyData: {
    topic_id: string;
    content: string;
    user_id: string;
    parent_id?: string;
    is_anonymous?: boolean;
  }) => {
    try {
      const { data, error } = await supabase
        .from('forum_replies')
        .insert([replyData])
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (err) {
      console.error('Error creating reply:', err);
      throw err;
    }
  }, []);

  useCallback(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    categories,
    topics,
    loading,
    error,
    fetchCategories,
    fetchTopics,
    fetchTopicDetails,
    addReaction,
    removeReaction,
    createTopic,
    createReply
  };
};
