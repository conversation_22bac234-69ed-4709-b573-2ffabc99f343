import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Platform,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { supabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

type TopicDetails = {
  id: string;
  title: string;
  content: string;
  created_at: string;
  user: {
    first_name: string;
    last_name: string;
  } | null;
  is_anonymous: boolean;
  category: {
    id: string;
    name: string;
  };
  replies: Array<{
    id: string;
    content: string;
    created_at: string;
    user: {
      first_name: string;
      last_name: string;
    } | null;
    is_anonymous: boolean;
  }>;
  reactions: Array<{
    reaction_type: string;
    count: number;
    user_has_reacted: boolean;
  }>;
};

export default function TopicScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const params = useLocalSearchParams();
  const topicId = params.id as string;

  const [topic, setTopic] = useState<TopicDetails | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTopic();
  }, [topicId]);

  const loadTopic = async () => {
    try {
      setLoading(true);
      setError(null);

      // Récupérer le topic avec info de base (sans jointure)
      const { data: topicData, error: topicError } = await supabase
        .from('forum_topics')
        .select('id, title, content, created_at, user_id, is_anonymous, category_id, is_pinned, is_locked, moderation_status')
        .eq('id', topicId)
        .single();

      if (topicError) throw topicError;

      // Récupérer la catégorie séparément
      const { data: categoryData, error: categoryError } = await supabase
        .from('forum_categories')
        .select('id, name')
        .eq('id', topicData.category_id)
        .single();

      if (categoryError) throw categoryError;

      // Récupérer les réponses
      const { data: repliesData, error: repliesError } = await supabase
        .from('forum_replies')
        .select('id, content, created_at, is_anonymous, user_id')
        .eq('topic_id', topicId)
        .order('created_at', { ascending: true });

      if (repliesError) throw repliesError;

      // Récupérer les réactions
      const { data: reactionsData, error: reactionsError } = await supabase
        .from('forum_reactions')
        .select('reaction_type')
        .eq('target_id', topicId)
        .eq('target_type', 'topic');

      if (reactionsError) throw reactionsError;

      // Récupérer les réactions de l'utilisateur
      const { data: userReactions, error: userReactionsError } = await supabase
        .from('forum_reactions')
        .select('reaction_type')
        .eq('target_id', topicId)
        .eq('target_type', 'topic')
        .eq('user_id', user?.id);

      if (userReactionsError) throw userReactionsError;

      // Compter les réactions par type
      const reactionCounts: Record<string, number> = {};
      reactionsData.forEach(reaction => {
        const type = reaction.reaction_type;
        reactionCounts[type] = (reactionCounts[type] || 0) + 1;
      });

      // Formater les réactions pour l'affichage
      const formattedReactions = Object.keys(reactionCounts).map(type => ({
        reaction_type: type,
        count: reactionCounts[type],
        user_has_reacted: userReactions?.some((ur: {reaction_type: string}) => ur.reaction_type === type)
      }));

      // Collecter tous les IDs d'utilisateurs
      const userIds = [topicData.user_id];
      repliesData.forEach((reply) => {
        if (reply.user_id && !userIds.includes(reply.user_id)) {
          userIds.push(reply.user_id);
        }
      });

      // Filtrer les IDs null ou undefined
      const validUserIds = userIds.filter(id => id);

      // Récupérer les profils
      let profilesData: Array<{id: string, first_name: string, last_name: string}> = [];
      if (validUserIds.length > 0) {
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, first_name, last_name')
          .in('id', validUserIds);

        if (profilesError) throw profilesError;
        profilesData = profiles || [];
      }

      // Créer un dictionnaire pour un accès rapide aux profils
      const profilesById: Record<string, {id: string, first_name: string, last_name: string}> = {};
      profilesData.forEach(profile => {
        profilesById[profile.id] = profile;
      });

      // Formater les réponses avec les utilisateurs
      const repliesWithUsers = repliesData.map(reply => ({
        ...reply,
        user: reply.user_id && profilesById[reply.user_id]
          ? {
              first_name: profilesById[reply.user_id].first_name,
              last_name: profilesById[reply.user_id].last_name
            }
          : null
      }));

      // Assembler les données complètes du topic
      const topicWithDetails = {
        ...topicData,
        user: topicData.user_id && profilesById[topicData.user_id]
          ? {
              first_name: profilesById[topicData.user_id].first_name,
              last_name: profilesById[topicData.user_id].last_name
            }
          : null,
        category: categoryData,
        replies: repliesWithUsers,
        reactions: formattedReactions
      };

      setTopic(topicWithDetails);
    } catch (err) {
      console.error('Error loading topic:', err);
      setError('Erreur lors du chargement de la discussion');
    } finally {
      setLoading(false);
    }
  };

  const handleReply = async () => {
    if (!replyContent.trim()) return;

    try {
      setSending(true);
      setError(null);

      const { data: reply, error: replyError } = await supabase.rpc(
        'create_forum_reply',
        {
          p_topic_id: topicId,
          p_content: replyContent.trim(),
          p_is_anonymous: isAnonymous
        }
      );

      if (replyError) throw replyError;

      setReplyContent('');
      setIsAnonymous(false);
      loadTopic();
    } catch (err) {
      console.error('Error sending reply:', err);
      setError('Erreur lors de l\'envoi de la réponse');
    } finally {
      setSending(false);
    }
  };

  const handleReaction = async (reactionType: string) => {
    try {
      const { error: reactionError } = await supabase
        .from('forum_reactions')
        .upsert(
          {
            user_id: user?.id,
            target_type: 'topic',
            target_id: topicId,
            reaction_type: reactionType,
          },
          { onConflict: 'user_id, target_type, target_id, reaction_type' }
        );

      if (reactionError) throw reactionError;

      loadTopic();
    } catch (err) {
      console.error('Error reacting to topic:', err);
      Alert.alert('Erreur', 'Impossible d\'ajouter la réaction');
    }
  };

  const handleReport = () => {
    Alert.alert(
      'Signaler',
      'Voulez-vous signaler ce contenu ?',
      [
        {
          text: 'Annuler',
          style: 'cancel',
        },
        {
          text: 'Signaler',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error: reportError } = await supabase
                .from('forum_reports')
                .insert({
                  reporter_id: user?.id,
                  target_type: 'topic',
                  target_id: topicId,
                  reason: 'Contenu inapproprié',
                });

              if (reportError) throw reportError;

              Alert.alert(
                'Merci',
                'Votre signalement a été pris en compte'
              );
            } catch (err) {
              console.error('Error reporting topic:', err);
              Alert.alert(
                'Erreur',
                'Impossible de signaler le contenu'
              );
            }
          },
        },
      ]
    );
  };

  const formatDate = (date: string) => {
    return format(new Date(date), 'PPP à HH:mm');
  };

  if (error) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={[styles.error, { color: theme.colors.error }]}>
          {error}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
          onPress={loadTopic}>
          <Text style={styles.retryButtonText}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (loading || !topic) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={{ color: theme.colors.text }}>
          Chargement de la discussion...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}>
          <Ionicons
            name="arrow-back"
            size={24}
            color={theme.colors.text}
          />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={[styles.categoryName, { color: theme.colors.gray[600] }]}>
            {topic.category.name}
          </Text>
          <Text
            style={[styles.title, { color: theme.colors.text }]}
            numberOfLines={2}>
            {topic.title}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.moreButton}
          onPress={handleReport}>
          <Ionicons
            name="flag-outline"
            size={24}
            color={theme.colors.error}
          />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <View
          style={[
            styles.topicCard,
            { backgroundColor: theme.colors.surface },
          ]}>
          <View style={styles.authorInfo}>
            <Ionicons
              name="person-outline"
              size={20}
              color={theme.colors.gray[500]}
            />
            <Text style={[styles.authorName, { color: theme.colors.gray[600] }]}>
              {topic.is_anonymous
                ? 'Anonyme'
                : topic.user
                ? `${topic.user.first_name} ${topic.user.last_name}`
                : 'Utilisateur supprimé'}
            </Text>
            <Text style={[styles.date, { color: theme.colors.gray[500] }]}>
              {formatDate(topic.created_at)}
            </Text>
          </View>

          <Text style={[styles.topicContent, { color: theme.colors.text }]}>
            {topic.content}
          </Text>

          <View style={styles.reactions}>
            {['support', 'hug', 'thanks'].map((type) => {
              const reaction = topic.reactions.find(
                (r) => r.reaction_type === type
              );
              return (
                <TouchableOpacity
                  key={type}
                  style={[
                    styles.reactionButton,
                    {
                      backgroundColor: reaction?.user_has_reacted
                        ? theme.colors.primary + '20'
                        : theme.colors.gray[200],
                    },
                  ]}
                  onPress={() => handleReaction(type)}>
                  <Ionicons
                    name={
                      type === 'support'
                        ? 'heart'
                        : type === 'hug'
                        ? 'people'
                        : 'thumbs-up'
                    }
                    size={16}
                    color={
                      reaction?.user_has_reacted
                        ? theme.colors.primary
                        : theme.colors.gray[600]
                    }
                  />
                  <Text
                    style={[
                      styles.reactionCount,
                      {
                        color: reaction?.user_has_reacted
                          ? theme.colors.primary
                          : theme.colors.gray[600],
                      },
                    ]}>
                    {reaction?.count || 0}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        </View>

        {topic.replies.length > 0 && (
          <View style={styles.repliesSection}>
            <Text style={[styles.repliesTitle, { color: theme.colors.text }]}>
              Réponses ({topic.replies.length})
            </Text>
            {topic.replies.map((reply) => (
              <View
                key={reply.id}
                style={[
                  styles.replyCard,
                  { backgroundColor: theme.colors.surface },
                ]}>
                <View style={styles.authorInfo}>
                  <Ionicons
                    name="person-outline"
                    size={16}
                    color={theme.colors.gray[500]}
                  />
                  <Text
                    style={[styles.authorName, { color: theme.colors.gray[600] }]}>
                    {reply.is_anonymous
                      ? 'Anonyme'
                      : reply.user
                      ? `${reply.user.first_name} ${reply.user.last_name}`
                      : 'Utilisateur supprimé'}
                  </Text>
                  <Text style={[styles.date, { color: theme.colors.gray[500] }]}>
                    {formatDate(reply.created_at)}
                  </Text>
                </View>

                <Text style={[styles.replyContent, { color: theme.colors.text }]}>
                  {reply.content}
                </Text>
              </View>
            ))}
          </View>
        )}
      </ScrollView>

      <View
        style={[styles.replyInput, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity
          style={[
            styles.anonymousToggle,
            {
              backgroundColor: isAnonymous
                ? theme.colors.primary + '20'
                : theme.colors.gray[200],
            },
          ]}
          onPress={() => setIsAnonymous(!isAnonymous)}>
          <Ionicons
            name={isAnonymous ? 'eye-off' : 'eye-outline'}
            size={20}
            color={isAnonymous ? theme.colors.primary : theme.colors.gray[600]}
          />
        </TouchableOpacity>

        <TextInput
          style={[styles.input, { color: theme.colors.text }]}
          placeholder="Votre réponse..."
          placeholderTextColor={theme.colors.gray[400]}
          value={replyContent}
          onChangeText={setReplyContent}
          multiline
          maxLength={1000}
          editable={!sending}
        />

        <TouchableOpacity
          style={[
            styles.sendButton,
            {
              backgroundColor: theme.colors.primary,
              opacity: sending || !replyContent.trim() ? 0.5 : 1,
            },
          ]}
          onPress={handleReply}
          disabled={sending || !replyContent.trim()}>
          <Ionicons name="send" size={20} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
  },
  backButton: {
    marginRight: 15,
  },
  headerContent: {
    flex: 1,
  },
  categoryName: {
    fontSize: 14,
    marginBottom: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  moreButton: {
    marginLeft: 15,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  topicCard: {
    padding: 15,
    borderRadius: 12,
    marginBottom: 20,
  },
  authorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    gap: 8,
  },
  authorName: {
    fontSize: 14,
    flex: 1,
  },
  date: {
    fontSize: 12,
  },
  topicContent: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 15,
  },
  reactions: {
    flexDirection: 'row',
    gap: 10,
  },
  reactionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    paddingHorizontal: 12,
    borderRadius: 16,
    gap: 6,
  },
  reactionCount: {
    fontSize: 14,
    fontWeight: '600',
  },
  repliesSection: {
    marginTop: 20,
  },
  repliesTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  replyCard: {
    padding: 15,
    borderRadius: 12,
    marginBottom: 10,
  },
  replyContent: {
    fontSize: 14,
    lineHeight: 20,
  },
  replyInput: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 10,
  },
  anonymousToggle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    flex: 1,
    maxHeight: 100,
    padding: 10,
    fontSize: 16,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  error: {
    marginBottom: 15,
    textAlign: 'center',
  },
  retryButton: {
    padding: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});