/*
  # Amélioration de la configuration des webhooks

  1. Nouvelle Table
    - `webhook_configurations` pour stocker les configurations des webhooks
    - Meilleure gestion des URLs et des paramètres
    
  2. Sécurité
    - RLS activé sur la nouvelle table
    - Politiques de sécurité appropriées
    
  3. Fonctions
    - Fonction améliorée pour la vérification du statut
*/

-- Créer la nouvelle table de configuration des webhooks
CREATE TABLE IF NOT EXISTS webhook_configurations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    url VARCHAR(500) NOT NULL,
    backup_url VARCHAR(500),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    max_retries INTEGER DEFAULT 3,
    retry_delay_base INTEGER DEFAULT 2000,
    health_check_interval INTEGER DEFAULT 300,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    last_check_timestamp TIMESTAMPTZ,
    last_error_timestamp TIMESTAMPTZ,
    error_count INTEGER DEFAULT 0,
    error_message TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    CONSTRAINT valid_urls CHECK (
        url ~ '^https://' AND 
        (backup_url IS NULL OR backup_url ~ '^https://')
    )
);

-- Activer RLS
ALTER TABLE webhook_configurations ENABLE ROW LEVEL SECURITY;

-- Créer les politiques de sécurité
CREATE POLICY "Lecture autorisée pour les utilisateurs authentifiés"
    ON webhook_configurations
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Mise à jour autorisée pour les utilisateurs authentifiés"
    ON webhook_configurations
    FOR UPDATE
    TO authenticated
    USING (is_active = true);

-- Fonction pour vérifier le statut du webhook
CREATE OR REPLACE FUNCTION check_webhook_configuration(webhook_name TEXT)
RETURNS TABLE (
    is_available BOOLEAN,
    current_url VARCHAR(500),
    error_details TEXT
) AS $$
DECLARE
    config_record webhook_configurations%ROWTYPE;
BEGIN
    -- Récupérer la configuration
    SELECT * INTO config_record
    FROM webhook_configurations
    WHERE name = webhook_name
    AND is_active = true;

    -- Vérifier si la configuration existe
    IF config_record IS NULL THEN
        RETURN QUERY SELECT 
            false::BOOLEAN, 
            NULL::VARCHAR(500), 
            'Configuration non disponible'::TEXT;
        RETURN;
    END IF;

    -- Retourner le statut
    RETURN QUERY
    SELECT 
        (config_record.is_active AND config_record.error_count < config_record.max_retries)::BOOLEAN,
        CASE 
            WHEN config_record.error_count >= config_record.max_retries AND config_record.backup_url IS NOT NULL 
            THEN config_record.backup_url
            ELSE config_record.url
        END,
        COALESCE(config_record.error_message, '')::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Migrer les données existantes
INSERT INTO webhook_configurations (
    name,
    url,
    backup_url,
    is_active,
    max_retries,
    retry_delay_base,
    health_check_interval
)
SELECT
    'n8n_chatbot_agent',
    'https://n8n-dw1u.onrender.com/webhook-test/chat',
    'https://backup-n8n-dw1u.onrender.com/webhook-test/chat',
    true,
    3,
    2000,
    300
WHERE NOT EXISTS (
    SELECT 1 FROM webhook_configurations WHERE name = 'n8n_chatbot_agent'
);