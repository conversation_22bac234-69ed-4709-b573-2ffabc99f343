-- Create a function to allow <PERSON><PERSON><PERSON> to create appointments
CREATE OR REPLACE FUNCTION create_appointment_by_n8n(
    p_user_id uuid,
    p_professional_id uuid,
    p_date_time timestamptz,
    p_type text,
    p_status text DEFAULT 'confirmed',
    p_notes text DEFAULT NULL,
    p_duration integer DEFAULT 30,
    p_metadata jsonb DEFAULT '{}'::jsonb,
    p_request_id text DEFAULT NULL,
    p_api_key text
)
RETURNS uuid AS $$
DECLARE
    v_appointment_id uuid;
    v_valid_key boolean;
BEGIN
    -- Verify the API key
    SELECT EXISTS (
        SELECT 1 FROM webhook_configurations 
        WHERE name = 'RDV AI WEBHOOK URL' 
        AND (api_key = p_api_key OR backup_api_key = p_api_key)
    ) INTO v_valid_key;
    
    IF NOT v_valid_key THEN
        RAISE EXCEPTION 'Invalid API key';
    END IF;
    
    -- Insert the appointment
    INSERT INTO appointments (
        user_id,
        professional_id,
        date_time,
        type,
        status,
        notes,
        duration,
        metadata
    ) VALUES (
        p_user_id,
        p_professional_id,
        p_date_time,
        p_type,
        p_status,
        p_notes,
        p_duration,
        p_metadata
    )
    RETURNING id INTO v_appointment_id;
    
    -- If a request_id is provided, update the conversation status
    IF p_request_id IS NOT NULL THEN
        UPDATE appointment_conversations
        SET 
            appointment_id = v_appointment_id,
            status = 'completed',
            updated_at = now()
        WHERE request_id = p_request_id
        AND user_id = p_user_id;
    END IF;
    
    -- Create a notification for the user
    INSERT INTO notifications (
        user_id,
        type,
        title,
        message,
        metadata
    ) VALUES (
        p_user_id,
        'appointment_created',
        'Nouveau rendez-vous',
        'Un rendez-vous a été créé pour vous',
        jsonb_build_object(
            'appointment_id', v_appointment_id,
            'date_time', p_date_time,
            'professional_id', p_professional_id
        )
    );
    
    RETURN v_appointment_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to find available professionals based on criteria
CREATE OR REPLACE FUNCTION find_available_professionals(
    p_speciality text DEFAULT NULL,
    p_type text DEFAULT NULL,
    p_date_from timestamptz DEFAULT now(),
    p_date_to timestamptz DEFAULT (now() + interval '7 days'),
    p_api_key text
)
RETURNS TABLE (
    id uuid,
    type text,
    speciality text,
    user_id uuid,
    first_name text,
    last_name text,
    available_slots json[]
) AS $$
DECLARE
    v_valid_key boolean;
BEGIN
    -- Verify the API key
    SELECT EXISTS (
        SELECT 1 FROM webhook_configurations 
        WHERE name = 'RDV AI WEBHOOK URL' 
        AND (api_key = p_api_key OR backup_api_key = p_api_key)
    ) INTO v_valid_key;
    
    IF NOT v_valid_key THEN
        RAISE EXCEPTION 'Invalid API key';
    END IF;
    
    RETURN QUERY
    WITH professional_availability AS (
        SELECT 
            p.id,
            p.type,
            p.speciality,
            p.user_id,
            u.first_name,
            u.last_name,
            -- Generate available slots (simplified for this example)
            array_agg(
                json_build_object(
                    'date', date_trunc('day', generate_series),
                    'time', to_char(generate_series, 'HH24:MI'),
                    'available', NOT EXISTS (
                        SELECT 1 FROM appointments a
                        WHERE a.professional_id = p.id
                        AND date_trunc('hour', a.date_time) = date_trunc('hour', generate_series)
                    )
                )
            ) AS available_slots
        FROM professionals p
        JOIN profiles u ON p.user_id = u.id
        CROSS JOIN generate_series(p_date_from, p_date_to, interval '1 hour') 
        WHERE 
            (p_speciality IS NULL OR p.speciality = p_speciality)
            AND (p_type IS NULL OR p.type = p_type)
            -- Only include business hours (9 AM to 5 PM)
            AND extract(hour from generate_series) BETWEEN 9 AND 17
            -- Only include weekdays (Monday to Friday)
            AND extract(isodow from generate_series) BETWEEN 1 AND 5
        GROUP BY p.id, p.type, p.speciality, p.user_id, u.first_name, u.last_name
    )
    SELECT * FROM professional_availability;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add API key columns to webhook_configurations if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'webhook_configurations' 
        AND column_name = 'api_key'
    ) THEN
        ALTER TABLE webhook_configurations ADD COLUMN api_key text;
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'webhook_configurations' 
        AND column_name = 'backup_api_key'
    ) THEN
        ALTER TABLE webhook_configurations ADD COLUMN backup_api_key text;
    END IF;
END $$;
