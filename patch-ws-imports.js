// Script to patch ws imports in node_modules
const fs = require('fs');
const path = require('path');

console.log('Patching ws imports in node_modules...');

// Function to recursively find and patch files
function patchDirectory(dir) {
  if (!fs.existsSync(dir)) {
    console.log(`Directory ${dir} does not exist, skipping...`);
    return;
  }

  try {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        // Skip node_modules subdirectories to avoid infinite recursion
        if (file !== 'node_modules') {
          patchDirectory(filePath);
        }
      } else if (file.endsWith('.js') || file.endsWith('.ts')) {
        patchFile(filePath);
      }
    }
  } catch (error) {
    console.error(`Error processing directory ${dir}:`, error.message);
  }
}

// Function to patch individual files
function patchFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Replace ws imports with polyfill
    const wsImportPatterns = [
      /require\(['"]ws['"]\)/g,
      /require\(['"]ws\/lib\/stream['"]\)/g,
      /require\(['"]ws\/lib\/websocket['"]\)/g,
      /import.*from\s+['"]ws['"]/g,
      /import.*from\s+['"]ws\/lib\/stream['"]/g,
      /import.*from\s+['"]ws\/lib\/websocket['"]/g,
    ];
    
    for (const pattern of wsImportPatterns) {
      if (pattern.test(content)) {
        console.log(`Patching ws import in: ${filePath}`);
        content = content.replace(pattern, `require('${path.resolve(__dirname, 'ws-polyfill/index.js').replace(/\\/g, '/')}')`);
        modified = true;
      }
    }
    
    // Replace stream imports from ws files
    if (filePath.includes('node_modules/ws/') || filePath.includes('node_modules\\ws\\')) {
      const streamPattern = /require\(['"]stream['"]\)/g;
      if (streamPattern.test(content)) {
        console.log(`Patching stream import in ws file: ${filePath}`);
        content = content.replace(streamPattern, `require('${path.resolve(__dirname, 'ws-polyfill/index.js').replace(/\\/g, '/')}')`);
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`Successfully patched: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error patching file ${filePath}:`, error.message);
  }
}

// Patch specific directories
const directoriesToPatch = [
  'node_modules/@supabase/realtime-js',
  'node_modules/@react-native-community/cli-server-api',
  'node_modules/@expo/cli',
  'node_modules/metro',
  'node_modules/react-devtools-core',
  'node_modules/ws'
];

for (const dir of directoriesToPatch) {
  console.log(`\nPatching directory: ${dir}`);
  patchDirectory(dir);
}

console.log('\nPatching completed!');
