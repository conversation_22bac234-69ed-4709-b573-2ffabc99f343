// Test script to verify ws imports are properly polyfilled
console.log('Testing ws import...');

try {
  const ws = require('ws');
  console.log('ws imported successfully:', typeof ws);
  console.log('ws.WebSocket:', typeof ws.WebSocket);
  console.log('ws.CONNECTING:', ws.CONNECTING);
} catch (error) {
  console.error('Error importing ws:', error.message);
}

try {
  const wsStream = require('ws/lib/stream');
  console.log('ws/lib/stream imported successfully:', typeof wsStream);
} catch (error) {
  console.error('Error importing ws/lib/stream:', error.message);
}

console.log('Test completed.');
