import React, { useState, useRef } from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet, Alert, ActivityIndicator, Platform } from 'react-native';
import { useProfileStore } from './store';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import { supabase } from '../../../../lib/supabase';
import * as FileSystem from 'expo-file-system';
import { decode } from 'base64-arraybuffer';
import { useAuth } from '../../../../context/auth';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';

export function ProfilePictureStep() {
  const { profile, updateProfile, setCurrentStep } = useProfileStore();
  const { user } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [uploadComplete, setUploadComplete] = useState(false);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const handlePrevious = () => {
    setCurrentStep(2);
  };

  // Fonction pour télécharger l'image de profil vers Supabase Storage
  const uploadProfilePicture = async (uri: string): Promise<string | null> => {
    try {
      // Lire le fichier en base64
      const fileBase64 = await FileSystem.readAsStringAsync(uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Générer un nom de fichier unique
      const fileExt = uri.split('.').pop();
      const fileName = `${uuidv4()}.${fileExt}`;
      const filePath = `${user?.id}/${fileName}`;

      // Convertir la base64 en ArrayBuffer pour le téléchargement
      const fileBuffer = decode(fileBase64);

      // Télécharger le fichier vers Supabase Storage
      const { data, error } = await supabase.storage
        .from('avatars')
        .upload(filePath, fileBuffer, {
          contentType: `image/${fileExt}`,
        });

      if (error) {
        console.error("Supabase upload error:", error);
        throw new Error(`Erreur lors du téléchargement: ${error.message}`);
      }

      // Récupérer l'URL publique du fichier téléchargé
      const { data: urlData } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      return urlData.publicUrl;
    } catch (err) {
      console.error('Erreur lors du téléchargement de l\'image:', err);
      return null;
    }
  };

  const saveProfileToDatabase = async () => {
    try {
      let avatarUrl = null;

      // Si une photo de profil a été sélectionnée, la télécharger
      if (profile.profilePicture) {
        avatarUrl = await uploadProfilePicture(profile.profilePicture);
      }

      // Préparer les données du profil à enregistrer
      const profileData = {
        id: user?.id,
        first_name: profile.firstName,
        last_name: profile.lastName,
        phone: profile.phoneNumber,
        date_of_birth: profile.dateOfBirth,
        gender_identity: profile.gender,
        avatar_url: avatarUrl,
        risk_level: profile.riskLevel,
        risk_assessment: {
          evaluation: profile.aiEvaluation,
          responses: profile.aiResponses
        },
        profile_completed: true,
        updated_at: new Date()
      };

      // Enregistrer dans la base de données
      const { error } = await supabase
        .from('profiles')
        .upsert(profileData, { onConflict: 'id' });

      if (error) {
        throw new Error(`Erreur lors de l'enregistrement: ${error.message}`);
      }

      return true;
    } catch (err) {
      console.error('Erreur lors de la sauvegarde du profil:', err);
      return false;
    }
  };

  const handleFinish = async () => {
    setSubmitting(true);

    try {
      // Enregistrer les données dans la base de données
      const success = await saveProfileToDatabase();

      if (!success) {
        throw new Error("Échec de l'enregistrement des données du profil");
      }

      // Afficher le message de succès
      Alert.alert(
        "Profil complété",
        "Votre profil a été créé avec succès. Vous allez être redirigé vers le tableau de bord.",
        [
          {
            text: "OK",
            onPress: () => {
              // Rediriger vers le tableau de bord
              router.replace("/(app)");
            }
          }
        ]
      );
    } catch (err) {
      console.error("Erreur lors de la finalisation du profil:", err);
      Alert.alert(
        "Erreur",
        "Une erreur est survenue lors de la finalisation de votre profil. Veuillez réessayer.",
        [{ text: "OK" }]
      );
    } finally {
      setSubmitting(false);
    }
  };

  const pickImage = async () => {
    try {
      setLoading(true);
      setError(null);

      // Demander la permission d'accéder à la galerie
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        setError('Nous avons besoin de votre permission pour accéder à vos photos.');
        return;
      }

      // Ouvrir le sélecteur d'images
      let result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedImage = result.assets[0];

        // Vérifier la taille de l'image
        const fileInfo = await fetch(selectedImage.uri).then(r => r.blob());

        if (fileInfo.size > 5 * 1024 * 1024) {
          setError('La taille de l\'image ne doit pas dépasser 5 MB.');
          return;
        }

        updateProfile({ profilePicture: selectedImage.uri });
        setUploadComplete(true);
      }
    } catch (err) {
      setError('Une erreur est survenue lors de la sélection de l\'image.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const removeImage = () => {
    updateProfile({ profilePicture: null });
    setUploadComplete(false);
  };

  return (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <Text style={styles.cardTitle}>Photo de profil</Text>
        <Text style={styles.cardDescription}>
          Ajoutez une photo de profil pour personnaliser votre compte
        </Text>
      </View>

      <View style={styles.cardContent}>
        {error ? (
          <View style={styles.alertError}>
            <Ionicons name="warning" size={16} color="#ff3b30" />
            <Text style={styles.alertText}>{error}</Text>
          </View>
        ) : null}

        {profile.profilePicture ? (
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: profile.profilePicture }}
              style={styles.profileImage}
            />
            <TouchableOpacity
              style={styles.removeButton}
              onPress={removeImage}
            >
              <Ionicons name="close" size={16} color="#fff" />
            </TouchableOpacity>

            {uploadComplete && (
              <View style={styles.successBadge}>
                <Ionicons name="checkmark-circle" size={24} color="#fff" />
              </View>
            )}
          </View>
        ) : (
          <TouchableOpacity
            style={styles.uploadContainer}
            onPress={pickImage}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="large" color="#0066ff" />
            ) : (
              <>
                <Ionicons name="image-outline" size={40} color="#999" style={styles.uploadIcon} />
                <Text style={styles.uploadTitle}>Sélectionner une image</Text>
                <Text style={styles.uploadDescription}>
                  Formats acceptés : JPEG, PNG. Taille maximale : 5 MB
                </Text>
              </>
            )}
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.cardFooter}>
        <TouchableOpacity
          style={styles.secondaryButton}
          onPress={handlePrevious}
          disabled={submitting}
        >
          <Text style={styles.secondaryButtonText}>Précédent</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.primaryButton,
            (!profile.profilePicture || submitting) && styles.buttonDisabled
          ]}
          onPress={handleFinish}
          disabled={!profile.profilePicture || submitting}
        >
          {submitting ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.primaryButtonText}>Terminer</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    margin: 8,
    overflow: 'hidden'
  },
  cardHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: '#fafafa'
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
    marginBottom: 4
  },
  cardDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18
  },
  cardContent: {
    padding: 16,
    alignItems: 'center'
  },
  cardFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  alertError: {
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
    padding: 12,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    width: '100%'
  },
  alertText: {
    color: '#ff3b30',
    fontSize: 14,
    marginLeft: 8,
    flex: 1
  },
  imageContainer: {
    alignItems: 'center',
    position: 'relative',
    marginVertical: 24
  },
  profileImage: {
    width: 180,
    height: 180,
    borderRadius: 90,
    borderWidth: 4,
    borderColor: '#0066ff'
  },
  removeButton: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#ff3b30',
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2
  },
  successBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#34c759',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2
  },
  uploadContainer: {
    borderWidth: 2,
    borderColor: '#ddd',
    borderStyle: 'dashed',
    borderRadius: 16,
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 16,
    width: '100%'
  },
  uploadIcon: {
    marginBottom: 16
  },
  uploadTitle: {
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333'
  },
  uploadDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center'
  },
  primaryButton: {
    backgroundColor: '#0066ff',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 120
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600'
  },
  secondaryButton: {
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    paddingVertical: 12,
    paddingHorizontal: 20
  },
  secondaryButtonText: {
    color: '#666',
    fontSize: 16
  },
  buttonDisabled: {
    opacity: 0.5
  }
});

// Export par défaut pour Expo Router
export default ProfilePictureStep;