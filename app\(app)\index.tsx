import React, { useEffect } from 'react';
import { Redirect, useRootNavigationState } from 'expo-router';
import { 
  View, 
  Text, 
  ActivityIndicator, 
  StyleSheet, 
  Image, 
  Animated, 
  Dimensions 
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as SplashScreen from 'expo-splash-screen';
import { Asset } from 'expo-asset';

const { width, height } = Dimensions.get('window');

// Précharger les ressources importantes
const preloadAssets = async () => {
  try {
    // Précharger les images
    await Asset.loadAsync([require('../../assets/images/profile.png')]);
    return true;
  } catch (e) {
    console.warn('Erreur lors du préchargement des ressources:', e);
    return false;
  }
};

// Ce fichier sert de route par défaut pour le groupe (app)
// Il redirige vers la page d'accueil dans le groupe (tabs)
export default function AppIndex() {
  // Animation pour le logo
  const logoOpacity = new Animated.Value(0);
  const logoScale = new Animated.Value(0.8);

  // Lancer l'animation au chargement et précharger les ressources
  useEffect(() => {
    // Garder l'écran de démarrage visible pendant le préchargement
    SplashScreen.preventAutoHideAsync().catch(() => {});
    
    // Précharger les ressources puis lancer les animations
    preloadAssets().then(() => {
      // Masquer l'écran de démarrage
      SplashScreen.hideAsync().catch(() => {});
      
      // Démarrer les animations
      Animated.parallel([
        Animated.timing(logoOpacity, {
          toValue: 1,
          duration: 600, // Réduire légèrement la durée pour accélérer
          useNativeDriver: true,
        }),
        Animated.spring(logoScale, {
          toValue: 1,
          friction: 8,
          tension: 50, // Augmenter la tension pour une animation plus rapide
          useNativeDriver: true,
        }),
      ]).start();
    });
  }, []);

  // Attendre que l'état de navigation soit prêt
  const navigationState = useRootNavigationState();

  // Afficher un écran de chargement amélioré pendant l'initialisation de la navigation
  if (!navigationState?.key) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={['#4c669f', '#3b5998', '#192f6a']}
          locations={[0, 0.5, 1]}
          start={{x: 0, y: 0}}
          end={{x: 0, y: 1}}
          style={styles.background}
        />
        
        <Animated.View 
          style={[
            styles.logoContainer,
            { 
              opacity: logoOpacity,
              transform: [{ scale: logoScale }]
            }
          ]}
        >
          <Image 
            source={require('../../assets/images/profile.png')} 
            style={styles.logo}
            resizeMode="contain"
            // Ajouter le préchargement de l'image
            fadeDuration={200}
          />
          <Text style={styles.appName}>Bomoko</Text>
        </Animated.View>
        
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#ffffff" />
          <Text style={styles.loadingText}>Chargement en cours...</Text>
        </View>
      </View>
    );
  }

  // Rediriger vers la page d'accueil
  return <Redirect href="../(tabs)" />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 50,
  },
  logo: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 20,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 10,
  },
  loadingContainer: {
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#ffffff',
    fontWeight: '500',
  },
}); 