-- Update webhook URLs and configuration
UPDATE webhook_urls
SET url = 'https://n8n-dw1u.onrender.com/webhook-test/chat',
    backup_url = 'https://backup-n8n-dw1u.onrender.com/webhook-test/chat',
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    health_check_enabled = true,
    max_retries = 3,
    retry_delay_base = 2000,
    health_check_interval = 300
WHERE name = 'n8n_chatbot_agent';

-- Create or replace the health check function with better error handling
CREATE OR REPLACE FUNCTION check_webhook_health()
RETURNS void AS $$
BEGIN
  -- Reset webhook status if it's been inactive too long
  UPDATE webhook_urls
  SET is_active = true,
      error_count = 0,
      error_message = null,
      last_check_timestamp = CURRENT_TIMESTAMP
  WHERE name = 'n8n_chatbot_agent'
    AND (
      last_check_timestamp IS NULL 
      OR last_check_timestamp < CURRENT_TIMESTAMP - INTERVAL '5 minutes'
      OR (error_count > 0 AND last_error_timestamp < CURRENT_TIMESTAMP - INTERVAL '15 minutes')
    );

  -- Clean up old health logs
  DELETE FROM webhook_health_logs
  WHERE check_timestamp < CURRENT_TIMESTAMP - INTERVAL '24 hours';
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically check health
CREATE OR REPLACE FUNCTION trigger_health_check()
RETURNS trigger AS $$
BEGIN
  PERFORM check_webhook_health();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS webhook_health_check_trigger ON webhook_urls;

-- Create new trigger
CREATE TRIGGER webhook_health_check_trigger
  AFTER UPDATE OF error_count, last_error_timestamp
  ON webhook_urls
  FOR EACH ROW
  EXECUTE FUNCTION trigger_health_check();