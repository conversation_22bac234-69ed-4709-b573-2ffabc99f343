#!/bin/bash

# Arrêter tous les processus Metro en cours
echo "Arrêt des processus Metro en cours..."
npx kill-port 8081 19000 19001 19002

# Supprimer les dossiers de cache
echo "Suppression des dossiers de cache..."
rm -rf node_modules
rm -rf .expo
rm -rf .rn-cli.cache
rm -rf android/build
rm -rf ios/build
rm -rf android/.gradle
rm -rf ios/Pods

# Supprimer les fichiers de verrouillage
echo "Suppression des fichiers de verrouillage..."
rm -f yarn.lock
rm -f package-lock.json

# Nettoyer le cache de npm
echo "Nettoyage du cache npm..."
npm cache clean --force

# Nettoyer le cache de Watchman
echo "Nettoyage du cache Watchman..."
watchman watch-del-all

# Réinstaller les dépendances
echo "Réinstallation des dépendances..."
npm install

# Nettoyer le cache de Metro
echo "Nettoyage du cache Metro..."
npx react-native start --reset-cache --no-interactive &
sleep 10
kill $!

echo "Nettoyage terminé. Vous pouvez maintenant redémarrer l'application avec 'npx expo start --clear'."
