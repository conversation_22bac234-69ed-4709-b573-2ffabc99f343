-- Update webhook configurations with proper error handling
CREATE OR REPLACE FUNCTION check_webhook_configuration(webhook_name TEXT)
RETURNS TABLE (
    is_available boolean,
    current_url varchar(500),
    error_details text
) AS $$
DECLARE
    config_record webhook_configurations%ROWTYPE;
    backup_available boolean;
BEGIN
    -- Get webhook record with retry logic
    BEGIN
        SELECT * INTO config_record
        FROM webhook_configurations
        WHERE name = webhook_name
        AND is_active = true;

        IF config_record IS NULL THEN
            RETURN QUERY SELECT 
                false::boolean, 
                NULL::varchar(500), 
                'Configuration non disponible'::text;
            RETURN;
        END IF;

        -- Check if backup URL is available and valid
        backup_available := config_record.backup_url IS NOT NULL 
                          AND config_record.backup_url != ''
                          AND config_record.backup_url ~ '^https://';

        -- Return appropriate URL based on error count and backup availability
        RETURN QUERY
        SELECT 
            CASE
                WHEN config_record.error_count >= config_record.max_retries AND NOT backup_available THEN false
                ELSE true
            END::boolean,
            CASE 
                WHEN config_record.error_count >= config_record.max_retries AND backup_available THEN config_record.backup_url
                ELSE config_record.url
            END::varchar(500),
            CASE
                WHEN config_record.error_count >= config_record.max_retries AND NOT backup_available THEN 'Service temporairement indisponible'
                WHEN config_record.error_message IS NOT NULL AND config_record.error_message != '' THEN config_record.error_message
                ELSE ''
            END::text;
    EXCEPTION WHEN OTHERS THEN
        RETURN QUERY SELECT 
            false::boolean, 
            NULL::varchar(500), 
            'Erreur interne du service'::text;
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update webhook status
CREATE OR REPLACE FUNCTION update_webhook_status(
    webhook_name TEXT,
    success BOOLEAN,
    error_msg TEXT DEFAULT NULL
)
RETURNS void AS $$
BEGIN
    UPDATE webhook_configurations
    SET 
        error_count = CASE 
            WHEN success THEN 0 
            ELSE error_count + 1 
        END,
        error_message = CASE 
            WHEN success THEN NULL 
            ELSE error_msg 
        END,
        last_check_timestamp = CURRENT_TIMESTAMP,
        last_error_timestamp = CASE 
            WHEN success THEN last_error_timestamp 
            ELSE CURRENT_TIMESTAMP 
        END
    WHERE name = webhook_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update webhook configurations
UPDATE webhook_configurations
SET 
    url = 'https://n8n-dw1u.onrender.com/webhook-test/emerigency',
    backup_url = 'https://n8n-dw1u.onrender.com/webhook-test/emerigency',
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    metadata = jsonb_build_object(
        'version', '1.0.1',
        'status', 'healthy',
        'last_update', CURRENT_TIMESTAMP,
        'webhook_type', 'emergency',
        'content_type', 'application/json',
        'expected_response_format', jsonb_build_object(
            'status', 'string',
            'message', 'string',
            'event_id', 'string'
        )
    )
WHERE name = 'n8n_emergency_webhook';