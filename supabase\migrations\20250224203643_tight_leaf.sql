-- Update webhook configuration with correct URL and settings
UPDATE webhook_configurations
SET 
    url = 'https://n8n-dw1u.onrender.com/webhook-test/chat',
    backup_url = 'https://n8n-dw1u.onrender.com/webhook-test/chat',
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    last_error_timestamp = null,
    metadata = jsonb_build_object(
        'version', '1.0.0',
        'status', 'healthy',
        'last_update', CURRENT_TIMESTAMP,
        'webhook_type', 'chat',
        'content_type', 'application/json',
        'expected_response_format', jsonb_build_object(
            'output', 'string',
            'status', 'string',
            'metadata', 'object'
        )
    )
WHERE name = 'n8n_chatbot_agent';

-- Create function to validate webhook response format
CREATE OR REPLACE FUNCTION validate_webhook_response(response jsonb)
RETURNS boolean AS $$
BEGIN
    RETURN (
        response ? 'output' AND 
        jsonb_typeof(response->'output') = 'string' AND
        response ? 'status'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;