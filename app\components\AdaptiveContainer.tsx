import React from 'react';
import { View, StyleSheet, Dimensions, ViewStyle, StyleProp } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface AdaptiveContainerProps {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  fullWidth?: boolean;
  padded?: boolean;
  centered?: boolean;
}

/**
 * Conteneur adaptatif qui s'ajuste automatiquement aux différentes tailles d'écran
 * - Sur les petits écrans (téléphones), utilise toute la largeur avec des marges
 * - Sur les grands écrans (tablettes), limite la largeur et centre le contenu
 */
export default function AdaptiveContainer({
  children,
  style,
  fullWidth = false,
  padded = true,
  centered = true,
}: AdaptiveContainerProps) {
  const { width } = Dimensions.get('window');
  const insets = useSafeAreaInsets();
  const isLargeScreen = width >= 768;

  // Calculer la largeur maximale en fonction de la taille de l'écran
  const maxWidth = fullWidth ? '100%' : isLargeScreen ? 700 : '100%';

  return (
    <View
      style={[
        styles.container,
        {
          paddingHorizontal: padded ? 16 : 0,
          paddingBottom: insets.bottom > 0 ? insets.bottom : 16,
          alignItems: centered ? 'center' : 'flex-start',
        },
        style,
      ]}
    >
      <View
        style={[
          styles.innerContainer,
          {
            maxWidth,
            width: '100%',
          },
        ]}
      >
        {children}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
  },
  innerContainer: {
    flex: 1,
  },
});
