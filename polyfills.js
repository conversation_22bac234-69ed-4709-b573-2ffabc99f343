// Polyfills for React Native to handle Node.js modules
import 'react-native-url-polyfill/auto';
import 'react-native-get-random-values';

// Mock WebSocket and related modules to prevent bundling errors
if (typeof global !== 'undefined') {
  // Mock WebSocket if it doesn't exist
  if (!global.WebSocket) {
    global.WebSocket = class MockWebSocket {
      constructor() {
        console.warn('WebSocket is not available in React Native');
      }

      close() {}
      send() {}
      addEventListener() {}
      removeEventListener() {}
    };
  }

  // Mock ws library modules
  global.ws = false;

  // Mock Node.js modules that might be imported
  global.process = global.process || {
    env: {},
    nextTick: (fn) => setTimeout(fn, 0),
    version: '16.0.0',
    platform: 'react-native'
  };

  // Mock Buffer if not available
  if (!global.Buffer) {
    global.Buffer = require('@craftzdog/react-native-buffer').Buffer;
  }

  // Mock stream module
  global.stream = false;

  // Mock other Node.js modules
  global.http = false;
  global.https = false;
  global.net = false;
  global.tls = false;
  global.fs = false;
  global.path = false;
  global.os = false;
  global.crypto = false;
  global.zlib = false;
}

// Mock ws module exports to prevent import errors
const mockWs = function() {
  console.warn('WebSocket (ws) library is not available in React Native');
  return null;
};

// Mock all possible ws exports
mockWs.WebSocket = mockWs;
mockWs.WebSocketServer = mockWs;
mockWs.createWebSocketStream = mockWs;
mockWs.CONNECTING = 0;
mockWs.OPEN = 1;
mockWs.CLOSING = 2;
mockWs.CLOSED = 3;

// Export the mock as both default and named exports
export default mockWs;
export const WebSocket = mockWs;
export const WebSocketServer = mockWs;
export const createWebSocketStream = mockWs;
export const CONNECTING = 0;
export const OPEN = 1;
export const CLOSING = 2;
export const CLOSED = 3;

// Also export as CommonJS for compatibility
if (typeof module !== 'undefined' && module.exports) {
  module.exports = mockWs;
  module.exports.default = mockWs;
  module.exports.WebSocket = mockWs;
  module.exports.WebSocketServer = mockWs;
  module.exports.createWebSocketStream = mockWs;
  module.exports.CONNECTING = 0;
  module.exports.OPEN = 1;
  module.exports.CLOSING = 2;
  module.exports.CLOSED = 3;
}
