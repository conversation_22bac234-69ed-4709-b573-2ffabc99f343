// Polyfills for React Native to handle Node.js modules
import 'react-native-url-polyfill/auto';
import 'react-native-get-random-values';

// Mock WebSocket and related modules to prevent bundling errors
if (typeof global !== 'undefined') {
  // Mock WebSocket if it doesn't exist
  if (!global.WebSocket) {
    global.WebSocket = class MockWebSocket {
      constructor() {
        console.warn('WebSocket is not available in React Native');
      }
      
      close() {}
      send() {}
      addEventListener() {}
      removeEventListener() {}
    };
  }

  // Mock ws library modules
  global.ws = false;
  
  // Mock Node.js modules that might be imported
  global.process = global.process || {
    env: {},
    nextTick: (fn) => setTimeout(fn, 0),
    version: '16.0.0',
    platform: 'react-native'
  };

  // Mock Buffer if not available
  if (!global.Buffer) {
    global.Buffer = require('@craftzdog/react-native-buffer').Buffer;
  }

  // Mock stream module
  global.stream = false;
  
  // Mock other Node.js modules
  global.http = false;
  global.https = false;
  global.net = false;
  global.tls = false;
  global.fs = false;
  global.path = false;
  global.os = false;
  global.crypto = false;
  global.zlib = false;
}

// Export empty object to make this a valid module
export default {};
