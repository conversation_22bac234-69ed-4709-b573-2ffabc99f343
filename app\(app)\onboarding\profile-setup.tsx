import React from 'react';
import { View, SafeAreaView, StyleSheet, StatusBar, KeyboardAvoidingView, Platform } from 'react-native';
import { ProfileSetup } from './profile-setup-steps/ProfileSetup';
import { useFocusEffect } from '@react-navigation/native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, { FadeIn } from 'react-native-reanimated';

export default function OnboardingScreen() {
  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoid}
      >
        <SafeAreaView style={styles.container}>
          <LinearGradient
            colors={['#f0f8ff', '#e6f2ff', '#f7f7f7']}
            style={styles.gradient}
          >
            <Animated.View 
              style={styles.content}
              entering={FadeIn.duration(800).delay(200)}
            >
              <ProfileSetup />
            </Animated.View>
          </LinearGradient>
        </SafeAreaView>
      </KeyboardAvoidingView>
    </>
  );
}

const styles = StyleSheet.create({
  keyboardAvoid: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: '#f7f7f7',
  },
  gradient: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  content: {
    flex: 1,
    width: '100%',
    height: '100%',
    paddingTop: StatusBar.currentHeight || 0,
  }
});