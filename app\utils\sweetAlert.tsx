import React from 'react';
import { Alert, Platform, View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Toast from 'react-native-toast-message';
import { Ionicons } from '@expo/vector-icons';

// Types pour les alertes
interface AlertOptions {
  title?: string;
  message: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  confirmText?: string;
  cancelText?: string;
  type?: 'success' | 'error' | 'warning' | 'info';
}

interface ToastContentProps {
  title?: string;
  message: string;
  color: string;
  backgroundColor: string;
  type: AlertOptions['type'];
  icon?: string;
}

// Fonction pour obtenir l'icône en fonction du type d'alerte
const getIconByType = (type: AlertOptions['type']): string => {
  switch (type) {
    case 'success':
      return 'checkmark-circle';
    case 'error':
      return 'close-circle';
    case 'warning':
      return 'warning';
    case 'info':
    default:
      return 'information-circle';
  }
};

const ToastContent: React.FC<ToastContentProps> = ({ 
  title, 
  message, 
  color, 
  backgroundColor, 
  type, 
  icon 
}) => {
  // Utiliser l'icône fournie ou obtenir celle par défaut en fonction du type
  const iconName = icon || getIconByType(type);
  
  return (
    <View style={{ backgroundColor: backgroundColor, borderRadius: 8, padding: 16, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <Ionicons name={iconName as any} size={24} color={color} style={{ marginRight: 8 }} />
        <View>
          <Text style={{ fontSize: 16, fontWeight: 'bold', color: color }}>{title || getDefaultTitleByType(type)}</Text>
          <Text style={{ fontSize: 14, color: '#444' }}>{message}</Text>
        </View>
      </View>
      <TouchableOpacity onPress={() => Toast.hide()}>
        <Ionicons name="close-circle-outline" size={24} color="#888" />
      </TouchableOpacity>
    </View>
  );
};

// Fonction pour afficher une alerte de base
export const showAlert = (options: AlertOptions) => {
  const { 
    title = '', 
    message, 
    onConfirm, 
    onCancel, 
    confirmText = 'OK',
    cancelText = 'Annuler',
    type = 'info'
  } = options;

  // Version améliorée pour les alertes de base
  if (onCancel) {
    // Alerte avec confirmation et annulation
    Alert.alert(
      title,
      message,
      [
        {
          text: cancelText,
          onPress: onCancel,
          style: 'cancel',
        },
        {
          text: confirmText,
          onPress: onConfirm,
          style: 'default',
        },
      ],
      { cancelable: false }
    );
  } else {
    // Alerte simple avec un seul bouton
    Alert.alert(
      title,
      message,
      [
        {
          text: confirmText,
          onPress: onConfirm,
        },
      ],
      { cancelable: false }
    );
  }

  let backgroundColor = '#f0f8ff';
  let color = '#007AFF';
  let icon = getIconByType(type);

  switch (type) {
    case 'success':
      backgroundColor = '#f0fff0';
      color = '#28A745';
      break;
    case 'error':
      backgroundColor = '#fff0f0';
      color = '#FF3B30';
      break;
    case 'warning':
      backgroundColor = '#fffaf0';
      color = '#FF9500';
      break;
    case 'info':
      backgroundColor = '#f0f8ff';
      color = '#007AFF';
      break;
  }

// Afficher également un toast pour plus de visibilité
  Toast.show({
    position: 'top',
    visibilityTime: 4000,
    autoHide: true,
    topOffset: Platform.OS === 'ios' ? 60 : 40,
    render: () => (
      <ToastContent
        title={title}
        message={message}
        color={color}
        backgroundColor={backgroundColor}
        type={type}
        icon={icon}
      />
    ),
  });
};

// Fonction pour obtenir un titre par défaut selon le type d'alerte
const getDefaultTitleByType = (type: AlertOptions['type']) => {
  switch (type) {
    case 'success':
      return 'Succès';
    case 'error':
      return 'Erreur';
    case 'warning':
      return 'Attention';
    case 'info':
    default:
      return 'Information';
  }
};

// Fonctions d'aide pour différents types d'alertes
export const showSuccessAlert = (options: Omit<AlertOptions, 'type'>) => {
  showAlert({ ...options, type: 'success' });
};

export const showErrorAlert = (options: Omit<AlertOptions, 'type'>) => {
  showAlert({ ...options, type: 'error' });
};

export const showWarningAlert = (options: Omit<AlertOptions, 'type'>) => {
  showAlert({ ...options, type: 'warning' });
};

export const showInfoAlert = (options: Omit<AlertOptions, 'type'>) => {
  showAlert({ ...options, type: 'info' });
};

// Alerte de confirmation avec deux boutons
export const showConfirmAlert = (
  title: string,
  message: string,
  onConfirm: () => void,
  onCancel?: () => void,
  confirmText = 'Confirmer',
  cancelText = 'Annuler'
) => {
  showAlert({
    title,
    message,
    onConfirm,
    onCancel: onCancel || (() => {}),
    confirmText,
    cancelText,
    type: 'warning',
  });
};

// Exportation par défaut pour faciliter l'utilisation
export default {
  showAlert,
  showSuccessAlert,
  showErrorAlert,
  showWarningAlert,
  showInfoAlert,
  showConfirmAlert,
};
