-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can manage their own SOS events" ON sos_events;
DROP POLICY IF EXISTS "Users can manage their own location updates" ON sos_location_updates;
DROP POLICY IF EXISTS "Users can manage their own audio recordings" ON sos_audio_recordings;

-- Drop existing tables if they exist
DROP TABLE IF EXISTS sos_audio_recordings CASCADE;
DROP TABLE IF EXISTS sos_location_updates CASCADE;
DROP TABLE IF EXISTS sos_events CASCADE;

-- Create SOS events table
CREATE TABLE IF NOT EXISTS sos_events (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    started_at timestamptz DEFAULT now(),
    ended_at timestamptz,
    initial_location jsonb,
    status text NOT NULL CHECK (status IN ('active', 'ended', 'cancelled')) DEFAULT 'active',
    metadata jsonb DEFAULT '{}'::jsonb
);

-- Create SOS location updates table
CREATE TABLE IF NOT EXISTS sos_location_updates (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    sos_event_id uuid REFERENCES sos_events(id) ON DELETE CASCADE,
    latitude double precision NOT NULL,
    longitude double precision NOT NULL,
    accuracy double precision,
    timestamp timestamptz DEFAULT now(),
    metadata jsonb DEFAULT '{}'::jsonb
);

-- Create SOS audio recordings table
CREATE TABLE IF NOT EXISTS sos_audio_recordings (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    sos_event_id uuid REFERENCES sos_events(id) ON DELETE CASCADE,
    file_path text NOT NULL,
    duration integer NOT NULL, -- in seconds
    started_at timestamptz DEFAULT now(),
    uploaded_at timestamptz,
    telegram_message_id text,
    metadata jsonb DEFAULT '{}'::jsonb
);

-- Enable RLS
ALTER TABLE sos_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE sos_location_updates ENABLE ROW LEVEL SECURITY;
ALTER TABLE sos_audio_recordings ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can manage their own SOS events"
    ON sos_events
    FOR ALL
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can manage their own location updates"
    ON sos_location_updates
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM sos_events
            WHERE sos_events.id = sos_event_id
            AND sos_events.user_id = auth.uid()
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM sos_events
            WHERE sos_events.id = sos_event_id
            AND sos_events.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage their own audio recordings"
    ON sos_audio_recordings
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM sos_events
            WHERE sos_events.id = sos_event_id
            AND sos_events.user_id = auth.uid()
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM sos_events
            WHERE sos_events.id = sos_event_id
            AND sos_events.user_id = auth.uid()
        )
    );

-- Drop existing functions if they exist
DROP FUNCTION IF EXISTS start_sos_event(double precision, double precision, double precision, jsonb);
DROP FUNCTION IF EXISTS end_sos_event(uuid, text);

-- Create function to start SOS event
CREATE OR REPLACE FUNCTION start_sos_event(
    initial_lat double precision,
    initial_lng double precision,
    initial_accuracy double precision DEFAULT NULL,
    metadata jsonb DEFAULT '{}'::jsonb
)
RETURNS uuid AS $$
DECLARE
    new_event_id uuid;
BEGIN
    -- Create new SOS event
    INSERT INTO sos_events (
        user_id,
        initial_location,
        metadata
    ) VALUES (
        auth.uid(),
        jsonb_build_object(
            'latitude', initial_lat,
            'longitude', initial_lng,
            'accuracy', initial_accuracy,
            'timestamp', CURRENT_TIMESTAMP
        ),
        metadata
    )
    RETURNING id INTO new_event_id;

    -- Create initial location update
    INSERT INTO sos_location_updates (
        sos_event_id,
        latitude,
        longitude,
        accuracy
    ) VALUES (
        new_event_id,
        initial_lat,
        initial_lng,
        initial_accuracy
    );

    RETURN new_event_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to end SOS event
CREATE OR REPLACE FUNCTION end_sos_event(
    event_id uuid,
    end_status text DEFAULT 'ended'
)
RETURNS boolean AS $$
BEGIN
    UPDATE sos_events
    SET 
        ended_at = CURRENT_TIMESTAMP,
        status = end_status
    WHERE id = event_id
    AND user_id = auth.uid()
    AND status = 'active';

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;