import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Stack } from 'expo-router';
import { useTheme } from '../../context/theme';
import AppointmentsScreen from '../(app)/appointments';

export default function AppointmentsTab() {
  const { theme } = useTheme();

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      <AppointmentsScreen />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});