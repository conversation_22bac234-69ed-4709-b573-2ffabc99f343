import 'react-native-url-polyfill/auto';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// Logger pour le débogage
const logger = {
  debug: (...args: any[]) => {
    if (__DEV__) {
      console.debug('[Supabase]', ...args);
    }
  },
  info: (...args: any[]) => {
    if (__DEV__) {
      console.info('[Supabase]', ...args);
    }
  },
  warn: (...args: any[]) => {
    console.warn('[Supabase]', ...args);
  },
  error: (...args: any[]) => {
    console.error('[Supabase]', ...args);
  }
};

// Implémentation du stockage sécurisé pour Supabase
const storage = {
      getItem: async (key: string) => {
    if (Platform.OS === 'web') {
      return localStorage.getItem(key);
    } else {
      try {
        return await SecureStore.getItemAsync(key);
      } catch (error) {
        console.error('Erreur lors de la récupération de la clé:', error);
                return null;
              }
        }
      },
      setItem: async (key: string, value: string) => {
    if (Platform.OS === 'web') {
      localStorage.setItem(key, value);
    } else {
      try {
        await SecureStore.setItemAsync(key, value);
      } catch (error) {
        console.error('Erreur lors de l\'enregistrement de la clé:', error);
      }
        }
      },
      removeItem: async (key: string) => {
    if (Platform.OS === 'web') {
      localStorage.removeItem(key);
    } else {
      try {
        await SecureStore.deleteItemAsync(key);
      } catch (error) {
        console.error('Erreur lors de la suppression de la clé:', error);
      }
        }
      },
    };

// Créer le client Supabase avec des options de base
export const supabase = createClient(
  process.env.EXPO_PUBLIC_SUPABASE_URL || "",
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || "",
  {
  auth: {
    storage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: Platform.OS === 'web',
    },
    // Désactiver les hooks globaux qui pourraient causer des problèmes
    global: {
      headers: {
        'X-Client-Info': 'bomoko-mobile-app'
      }
    },
    // Configurer les options de requête pour éviter les problèmes de transaction
    db: {
      schema: 'public'
    },
    // Désactiver les fonctionnalités real-time pour éviter les problèmes de WebSocket
    realtime: {
      params: {
        eventsPerSecond: 0
      }
    }
  }
);

// Écouter les changements d'état d'authentification
supabase.auth.onAuthStateChange((event, session) => {
  if (event === 'SIGNED_IN' && session) {
    console.log('Utilisateur connecté:', session.user?.email);
  } else if (event === 'SIGNED_OUT') {
    console.log('Utilisateur déconnecté');
  } else if (event === 'TOKEN_REFRESHED') {
    console.log('Token rafraîchi');
  } else if (event === 'USER_UPDATED') {
    console.log('Utilisateur mis à jour');
  }
});

// Fonction utilitaire pour vérifier l'état de la session
export const checkAuthStatus = async () => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) {
      logger.error('Erreur vérification session:', error);
      return null;
    }
    if (session) {
      logger.info('Session valide trouvée:', session.user.email);
    }
    return session;
  } catch (err) {
    logger.error('Exception vérification session:', err);
    return null;
  }
};

// Fonction utilitaire pour tester la connexion Supabase
export const testSupabaseConnection = async () => {
  try {
    logger.info('Test de la connexion Supabase...');

    // Vérifier la connexion à Supabase
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1)
      .single();

    if (error) {
      logger.error('Erreur de connexion:', error);
      return false;
    }

    logger.info('Connexion Supabase OK');
    return true;
  } catch (err) {
    logger.error('Exception test connexion:', err);
    return false;
  }
};

// Fonction utilitaire pour vérifier si une table existe
export async function checkTableExists(tableName: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', tableName)
      .single();

    if (error) {
      console.error(`Erreur lors de la vérification de l'existence de la table ${tableName}:`, error);
      return false;
    }

    return !!data;
  } catch (error) {
    console.error(`Exception lors de la vérification de l'existence de la table ${tableName}:`, error);
    return false;
  }
}