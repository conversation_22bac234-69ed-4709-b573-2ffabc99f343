import { create } from 'zustand';

export type Gender = 'homme' | 'femme' | 'non-binaire' | 'non_precise';

export interface UserProfile {
  firstName: string;
  lastName: string;
  age: number | null;
  gender: Gender | '';
  phoneNumber: string;
  dateOfBirth: string | null;
  address: string;
  emergencyContact: string;
  languagePreference: string;
  profilePicture: string | null;
  professionalTitle: string;
  professionalBio: string;
  profileVisibility: string;
  profileCompleted: boolean;
  notificationPreferences: Record<string, boolean>;
  riskAssessment: Record<string, any>;
  riskLevel: string;
  aiEvaluation: string | null;
  aiResponses: Record<string, string>;
}

interface ProfileStore {
  currentStep: number;
  profile: UserProfile;
  setCurrentStep: (step: number) => void;
  updateProfile: (data: Partial<UserProfile>) => void;
  resetProfile: () => void;
  addAiResponse: (question: string, answer: string) => void;
}

// Définition d'un profil par défaut
const defaultProfile: UserProfile = {
  firstName: '',
  lastName: '',
  age: null,
  gender: '',
  phoneNumber: '',
  dateOfBirth: null,
  address: '',
  emergencyContact: '',
  languagePreference: '',
  profilePicture: null,
  professionalTitle: '',
  professionalBio: '',
  profileVisibility: 'public',
  profileCompleted: false,
  notificationPreferences: {},
  riskAssessment: {},
  riskLevel: '',
  aiEvaluation: null,
  aiResponses: {}
};

export const useProfileStore = create<ProfileStore>((set: any) => ({
  currentStep: 1,
  profile: { ...defaultProfile },

  setCurrentStep: (step: number) => set({ currentStep: step }),

  updateProfile: (data: Partial<UserProfile>) => set((state: any) => ({
    profile: { ...state.profile, ...data }
  })),

  resetProfile: () => set({ profile: { ...defaultProfile }, currentStep: 1 }),

  addAiResponse: (question: string, answer: string) => set((state: any) => ({
    profile: {
      ...state.profile,
      aiResponses: {
        ...state.profile.aiResponses,
        [question]: answer
      }
    }
  }))
}));

// Export par défaut pour Expo Router
export default useProfileStore;