-- Create forum categories table
CREATE TABLE forum_categories (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text NOT NULL,
    description text,
    slug text UNIQUE NOT NULL,
    icon text,
    color text,
    order_index integer DEFAULT 0,
    is_private boolean DEFAULT false,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create forum topics table
CREATE TABLE forum_topics (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    category_id uuid REFERENCES forum_categories(id) ON DELETE CASCADE,
    title text NOT NULL,
    content text NOT NULL,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    is_anonymous boolean DEFAULT false,
    is_locked boolean DEFAULT false,
    is_pinned boolean DEFAULT false,
    moderation_status text DEFAULT 'pending' CHECK (moderation_status IN ('pending', 'approved', 'rejected', 'flagged')),
    moderation_reason text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    metadata jsonb DEFAULT '{}'::jsonb
);

-- Create forum replies table
CREATE TABLE forum_replies (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    topic_id uuid REFERENCES forum_topics(id) ON DELETE CASCADE,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    content text NOT NULL,
    is_anonymous boolean DEFAULT false,
    parent_id uuid REFERENCES forum_replies(id),
    moderation_status text DEFAULT 'pending' CHECK (moderation_status IN ('pending', 'approved', 'rejected', 'flagged')),
    moderation_reason text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    metadata jsonb DEFAULT '{}'::jsonb
);

-- Create forum reactions table
CREATE TABLE forum_reactions (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    target_type text NOT NULL CHECK (target_type IN ('topic', 'reply')),
    target_id uuid NOT NULL,
    reaction_type text NOT NULL CHECK (reaction_type IN ('like', 'support', 'hug', 'thanks')),
    created_at timestamptz DEFAULT now(),
    UNIQUE(user_id, target_type, target_id, reaction_type)
);

-- Create forum reports table
CREATE TABLE forum_reports (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    reporter_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    target_type text NOT NULL CHECK (target_type IN ('topic', 'reply')),
    target_id uuid NOT NULL,
    reason text NOT NULL,
    status text DEFAULT 'pending' CHECK (status IN ('pending', 'resolved', 'dismissed')),
    created_at timestamptz DEFAULT now(),
    resolved_at timestamptz,
    resolved_by uuid REFERENCES auth.users(id)
);

-- Enable RLS
ALTER TABLE forum_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_topics ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_replies ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE forum_reports ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Anyone can view public categories"
    ON forum_categories
    FOR SELECT
    USING (NOT is_private);

CREATE POLICY "Authenticated users can view private categories"
    ON forum_categories
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Users can view approved topics"
    ON forum_topics
    FOR SELECT
    USING (moderation_status = 'approved');

CREATE POLICY "Users can create topics"
    ON forum_topics
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Users can update own topics"
    ON forum_topics
    FOR UPDATE
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can view approved replies"
    ON forum_replies
    FOR SELECT
    USING (moderation_status = 'approved');

CREATE POLICY "Users can create replies"
    ON forum_replies
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

CREATE POLICY "Users can update own replies"
    ON forum_replies
    FOR UPDATE
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can manage own reactions"
    ON forum_reactions
    FOR ALL
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can create reports"
    ON forum_reports
    FOR INSERT
    TO authenticated
    WITH CHECK (reporter_id = auth.uid());

-- Create functions for content moderation
CREATE OR REPLACE FUNCTION moderate_content(content text)
RETURNS text AS $$
DECLARE
    moderation_result text := 'approved';
BEGIN
    -- Basic content moderation rules
    IF content ~* '(insulte|menace|violence|haine)' THEN
        moderation_result := 'rejected';
    END IF;
    
    RETURN moderation_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to create a topic with moderation
CREATE OR REPLACE FUNCTION create_forum_topic(
    p_category_id uuid,
    p_title text,
    p_content text,
    p_is_anonymous boolean DEFAULT false
)
RETURNS uuid AS $$
DECLARE
    v_topic_id uuid;
    v_moderation_status text;
BEGIN
    -- Check content
    v_moderation_status := moderate_content(p_content);
    
    -- Create topic
    INSERT INTO forum_topics (
        category_id,
        title,
        content,
        user_id,
        is_anonymous,
        moderation_status
    ) VALUES (
        p_category_id,
        p_title,
        p_content,
        auth.uid(),
        p_is_anonymous,
        v_moderation_status
    )
    RETURNING id INTO v_topic_id;
    
    RETURN v_topic_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to create a reply with moderation
CREATE OR REPLACE FUNCTION create_forum_reply(
    p_topic_id uuid,
    p_content text,
    p_is_anonymous boolean DEFAULT false,
    p_parent_id uuid DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
    v_reply_id uuid;
    v_moderation_status text;
BEGIN
    -- Check content
    v_moderation_status := moderate_content(p_content);
    
    -- Create reply
    INSERT INTO forum_replies (
        topic_id,
        content,
        user_id,
        is_anonymous,
        parent_id,
        moderation_status
    ) VALUES (
        p_topic_id,
        p_content,
        auth.uid(),
        p_is_anonymous,
        p_parent_id,
        v_moderation_status
    )
    RETURNING id INTO v_reply_id;
    
    RETURN v_reply_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert initial categories
INSERT INTO forum_categories (name, description, slug, icon, color, order_index) VALUES
('Témoignages', 'Partagez votre histoire et votre expérience', 'temoignages', 'heart-outline', '#6366F1', 1),
('Entraide', 'Demandez et offrez du soutien', 'entraide', 'people-outline', '#EC4899', 2),
('Ressources', 'Informations utiles et contacts', 'ressources', 'information-circle-outline', '#10B981', 3),
('Questions & Réponses', 'Posez vos questions à la communauté', 'questions', 'help-circle-outline', '#F59E0B', 4);