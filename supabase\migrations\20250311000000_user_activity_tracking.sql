/*
  # Création du système de suivi des activités utilisateur
  
  Ce fichier de migration crée :
  1. Une nouvelle table `user_activities` pour stocker toutes les activités des utilisateurs
  2. Des fonctions et triggers pour enregistrer automatiquement certaines activités
  3. Des politiques RLS pour sécuriser l'accès aux données
*/

-- Création de la table principale pour le suivi des activités
CREATE TABLE IF NOT EXISTS user_activities (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  activity_type text NOT NULL,
  activity_data jsonb DEFAULT '{}'::jsonb,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamptz DEFAULT now(),
  
  -- Contrainte pour vérifier que activity_type est une valeur valide
  CONSTRAINT valid_activity_type CHECK (
    activity_type IN (
      'login', 
      'logout', 
      'profile_update', 
      'forum_view', 
      'forum_post', 
      'forum_reply', 
      'chatbot_interaction', 
      'appointment_created', 
      'appointment_updated', 
      'appointment_cancelled', 
      'alert_created', 
      'alert_responded', 
      'document_uploaded', 
      'document_viewed',
      'resource_accessed',
      'assessment_completed',
      'location_updated',
      'notification_received',
      'notification_clicked',
      'page_view',
      'map_interaction',
      'sos_triggered',
      'sos_cancelled',
      'ngo_viewed',
      'ngo_contacted',
      'search_performed',
      'settings_changed',
      'media_played',
      'link_clicked',
      'feature_used'
    )
  )
);

-- Index pour améliorer les performances des requêtes
CREATE INDEX user_activities_user_id_idx ON user_activities(user_id);
CREATE INDEX user_activities_activity_type_idx ON user_activities(activity_type);
CREATE INDEX user_activities_created_at_idx ON user_activities(created_at);

-- Activer RLS sur la table
ALTER TABLE user_activities ENABLE ROW LEVEL SECURITY;

-- Politique pour permettre aux utilisateurs de voir uniquement leurs propres activités
CREATE POLICY "Users can view their own activities"
  ON user_activities
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Politique pour permettre aux administrateurs de voir toutes les activités
CREATE POLICY "Admins can view all activities"
  ON user_activities
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  );

-- Politique pour permettre aux utilisateurs d'insérer leurs propres activités
CREATE POLICY "Users can insert their own activities"
  ON user_activities
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Fonction pour enregistrer une activité de connexion
CREATE OR REPLACE FUNCTION log_user_login()
RETURNS trigger AS $$
BEGIN
  INSERT INTO user_activities (user_id, activity_type, activity_data, metadata)
  VALUES (
    NEW.id, 
    'login', 
    jsonb_build_object('email', NEW.email),
    jsonb_build_object(
      'ip_address', current_setting('request.headers', true)::jsonb->'x-forwarded-for',
      'user_agent', current_setting('request.headers', true)::jsonb->'user-agent'
    )
  );
  RETURN NEW;
END;
$$ language plpgsql security definer;

-- Trigger pour enregistrer les connexions
CREATE TRIGGER on_auth_user_login
  AFTER INSERT ON auth.sessions
  FOR EACH ROW EXECUTE FUNCTION log_user_login();

-- Fonction pour enregistrer une mise à jour de profil
CREATE OR REPLACE FUNCTION log_profile_update()
RETURNS trigger AS $$
BEGIN
  INSERT INTO user_activities (user_id, activity_type, activity_data)
  VALUES (
    NEW.id, 
    'profile_update', 
    jsonb_build_object(
      'updated_fields', (
        SELECT jsonb_object_agg(key, value)
        FROM jsonb_each(to_jsonb(NEW))
        WHERE key != 'id' AND key != 'created_at' AND key != 'updated_at'
        AND (to_jsonb(OLD) ->> key) IS DISTINCT FROM (to_jsonb(NEW) ->> key)
      )
    )
  );
  RETURN NEW;
END;
$$ language plpgsql security definer;

-- Trigger pour enregistrer les mises à jour de profil
CREATE TRIGGER on_profile_update
  AFTER UPDATE ON profiles
  FOR EACH ROW EXECUTE FUNCTION log_profile_update();

-- Fonction pour obtenir un résumé des activités d'un utilisateur
CREATE OR REPLACE FUNCTION get_user_activity_summary(user_uuid uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result jsonb;
BEGIN
  -- Vérifier si l'utilisateur est autorisé à voir ces données
  IF auth.uid() != user_uuid AND NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Non autorisé à accéder aux données de cet utilisateur';
  END IF;

  -- Calculer le résumé des activités
  SELECT jsonb_build_object(
    'total_activities', COUNT(*),
    'first_activity', MIN(created_at),
    'last_activity', MAX(created_at),
    'activity_counts', (
      SELECT jsonb_object_agg(activity_type, activity_count)
      FROM (
        SELECT activity_type, COUNT(*) as activity_count
        FROM user_activities
        WHERE user_id = user_uuid
        GROUP BY activity_type
      ) as activity_counts
    ),
    'recent_activities', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'activity_type', activity_type,
          'created_at', created_at,
          'activity_data', activity_data
        )
      )
      FROM (
        SELECT activity_type, created_at, activity_data
        FROM user_activities
        WHERE user_id = user_uuid
        ORDER BY created_at DESC
        LIMIT 10
      ) as recent
    )
  )
  INTO result
  FROM user_activities
  WHERE user_id = user_uuid;

  RETURN COALESCE(result, '{}'::jsonb);
END;
$$;

-- Fonction pour enregistrer une activité générique
CREATE OR REPLACE FUNCTION record_user_activity(
  activity_type_param text,
  activity_data_param jsonb DEFAULT '{}'::jsonb,
  metadata_param jsonb DEFAULT '{}'::jsonb
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_id uuid;
BEGIN
  -- Vérifier que l'utilisateur est authentifié
  IF auth.uid() IS NULL THEN
    RAISE EXCEPTION 'Utilisateur non authentifié';
  END IF;

  -- Insérer l'activité
  INSERT INTO user_activities (user_id, activity_type, activity_data, metadata)
  VALUES (auth.uid(), activity_type_param, activity_data_param, metadata_param)
  RETURNING id INTO new_id;

  RETURN new_id;
END;
$$;

-- Créer une vue pour faciliter l'analyse des activités par l'IA
CREATE OR REPLACE VIEW ai_user_activity_analysis AS
SELECT 
  p.id as user_id,
  p.email,
  p.first_name,
  p.last_name,
  p.role,
  jsonb_build_object(
    'total_activities', COUNT(ua.id),
    'activity_summary', (
      SELECT jsonb_object_agg(activity_type, activity_count)
      FROM (
        SELECT activity_type, COUNT(*) as activity_count
        FROM user_activities
        WHERE user_id = p.id
        GROUP BY activity_type
      ) as activity_counts
    ),
    'first_activity', MIN(ua.created_at),
    'last_activity', MAX(ua.created_at),
    'activity_frequency', (
      SELECT jsonb_build_object(
        'daily', COUNT(DISTINCT DATE(ua2.created_at)) / 
                GREATEST(1, EXTRACT(DAY FROM NOW() - MIN(ua2.created_at))),
        'weekly', COUNT(DISTINCT DATE_TRUNC('week', ua2.created_at)) / 
                 GREATEST(1, EXTRACT(WEEK FROM NOW() - MIN(ua2.created_at)))
      )
      FROM user_activities ua2
      WHERE ua2.user_id = p.id
    ),
    'engagement_score', (
      CASE 
        WHEN COUNT(ua.id) = 0 THEN 0
        ELSE (
          COUNT(ua.id) * 0.3 + 
          COUNT(DISTINCT DATE(ua.created_at)) * 0.4 +
          COUNT(DISTINCT ua.activity_type) * 0.3
        ) / 10.0
      END
    )::numeric(3,1)
  ) as activity_metrics
FROM 
  profiles p
LEFT JOIN 
  user_activities ua ON p.id = ua.user_id
GROUP BY 
  p.id, p.email, p.first_name, p.last_name, p.role;

-- Sécuriser la vue
ALTER VIEW ai_user_activity_analysis OWNER TO postgres;
GRANT SELECT ON ai_user_activity_analysis TO authenticated;

-- Créer une fonction pour accéder à la vue avec contrôle d'accès
CREATE OR REPLACE FUNCTION get_user_activity_analysis(user_uuid uuid DEFAULT NULL)
RETURNS SETOF ai_user_activity_analysis
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Vérifier si l'utilisateur est un admin ou demande ses propres données
  IF (
    auth.uid() = user_uuid 
    OR 
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
    OR
    user_uuid IS NULL AND EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'admin'
    )
  ) THEN
    IF user_uuid IS NULL THEN
      -- Admin demande toutes les données
      RETURN QUERY SELECT * FROM ai_user_activity_analysis;
    ELSE
      -- Utilisateur demande ses propres données ou admin demande des données spécifiques
      RETURN QUERY SELECT * FROM ai_user_activity_analysis WHERE user_id = user_uuid;
    END IF;
  ELSE
    -- Non autorisé
    RAISE EXCEPTION 'Non autorisé à accéder à ces données';
  END IF;
END;
$$;

COMMENT ON TABLE user_activities IS 'Stocke toutes les activités des utilisateurs pour l''analyse et le suivi';
COMMENT ON COLUMN user_activities.activity_type IS 'Type d''activité (login, forum_post, etc.)';
COMMENT ON COLUMN user_activities.activity_data IS 'Données spécifiques à l''activité au format JSON';
COMMENT ON COLUMN user_activities.metadata IS 'Métadonnées supplémentaires comme l''appareil, l''IP, etc.'; 