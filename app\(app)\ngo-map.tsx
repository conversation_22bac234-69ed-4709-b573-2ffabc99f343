import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Linking,
  Platform,
  Alert,
  Dimensions,
  FlatList,
  StatusBar,
  Animated,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Stack } from 'expo-router';
import { useTheme } from '../../context/theme';
import { useAuth } from '../../context/auth';
import { supabase } from '../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import MapView, { Marker, <PERSON>yline, PROVIDER_GOOGLE } from '../../components/Map';
import { recordActivity } from '../../lib/activity-tracking';

const { width } = Dimensions.get('window');

// Types pour les ONG
interface NGO {
  id: string;
  name: string;
  description: string;
  type: 'juridique' | 'sociale' | 'medicale' | 'autre';
  latitude: number;
  longitude: number;
  address: string;
  city: string;
  phone: string;
  email: string;
  services: Record<string, boolean>;
  logo_url?: string;
  distance?: number; // Distance calculée par rapport à l'utilisateur
}

// Type pour les filtres
type FilterType = 'all' | 'juridique' | 'sociale' | 'medicale';

// Type pour les coordonnées de l'itinéraire
type RouteCoordinate = {
  latitude: number;
  longitude: number;
};

// Ajoutez ce type pour les modes de transport
type TransportMode = 'walking' | 'driving';

// Déplaçons la déclaration des styles à l'extérieur de la fonction du composant
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  mapContainer: {
    flex: 3, // Réduire encore plus le ratio pour donner plus d'espace à la liste
    position: 'relative',
    minHeight: Dimensions.get('window').height < 700 ? 180 : 200, // Adapter la hauteur minimale en fonction de la taille de l'écran
    maxHeight: Dimensions.get('window').height < 700 ? 200 : 250, // Adapter la hauteur maximale en fonction de la taille de l'écran
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  mapControls: {
    position: 'absolute',
    right: 16,
    top: 16,
    gap: 10,
  },
  mapControlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  filterScrollView: {
    position: 'absolute',
    bottom: 20, // Placer les filtres en bas de la carte pour une meilleure visibilité
    left: 0,
    right: 0,
    maxHeight: 60,
  },
  filterContainer: {
    paddingHorizontal: 16,
    gap: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 25,
    backgroundColor: 'rgba(255,255,255,0.95)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3,
    elevation: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterButtonActive: {
    backgroundColor: '#007AFF',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginLeft: 6,
  },
  filterTextActive: {
    color: '#fff',
  },
  collapseButton: {
    alignItems: 'center',
    paddingTop: 8,
    paddingBottom: 3,
  },
  dragHandle: {
    width: 36,
    height: 4,
    backgroundColor: 'rgba(0,0,0,0.15)',
    borderRadius: 2,
    marginBottom: 2,
  },
  ngoListWrapper: {
    flex: 11, // Augmenter encore plus le ratio pour donner plus d'espace à la liste
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 5,
    paddingTop: 5, // Réduire le padding du haut
    minHeight: 350, // Augmenter davantage la hauteur minimale
    marginTop: -20, // Chevaucher davantage la carte pour réduire l'espace vide
    marginBottom: -1, // Étendre la liste jusqu'au bord inférieur de l'écran
  },
  ngoListHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 2, // Réduire la marge en bas
    marginTop: 5, // Ajouter une marge en haut
  },
  ngoListTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  ngoListBadge: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginLeft: 8,
  },
  ngoListBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  ngoListSubtitle: {
    fontSize: 11, // Réduire la taille de la police
    opacity: 0.6,
    marginHorizontal: 16,
    marginBottom: 5, // Réduire la marge en bas
  },
  ngoList: {
    paddingHorizontal: 16,
    flex: 1, // Utiliser flex pour que la liste prenne tout l'espace disponible
    marginTop: 5, // Ajouter une marge en haut pour réduire l'espace entre le titre et la liste
  },
  ngoListContent: {
    paddingBottom: Platform.OS === 'ios' ? 90 : 70, // Augmenter significativement le padding en bas pour éviter que le dernier élément soit caché par la barre de navigation
    flexGrow: 1, // Permettre au contenu de la liste de s'étendre
  },
  ngoListItem: {
    flexDirection: 'row',
    padding: 12, // Réduire le padding pour rendre les éléments plus compacts
    borderRadius: 12,
    marginBottom: 8, // Réduire la marge entre les éléments
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
    alignItems: 'center',
  },
  ngoListItemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ngoListItemContent: {
    flex: 1,
    marginLeft: 10,
  },
  ngoListItemName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  ngoListItemDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    marginBottom: 2,
  },
  ngoTypeTag: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    marginRight: 8,
  },
  ngoTypeTagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  ngoListItemDistance: {
    fontSize: 12,
    opacity: 0.7,
  },
  ngoListItemAddress: {
    fontSize: 12,
    marginTop: 2,
    opacity: 0.6,
  },
  ngoListItemArrow: {
    marginLeft: 10,
  },
  ngoDetails: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    maxHeight: '80%', // Augmenter la hauteur maximale
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 5,
    zIndex: 1000, // S'assurer que les détails sont au-dessus des autres éléments
  },
  ngoDetailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 15,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  ngoName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 5,
  },
  ngoDetailsContent: {
    padding: 20,
    maxHeight: '100%',
    paddingBottom: Platform.OS === 'ios' ? 90 : 70, // Ajouter un padding en bas pour éviter que le contenu soit caché par la barre de navigation
  },
  ngoType: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 10,
  },
  ngoDescription: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 15,
  },
  ngoContact: {
    marginBottom: 15,
  },
  ngoContactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  ngoContactText: {
    marginLeft: 10,
    fontSize: 14,
  },
  directionsInfo: {
    marginBottom: 15,
    padding: 15,
    borderRadius: 12,
    backgroundColor: 'rgba(0,0,0,0.03)',
  },
  directionsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  directionsDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  directionsItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  directionsText: {
    marginLeft: 5,
    fontSize: 14,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 5,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.7)',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  transportModeSelector: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 10,
  },
  transportModeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginHorizontal: 5,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  transportModeOptionActive: {
    backgroundColor: '#007AFF',
  },
  transportModeText: {
    marginLeft: 5,
    fontSize: 12,
    fontWeight: '500',
  },
  markerContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 5,
  },
  markerText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  destinationMarker: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default function NGOMapScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const insets = useSafeAreaInsets(); // Obtenir les zones de sécurité de l'écran
  const mapRef = useRef<MapView>(null);
  const [userLocation, setUserLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [ngos, setNgos] = useState<NGO[]>([]);
  const [filteredNgos, setFilteredNgos] = useState<NGO[]>([]);
  const [selectedNgo, setSelectedNgo] = useState<NGO | null>(null);
  const [filter, setFilter] = useState<FilterType>('all');
  const [loading, setLoading] = useState(true);
  const [routeCoordinates, setRouteCoordinates] = useState<RouteCoordinate[]>([]);
  const [routeDistance, setRouteDistance] = useState<string | null>(null);
  const [routeDuration, setRouteDuration] = useState<string | null>(null);
  const [showDirections, setShowDirections] = useState(false);
  const [mapType, setMapType] = useState<'standard' | 'satellite'>('standard');
  const [transportMode, setTransportMode] = useState<TransportMode>('walking');
  const [isListCollapsed, setIsListCollapsed] = useState(false);
  const listHeightAnim = useRef(new Animated.Value(1)).current; // 1 pour ouvert, 0 pour réduit

  // Récupérer la localisation de l'utilisateur
  useEffect(() => {
    const getUserLocation = async () => {
      try {
        const { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission refusée', 'Nous avons besoin de votre localisation pour afficher les ONG à proximité.');
          setLoading(false);
          return;
        }

        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Balanced,
        });

        setUserLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });
      } catch (error) {
        console.error('Erreur lors de la récupération de la localisation:', error);
        Alert.alert('Erreur', 'Impossible de récupérer votre localisation. Veuillez réessayer.');
      } finally {
        setLoading(false);
      }
    };

    getUserLocation();
  }, []);

  // Récupérer les ONG depuis Supabase
  useEffect(() => {
    const fetchNGOs = async () => {
      try {
        const { data, error } = await supabase
          .from('ngos')
          .select('*')
          .eq('is_active', true);

        if (error) {
          throw error;
        }

        if (data) {
          // Si aucune donnée n'est trouvée, utiliser des données de démonstration
          if (data.length === 0) {
            const demoData = getDemoNGOs();
            setNgos(demoData);
            setFilteredNgos(demoData);
          } else {
            // Calculer la distance si la localisation de l'utilisateur est disponible
            let ngosWithDistance = data as NGO[];
            if (userLocation) {
              ngosWithDistance = data.map((ngo) => ({
                ...ngo,
                distance: calculateDistance(
                  userLocation.latitude,
                  userLocation.longitude,
                  ngo.latitude,
                  ngo.longitude
                ),
              }));
              // Trier par distance
              ngosWithDistance.sort((a, b) => (a.distance || 0) - (b.distance || 0));
            }
            setNgos(ngosWithDistance);
            setFilteredNgos(ngosWithDistance);
          }
        }
      } catch (error) {
        console.error('Erreur lors de la récupération des ONG:', error);
        // En cas d'erreur, utiliser des données de démonstration
        const demoData = getDemoNGOs();
        setNgos(demoData);
        setFilteredNgos(demoData);
      }
    };

    if (!loading) {
      fetchNGOs();
    }
  }, [userLocation, loading]);

  // Ajouter un effet pour enregistrer la visite de la page des ONG
  useEffect(() => {
    if (user) {
      recordActivity('page_view', { page: 'ngo_map' });
    }
  }, [user]);

  // Données de démonstration pour les ONG
  const getDemoNGOs = (): NGO[] => {
    // Utiliser la position de l'utilisateur comme référence
    const baseLatitude = userLocation ? userLocation.latitude : -4.3250;
    const baseLongitude = userLocation ? userLocation.longitude : 15.3222;

    return [
      {
        id: '1',
        name: 'Association pour la Protection des Femmes',
        description: 'Organisation dédiée à la protection des droits des femmes et à la lutte contre les violences basées sur le genre.',
        type: 'juridique',
        latitude: baseLatitude + 0.003,
        longitude: baseLongitude - 0.002,
        address: '123 Avenue de la Paix',
        city: 'Kinshasa',
        phone: '+243123456789',
        email: '<EMAIL>',
        services: { consultation_juridique: true, accompagnement_judiciaire: true, mediation_familiale: true },
        distance: 0.5
      },
      {
        id: '2',
        name: 'Centre Médical d\'Urgence',
        description: 'Centre médical spécialisé dans les soins d\'urgence pour les victimes de violence.',
        type: 'medicale',
        latitude: baseLatitude - 0.002,
        longitude: baseLongitude + 0.003,
        address: '45 Boulevard de la Santé',
        city: 'Kinshasa',
        phone: '+243987654321',
        email: '<EMAIL>',
        services: { soins_urgence: true, soutien_psychologique: true, examens_medicaux: true },
        distance: 0.7
      },
      {
        id: '3',
        name: 'Foyer d\'Accueil Temporaire',
        description: 'Structure d\'hébergement temporaire pour les femmes et enfants victimes de violence.',
        type: 'sociale',
        latitude: baseLatitude + 0.001,
        longitude: baseLongitude + 0.001,
        address: '78 Rue de la Solidarité',
        city: 'Kinshasa',
        phone: '+243567891234',
        email: '<EMAIL>',
        services: { hebergement: true, alimentation: true, accompagnement_social: true, reinsertion: true },
        distance: 0.3
      }
    ];
  };

  // Filtrer les ONG
  useEffect(() => {
    if (filter === 'all') {
      setFilteredNgos(ngos);
    } else {
      setFilteredNgos(ngos.filter((ngo) => ngo.type === filter));
    }
    // Réinitialiser la sélection et l'itinéraire
    setSelectedNgo(null);
    setRouteCoordinates([]);
    setShowDirections(false);
  }, [filter, ngos]);

  // Calculer la distance entre deux points (formule de Haversine)
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // Rayon de la Terre en km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // Obtenir l'itinéraire vers une ONG
  const getDirections = async (ngo: NGO) => {
    if (!userLocation) return;

    try {
      setLoading(true);

      // Simuler un itinéraire simple (ligne droite) entre l'utilisateur et l'ONG
      // Dans une application réelle, vous utiliseriez une API comme Google Directions API
      const simulatedRoute = [
        { latitude: userLocation.latitude, longitude: userLocation.longitude },
        { latitude: ngo.latitude, longitude: ngo.longitude },
      ];

      setRouteCoordinates(simulatedRoute);

      // Calculer la distance approximative
      const distance = calculateDistance(
        userLocation.latitude,
        userLocation.longitude,
        ngo.latitude,
        ngo.longitude
      );

      setRouteDistance(`${distance.toFixed(2)} km`);

      // Estimer la durée en fonction du mode de transport
      let durationMinutes;
      if (transportMode === 'walking') {
        // À pied ~ 5 km/h
        durationMinutes = Math.round((distance / 5) * 60);
      } else {
        // En voiture ~ 50 km/h en ville (estimation)
        durationMinutes = Math.round((distance / 50) * 60);
      }

      setRouteDuration(`${durationMinutes} min`);

      setShowDirections(true);

      // Ajuster la vue de la carte pour inclure l'itinéraire
      if (mapRef.current) {
        const region = {
          latitude: (userLocation.latitude + ngo.latitude) / 2,
          longitude: (userLocation.longitude + ngo.longitude) / 2,
          latitudeDelta: Math.abs(userLocation.latitude - ngo.latitude) * 1.5,
          longitudeDelta: Math.abs(userLocation.longitude - ngo.longitude) * 1.5,
        };

        mapRef.current.animateToRegion(region, 1000);
      }
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'itinéraire:', error);
      Alert.alert('Erreur', 'Impossible de récupérer l\'itinéraire. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  // Ouvrir l'application de navigation
  const openNavigation = (ngo: NGO) => {
    if (!userLocation) return;

    const scheme = Platform.select({ ios: 'maps:', android: 'geo:' });
    const latLng = `${ngo.latitude},${ngo.longitude}`;
    const label = ngo.name;
    const url = Platform.select({
      ios: `${scheme}q=${label}&ll=${latLng}&dirflg=${transportMode === 'walking' ? 'w' : 'd'}`,
      android: `${scheme}0,0?q=${latLng}(${label})&mode=${transportMode === 'walking' ? 'walking' : 'driving'}`
    });

    if (url) {
      Linking.openURL(url).catch(err => {
        console.error('Erreur lors de l\'ouverture de l\'application de navigation:', err);
        Alert.alert('Erreur', 'Impossible d\'ouvrir l\'application de navigation.');
      });
    }
  };

  // Modifier la fonction d'appel d'ONG pour enregistrer l'activité
  const callNgo = (phone: string) => {
    if (!phone) return;

    if (selectedNgo) {
      // Enregistrer l'activité de contact d'ONG
      if (user) {
        recordActivity('ngo_contacted', {
          ngo_id: selectedNgo.id,
          ngo_name: selectedNgo.name,
          contact_method: 'phone'
        });
      }

      const url = `tel:${phone}`;
      Linking.openURL(url).catch(err => {
        console.error('Erreur lors de l\'appel:', err);
        Alert.alert('Erreur', 'Impossible de passer l\'appel.');
      });
    }
  };

  // Sélectionner une ONG
  const selectNgo = (ngo: NGO) => {
    setSelectedNgo(ngo);
    getDirections(ngo);
  };

  // Fermer les détails de l'ONG
  const closeNgoDetails = () => {
    setSelectedNgo(null);
    setRouteCoordinates([]);
    setShowDirections(false);
    setRouteDistance(null);
    setRouteDuration(null);
  };

  // Obtenir la couleur du marqueur en fonction du type d'ONG
  const getMarkerColor = (type: NGO['type']) => {
    switch (type) {
      case 'juridique':
        return '#4285F4';
      case 'medicale':
        return '#EA4335';
      case 'sociale':
        return '#34A853';
      default:
        return '#FBBC05';
    }
  };

  // Obtenir l'icône en fonction du type d'ONG
  const getNgoTypeIcon = (type: NGO['type'], size = 18, color = '#fff') => {
    switch (type) {
      case 'juridique':
        return <Ionicons name="scale" size={size} color={color} />;
      case 'medicale':
        return <Ionicons name="medical" size={size} color={color} />;
      case 'sociale':
        return <Ionicons name="people" size={size} color={color} />;
      default:
        return <Ionicons name="business" size={size} color={color} />;
    }
  };

  // Rendu du composant de marqueur personnalisé
  const renderCustomMarker = (type: NGO['type']) => {
    return (
      <View style={[styles.markerContainer, { backgroundColor: getMarkerColor(type) }]}>
        {getNgoTypeIcon(type, 16, '#fff')}
      </View>
    );
  };

  // Fonction pour changer le mode de transport
  const toggleTransportMode = () => {
    const newMode = transportMode === 'walking' ? 'driving' : 'walking';
    setTransportMode(newMode);

    // Recalculer l'itinéraire si un ONG est sélectionnée
    if (selectedNgo) {
      getDirections(selectedNgo);
    }
  };

  // Fonction pour réduire/agrandir la liste des ONGs
  const toggleListCollapse = () => {
    setIsListCollapsed(!isListCollapsed);

    // Animation fluide pour la transition
    Animated.timing(listHeightAnim, {
      toValue: isListCollapsed ? 1 : 0,
      duration: 300,
      useNativeDriver: false
    }).start();

    // Si on réduit la liste et qu'une ONG est sélectionnée, centrer la carte sur cette ONG
    if (!isListCollapsed && selectedNgo && mapRef.current) {
      setTimeout(() => {
        mapRef.current?.animateToRegion({
          latitude: selectedNgo.latitude,
          longitude: selectedNgo.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }, 500);
      }, 100);
    }
  };

  if (loading && !userLocation) {
    return (
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <Stack.Screen
          options={{
            headerShown: true,
            title: "ONGs à proximité",
            headerTitleStyle: {
              fontWeight: 'bold',
            },
            headerShadowVisible: false,
          }}
        />
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.loadingText, { color: theme.colors.text }]}>
          Chargement de la carte...
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, {
      // Supprimer le padding en bas pour éliminer l'espace vide
      paddingBottom: 0
    }]}>
      <StatusBar barStyle={theme.dark ? "light-content" : "dark-content"} />
      <Stack.Screen
        options={{
          headerShown: true,
          title: "ONGs à proximité",
          headerTitleStyle: {
            fontWeight: 'bold',
          },
          headerShadowVisible: false,
          presentation: 'modal',
        }}
      />

      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          style={styles.map}
          initialRegion={userLocation ? {
            latitude: userLocation.latitude,
            longitude: userLocation.longitude,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          } : undefined}
          showsUserLocation
          showsMyLocationButton={false}
          showsCompass={true}
          mapType={mapType}
        >
          {userLocation && (
            <Marker
              coordinate={{
                latitude: userLocation.latitude,
                longitude: userLocation.longitude,
              }}
              title="Ma position"
              pinColor="blue"
            />
          )}

          {filteredNgos.map((ngo) => (
            <Marker
              key={ngo.id}
              coordinate={{
                latitude: ngo.latitude,
                longitude: ngo.longitude,
              }}
              onPress={() => selectNgo(ngo)}
            >
              {renderCustomMarker(ngo.type)}
            </Marker>
          ))}

          {showDirections && routeCoordinates.length > 0 && (
            <>
              <Polyline
                coordinates={routeCoordinates}
                strokeWidth={5}
                strokeColor={transportMode === 'walking' ? '#4285F4' : '#34A853'}
                lineDashPattern={transportMode === 'walking' ? [1, 3] : undefined}
                lineCap="round"
                lineJoin="round"
              />
              {/* Ajouter un marqueur de départ et d'arrivée pour le trajet */}
              {routeCoordinates.length > 1 && (
                <Marker
                  coordinate={routeCoordinates[routeCoordinates.length - 1]}
                  anchor={{ x: 0.5, y: 1 }}
                >
                  <View style={styles.destinationMarker}>
                    <Ionicons name="location" size={24} color="#EA4335" />
                  </View>
                </Marker>
              )}
            </>
          )}
        </MapView>

        {/* Contrôles de carte repositionnés */}
        <View style={styles.mapControls}>
          <TouchableOpacity
            style={styles.mapControlButton}
            onPress={() => {
              const nextType = mapType === 'standard' ? 'satellite' : 'standard';
              setMapType(nextType);
            }}
          >
            <Ionicons
              name={mapType === 'standard' ? 'map' : 'earth'}
              size={22}
              color="#fff"
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.mapControlButton}
            onPress={toggleTransportMode}
          >
            <Ionicons
              name={transportMode === 'walking' ? 'walk' : 'car'}
              size={22}
              color="#fff"
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.mapControlButton}
            onPress={() => {
              if (userLocation && mapRef.current) {
                mapRef.current.animateToRegion({
                  latitude: userLocation.latitude,
                  longitude: userLocation.longitude,
                  latitudeDelta: 0.05,
                  longitudeDelta: 0.05,
                }, 1000);
              }
            }}
          >
            <Ionicons name="locate" size={22} color="#fff" />
          </TouchableOpacity>
        </View>

        {/* Filtres repositionnés */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.filterScrollView}
          contentContainerStyle={styles.filterContainer}
        >
          <TouchableOpacity
            style={[
              styles.filterButton,
              filter === 'all' && styles.filterButtonActive
            ]}
            onPress={() => setFilter('all')}
          >
            <Ionicons
              name="apps"
              size={18}
              color={filter === 'all' ? '#fff' : '#333'}
            />
            <Text style={[
              styles.filterText,
              filter === 'all' && styles.filterTextActive
            ]}>Tous</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              filter === 'juridique' && styles.filterButtonActive
            ]}
            onPress={() => setFilter('juridique')}
          >
            <Ionicons
              name="scale"
              size={18}
              color={filter === 'juridique' ? '#fff' : '#4285F4'}
            />
            <Text style={[
              styles.filterText,
              filter === 'juridique' && styles.filterTextActive
            ]}>Juridique</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              filter === 'sociale' && styles.filterButtonActive
            ]}
            onPress={() => setFilter('sociale')}
          >
            <Ionicons
              name="people"
              size={18}
              color={filter === 'sociale' ? '#fff' : '#34A853'}
            />
            <Text style={[
              styles.filterText,
              filter === 'sociale' && styles.filterTextActive
            ]}>Sociale</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              filter === 'medicale' && styles.filterButtonActive
            ]}
            onPress={() => setFilter('medicale')}
          >
            <Ionicons
              name="medical"
              size={18}
              color={filter === 'medicale' ? '#fff' : '#EA4335'}
            />
            <Text style={[
              styles.filterText,
              filter === 'medicale' && styles.filterTextActive
            ]}>Médicale</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      {/* Liste des ONGs en bas de l'écran avec défilement */}
      <Animated.View style={[
        styles.ngoListWrapper,
        {
          flex: listHeightAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [isListCollapsed ? 2 : 3, 11] // Flex réduit quand collé, normal quand ouvert
          }),
          minHeight: listHeightAnim.interpolate({
            inputRange: [0, 1],
            outputRange: [80, 350] // Hauteur minimale réduite quand collé
          })
        }
      ]}>
        <TouchableOpacity
          style={styles.collapseButton}
          onPress={toggleListCollapse}
          activeOpacity={0.7}
        >
          <View style={styles.dragHandle} />
          <Ionicons
            name={isListCollapsed ? "chevron-up" : "chevron-down"}
            size={20}
            color="rgba(0,0,0,0.3)"
            style={{marginTop: 2}}
          />
        </TouchableOpacity>
        <View style={styles.ngoListHeader}>
          <Text style={[styles.ngoListTitle, { color: theme.colors.text }]}>
            ONGs à proximité
          </Text>
          <View style={styles.ngoListBadge}>
            <Text style={styles.ngoListBadgeText}>{filteredNgos.length}</Text>
          </View>
        </View>
        <Text style={[styles.ngoListSubtitle, { color: theme.colors.text }]}>
          Faites défiler pour voir toutes les organisations disponibles
        </Text>
        <FlatList
          data={filteredNgos}
          keyExtractor={(item) => item.id}
          style={styles.ngoList}
          contentContainerStyle={styles.ngoListContent}
          showsVerticalScrollIndicator={false}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[styles.ngoListItem, { backgroundColor: theme.colors.cardBackground || theme.colors.background }]}
              onPress={() => selectNgo(item)}
            >
              <View style={[styles.ngoListItemIcon, { backgroundColor: getMarkerColor(item.type) }]}>
                {getNgoTypeIcon(item.type)}
              </View>
              <View style={styles.ngoListItemContent}>
                <Text style={[styles.ngoListItemName, { color: theme.colors.text }]}>{item.name}</Text>
                <View style={styles.ngoListItemDetails}>
                  <View style={[styles.ngoTypeTag, { backgroundColor: getMarkerColor(item.type) + '20' }]}>
                    <Text style={[styles.ngoTypeTagText, { color: getMarkerColor(item.type) }]}>
                      {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                    </Text>
                  </View>
                  <Text style={[styles.ngoListItemDistance, { color: theme.colors.text }]}>
                    {item.distance ? `${item.distance.toFixed(1)} km` : 'Distance inconnue'}
                  </Text>
                </View>
                <Text style={[styles.ngoListItemAddress, { color: theme.colors.text }]} numberOfLines={1}>
                  {item.address}, {item.city}
                </Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color={theme.colors.text} style={styles.ngoListItemArrow} />
            </TouchableOpacity>
          )}
        />
      </Animated.View>

      {/* Détails de l'ONG sélectionnée */}
      {selectedNgo && (
        <View style={[styles.ngoDetails, { backgroundColor: theme.colors.cardBackground || theme.colors.background }]}>
          <View style={styles.ngoDetailsHeader}>
            <Text style={[styles.ngoName, { color: theme.colors.text }]}>{selectedNgo.name}</Text>
            <TouchableOpacity onPress={closeNgoDetails} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.ngoDetailsContent} showsVerticalScrollIndicator={false}>
            <Text style={[styles.ngoType, { color: getMarkerColor(selectedNgo.type) }]}>
              {selectedNgo.type.charAt(0).toUpperCase() + selectedNgo.type.slice(1)}
            </Text>

            <Text style={[styles.ngoDescription, { color: theme.colors.text }]}>
              {selectedNgo.description}
            </Text>

            <View style={styles.ngoContact}>
              <View style={styles.ngoContactItem}>
                <Ionicons name="location" size={20} color={theme.colors.primary} />
                <Text style={[styles.ngoContactText, { color: theme.colors.text }]}>
                  {selectedNgo.address}, {selectedNgo.city}
                </Text>
              </View>

              <View style={styles.ngoContactItem}>
                <Ionicons name="call" size={20} color={theme.colors.primary} />
                <Text
                  style={[styles.ngoContactText, { color: theme.colors.primary }]}
                  onPress={() => selectedNgo && callNgo(selectedNgo.phone)}
                >
                  {selectedNgo.phone}
                </Text>
              </View>

              <View style={styles.ngoContactItem}>
                <Ionicons name="mail" size={20} color={theme.colors.primary} />
                <Text style={[styles.ngoContactText, { color: theme.colors.text }]}>
                  {selectedNgo.email}
                </Text>
              </View>
            </View>

            {showDirections && routeDistance && routeDuration && (
              <View style={styles.directionsInfo}>
                <Text style={[styles.directionsTitle, { color: theme.colors.text }]}>
                  Itinéraire {transportMode === 'walking' ? 'à pied' : 'en voiture'}
                </Text>
                <View style={styles.directionsDetails}>
                  <View style={styles.directionsItem}>
                    <Ionicons
                      name={transportMode === 'walking' ? 'walk' : 'car'}
                      size={18}
                      color={theme.colors.primary}
                    />
                    <Text style={[styles.directionsText, { color: theme.colors.text }]}>
                      {routeDistance}
                    </Text>
                  </View>
                  <View style={styles.directionsItem}>
                    <Ionicons name="time" size={18} color={theme.colors.primary} />
                    <Text style={[styles.directionsText, { color: theme.colors.text }]}>
                      {routeDuration}
                    </Text>
                  </View>
                </View>

                <View style={styles.transportModeSelector}>
                  <TouchableOpacity
                    style={[
                      styles.transportModeOption,
                      transportMode === 'walking' && styles.transportModeOptionActive
                    ]}
                    onPress={() => {
                      if (transportMode !== 'walking') {
                        setTransportMode('walking');
                        if (selectedNgo) getDirections(selectedNgo);
                      }
                    }}
                  >
                    <Ionicons
                      name="walk"
                      size={18}
                      color={transportMode === 'walking' ? '#fff' : theme.colors.text}
                    />
                    <Text
                      style={[
                        styles.transportModeText,
                        { color: transportMode === 'walking' ? '#fff' : theme.colors.text }
                      ]}
                    >
                      À pied
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.transportModeOption,
                      transportMode === 'driving' && styles.transportModeOptionActive
                    ]}
                    onPress={() => {
                      if (transportMode !== 'driving') {
                        setTransportMode('driving');
                        if (selectedNgo) getDirections(selectedNgo);
                      }
                    }}
                  >
                    <Ionicons
                      name="car"
                      size={18}
                      color={transportMode === 'driving' ? '#fff' : theme.colors.text}
                    />
                    <Text
                      style={[
                        styles.transportModeText,
                        { color: transportMode === 'driving' ? '#fff' : theme.colors.text }
                      ]}
                    >
                      En voiture
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}

            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
                onPress={() => selectedNgo && getDirections(selectedNgo)}
              >
                <Text style={styles.actionButtonText}>Itinéraire</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: '#4CAF50' }]}
                onPress={() => selectedNgo && openNavigation(selectedNgo)}
              >
                <Text style={styles.actionButtonText}>Navigation</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, { backgroundColor: '#2196F3' }]}
                onPress={() => selectedNgo && callNgo(selectedNgo.phone)}
              >
                <Text style={styles.actionButtonText}>Appeler</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        </View>
      )}

      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Chargement...</Text>
        </View>
      )}
    </View>
  );
}
