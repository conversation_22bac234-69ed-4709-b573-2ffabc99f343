import { Stack } from 'expo-router';
import { Platform } from 'react-native';

export default function AppointmentsLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: true,
        headerStyle: {
          backgroundColor: '#fff',
        },
        headerTintColor: '#000',
        headerTitleStyle: {
          fontWeight: '600',
        },
        animation: Platform.OS === 'ios' ? 'default' : 'slide_from_right',
        presentation: 'card',
      }}
    >
      <Stack.Screen 
        name="index" 
        options={{
          title: "Rendez-vous",
          headerLargeTitle: Platform.OS === 'ios',
        }}
      />
      <Stack.Screen 
        name="book" 
        options={{
          title: "Prendre un rendez-vous",
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen 
        name="history" 
        options={{
          title: "Historique",
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen 
        name="video-call" 
        options={{
          title: "Consultation vidéo",
          presentation: 'fullScreenModal',
          animation: 'slide_from_bottom',
        }}
      />
      <Stack.Screen 
        name="[id]" 
        options={{
          title: "Détails du rendez-vous",
          animation: 'slide_from_right',
        }}
      />
    </Stack>
  );
}