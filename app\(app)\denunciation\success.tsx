import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import { router } from 'expo-router';
import { useTheme } from '../../../context/theme';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeIn, FadeInDown } from 'react-native-reanimated';

export default function DenunciationSuccessScreen() {
  const { theme } = useTheme();

  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.contentContainer}
    >
      <Animated.View 
        style={styles.card}
        entering={FadeIn.duration(800)}
      >
        <View style={styles.iconContainer}>
          <Ionicons name="checkmark-circle" size={80} color={theme.colors.success} />
        </View>
        
        <Animated.Text 
          style={[styles.title, { color: theme.colors.text }]}
          entering={FadeInDown.duration(800).delay(300)}
        >
          Signalement envoyé
        </Animated.Text>
        
        <Animated.Text 
          style={[styles.description, { color: theme.colors.gray[600] }]}
          entering={FadeInDown.duration(800).delay(500)}
        >
          Votre signalement a été envoyé avec succès. Nous vous remercions pour votre contribution à la lutte contre les violences.
        </Animated.Text>
        
        <Animated.View
          style={styles.infoContainer}
          entering={FadeInDown.duration(800).delay(700)}
        >
          <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
            <Ionicons name="shield" size={24} color={theme.colors.bomoko.blue} />
            <Text style={[styles.infoText, { color: theme.colors.text }]}>
              Votre signalement est traité de manière confidentielle
            </Text>
          </View>
          
          <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
            <Ionicons name="people" size={24} color={theme.colors.bomoko.purple} />
            <Text style={[styles.infoText, { color: theme.colors.text }]}>
              Des professionnels qualifiés examineront votre signalement
            </Text>
          </View>
        </Animated.View>
        
        <Animated.View
          style={styles.buttonsContainer}
          entering={FadeInDown.duration(800).delay(900)}
        >
          <TouchableOpacity
            style={[styles.button, { backgroundColor: theme.colors.bomoko.blue }]}
            onPress={() => router.push('/(tabs)')}
          >
            <Text style={styles.buttonText}>Retour à l'accueil</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, { backgroundColor: theme.colors.bomoko.purple }]}
            onPress={() => router.push('/denunciation')}
          >
            <Text style={styles.buttonText}>Nouveau signalement</Text>
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingTop: 32,
    paddingBottom: 40,
    alignItems: 'center',
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 500,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  infoContainer: {
    width: '100%',
    marginBottom: 24,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    marginLeft: 12,
    flex: 1,
  },
  buttonsContainer: {
    width: '100%',
  },
  button: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
