/*
  # Enhance webhook management and error handling

  1. Changes
    - Add webhook health check functionality
    - Add webhook status tracking
    - Add webhook availability monitoring
    - Add webhook retry configuration

  2. Security
    - Maintain existing RLS policies
    - Add new policies for webhook management
*/

-- Add webhook health check columns
ALTER TABLE webhook_urls
ADD COLUMN IF NOT EXISTS health_check_enabled BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS health_check_interval INTEGER DEFAULT 300, -- 5 minutes in seconds
ADD COLUMN IF NOT EXISTS last_successful_response TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS consecutive_failures INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS availability_threshold FLOAT DEFAULT 0.95, -- 95% availability required
ADD COLUMN IF NOT EXISTS backup_url TEXT;

-- <PERSON>reate function to track webhook availability
CREATE OR REPLACE FUNCTION calculate_webhook_availability(webhook_id INTEGER)
RETURNS FLOAT AS $$
DECLARE
  total_checks INTEGER;
  successful_checks INTEGER;
BEGIN
  SELECT 
    COUNT(*),
    COUNT(*) FILTER (WHERE error_message IS NULL)
  INTO total_checks, successful_checks
  FROM webhook_health_logs
  WHERE webhook_url_id = webhook_id
  AND check_timestamp > NOW() - INTERVAL '24 hours';

  IF total_checks = 0 THEN
    RETURN 0;
  END IF;

  RETURN CAST(successful_checks AS FLOAT) / total_checks;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create webhook health logs table
CREATE TABLE IF NOT EXISTS webhook_health_logs (
  id SERIAL PRIMARY KEY,
  webhook_url_id INTEGER REFERENCES webhook_urls(id),
  check_timestamp TIMESTAMPTZ DEFAULT now(),
  status TEXT NOT NULL,
  response_time INTEGER, -- in milliseconds
  error_message TEXT,
  response_code INTEGER
);

-- Enable RLS on health logs
ALTER TABLE webhook_health_logs ENABLE ROW LEVEL SECURITY;

-- Create policy for health logs
CREATE POLICY "Allow read access to health logs for authenticated users"
  ON webhook_health_logs
  FOR SELECT
  TO authenticated
  USING (true);

-- Create function to update webhook status with health check
CREATE OR REPLACE FUNCTION update_webhook_health_status(
  webhook_id INTEGER,
  status TEXT,
  response_time INTEGER DEFAULT NULL,
  error_msg TEXT DEFAULT NULL,
  response_code INTEGER DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
  -- Insert health log
  INSERT INTO webhook_health_logs (
    webhook_url_id,
    status,
    response_time,
    error_message,
    response_code
  ) VALUES (
    webhook_id,
    status,
    response_time,
    error_msg,
    response_code
  );

  -- Update webhook status
  UPDATE webhook_urls
  SET 
    last_check_timestamp = CURRENT_TIMESTAMP,
    consecutive_failures = CASE 
      WHEN status = 'healthy' THEN 0 
      ELSE consecutive_failures + 1 
    END,
    last_successful_response = CASE 
      WHEN status = 'healthy' THEN CURRENT_TIMESTAMP 
      ELSE last_successful_response 
    END,
    is_active = CASE 
      WHEN status = 'healthy' THEN true
      WHEN consecutive_failures >= max_retries THEN false
      ELSE is_active
    END,
    error_message = error_msg
  WHERE id = webhook_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update existing webhook configuration
UPDATE webhook_urls
SET 
  health_check_enabled = true,
  health_check_interval = 300,
  backup_url = 'https://backup-n8n.onrender.com/webhook-test/chat'
WHERE name = 'n8n_chatbot_agent';