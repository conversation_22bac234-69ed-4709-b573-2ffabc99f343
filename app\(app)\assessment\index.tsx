import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from 'react-native';
import { router } from 'expo-router';
import { useTheme } from '../../../context/theme';
import { Ionicons } from '@expo/vector-icons';

export default function AssessmentScreen() {
  const { theme } = useTheme();

  const handleStartAssessment = () => {
    router.push('/assessment/conversations');
  };

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}>
          <Ionicons
            name="arrow-back"
            size={24}
            color={theme.colors.text}
          />
        </TouchableOpacity>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Évaluation Initiale
        </Text>
      </View>

      <View style={styles.content}>
        <View style={[styles.infoCard, { backgroundColor: theme.colors.surface }]}>
          <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
            Avant de commencer
          </Text>
          <Text style={[styles.infoText, { color: theme.colors.gray[600] }]}>
            Cette évaluation vous guidera à travers une série de questions pour mieux comprendre votre situation et vous offrir le soutien le plus adapté.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Important à savoir
          </Text>
          {importantPoints.map((point, index) => (
            <View key={index} style={styles.point}>
              <Ionicons
                name={point.icon}
                size={24}
                color={theme.colors.primary}
                style={styles.pointIcon}
              />
              <View style={styles.pointContent}>
                <Text style={[styles.pointTitle, { color: theme.colors.text }]}>
                  {point.title}
                </Text>
                <Text style={[styles.pointText, { color: theme.colors.gray[600] }]}>
                  {point.description}
                </Text>
              </View>
            </View>
          ))}
        </View>

        <TouchableOpacity
          style={[styles.startButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleStartAssessment}>
          <Text style={styles.startButtonText}>Commencer l'évaluation</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.emergencyButton, { backgroundColor: theme.colors.error }]}
          onPress={() => router.push('/(app)/(emergency)')}>
          <Ionicons name="alert-circle" size={24} color="white" />
          <Text style={styles.emergencyButtonText}>
            Accès rapide aux urgences
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const importantPoints = [
  {
    icon: 'time-outline' as const,
    title: 'À votre rythme',
    description: 'Vous pouvez faire des pauses et reprendre quand vous le souhaitez.',
  },
  {
    icon: 'shield-checkmark-outline' as const,
    title: 'Confidentialité',
    description: 'Vos réponses sont strictement confidentielles et sécurisées.',
  },
  {
    icon: 'hand-left-outline' as const,
    title: 'Vous gardez le contrôle',
    description: 'Vous pouvez choisir de ne pas répondre à certaines questions.',
  },
  {
    icon: 'help-buoy-outline' as const,
    title: 'Soutien disponible',
    description: 'Une aide professionnelle est disponible à tout moment.',
  },
];

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
  },
  backButton: {
    marginRight: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  content: {
    padding: 20,
  },
  infoCard: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 16,
    lineHeight: 24,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 15,
  },
  point: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  pointIcon: {
    marginRight: 15,
    marginTop: 2,
  },
  pointContent: {
    flex: 1,
  },
  pointTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  pointText: {
    fontSize: 14,
    lineHeight: 20,
  },
  startButton: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 15,
  },
  startButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  emergencyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 8,
    gap: 10,
  },
  emergencyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});