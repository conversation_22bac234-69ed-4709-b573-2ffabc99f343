import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import * as Location from 'expo-location';
import { useAuth } from './auth';

type LocationContextType = {
  address: string | null;
  coordinates: { latitude: number; longitude: number } | null;
  errorMsg: string | null;
  loading: boolean;
  refreshLocation: () => Promise<void>;
};

const LocationContext = createContext<LocationContextType | undefined>(undefined);

export function LocationProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const [address, setAddress] = useState<string | null>(null);
  const [coordinates, setCoordinates] = useState<{ latitude: number; longitude: number } | null>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const locationWatchRef = useRef<Location.LocationSubscription | null>(null);

  useEffect(() => {
    if (user) {
      startLocationTracking();
    }
    
    return () => {
      if (locationWatchRef.current) {
        locationWatchRef.current.remove();
      }
    };
  }, [user]);

  const startLocationTracking = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setErrorMsg('Permission de localisation refusée');
        setLoading(false);
        return;
      }

      // Arrêter l'abonnement précédent s'il existe
      if (locationWatchRef.current) {
        locationWatchRef.current.remove();
      }

      // Démarrer un nouvel abonnement pour suivre la position
      locationWatchRef.current = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.Balanced,
          distanceInterval: 10, // Mise à jour tous les 10 mètres
          timeInterval: 5000,   // Ou toutes les 5 secondes
        },
        (location) => {
          setCoordinates({
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          });
          updateAddress(location.coords);
        }
      );
    } catch (error) {
      console.error('Erreur de localisation:', error);
      setErrorMsg('Impossible d\'obtenir la localisation');
      setLoading(false);
    }
  };

  const updateAddress = async (coords: Location.LocationObjectCoords) => {
    try {
      const addressResponse = await Location.reverseGeocodeAsync({
        latitude: coords.latitude,
        longitude: coords.longitude,
      });

      if (addressResponse && addressResponse.length > 0) {
        const location = addressResponse[0];
        const formattedAddress = [
          location.street,
          location.city,
          location.region,
        ]
          .filter(Boolean)
          .join(', ');
        
        setAddress(formattedAddress || 'Adresse inconnue');
        setErrorMsg(null);
      } else {
        setAddress('Position approximative');
      }
      setLoading(false);
    } catch (error) {
      console.error('Erreur de géocodage:', error);
      setAddress('Position approximative');
      setLoading(false);
    }
  };

  const refreshLocation = async () => {
    setLoading(true);
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });
      
      setCoordinates({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });
      
      updateAddress(location.coords);
    } catch (error) {
      console.error('Erreur lors de l\'actualisation de la localisation:', error);
      setErrorMsg('Impossible d\'actualiser la localisation');
      setLoading(false);
    }
  };

  const value = {
    address,
    coordinates,
    errorMsg,
    loading,
    refreshLocation,
  };

  return (
    <LocationContext.Provider value={value}>
      {children}
    </LocationContext.Provider>
  );
}

export function useLocation() {
  const context = useContext(LocationContext);
  if (context === undefined) {
    throw new Error('useLocation must be used within a LocationProvider');
  }
  return context;
}