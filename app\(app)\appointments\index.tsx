import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
  FlatList,
  Animated,
} from 'react-native';
import { router, Stack } from 'expo-router';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { supabase } from '../../../lib/supabase';
import { recordActivity } from '../../../lib/activity-tracking';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

type Appointment = {
  id: string;
  professional: {
    id: string;
    type: 'doctor' | 'lawyer';
    speciality: string;
    user: {
      first_name: string;
      last_name: string;
    };
  };
  date_time: string;
  status: string;
  type: string;
};

// Types pour les rendez-vous
type AppointmentStatus = 'pending' | 'confirmed' | 'completed' | 'cancelled';

interface AppointmentType {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  provider: string;
  location: string;
  status: AppointmentStatus;
}

// Ajoutons le type pour les notifications au début du fichier
type AppointmentNotification = {
  id: string;
  type: 'new_appointment' | 'reminder' | 'cancelled' | 'rescheduled' | 'confirmation';
  title: string;
  message: string;
  read: boolean;
  created_at: string;
  appointment_id?: string;
  professional_name?: string;
  professional_avatar?: string;
  appointment_date?: string;
};

export default function AppointmentsScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const [selectedTab, setSelectedTab] = useState<'upcoming' | 'past'>('upcoming');
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Ajoutons des états pour les notifications
  const [hasNotifications, setHasNotifications] = useState(false);
  const [notifications, setNotifications] = useState<AppointmentNotification[]>([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [showNotificationsPanel, setShowNotificationsPanel] = useState(false);

  // Animations pour le panneau de notifications
  const notificationsPanelAnim = useRef(new Animated.Value(-400)).current;
  const notificationBackdropAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Charger les notifications de rendez-vous
  const fetchNotifications = async () => {
    try {
      // Dans une vraie implémentation, vous feriez un appel API pour récupérer les notifications
      // Simulation de données de notification pour démonstration
      const mockNotifications: AppointmentNotification[] = [
        {
          id: '1',
          type: 'reminder',
          title: 'Rappel de rendez-vous',
          message: 'Vous avez un rendez-vous demain avec Dr. Mbaka',
          read: false,
          created_at: new Date(Date.now() - 3600000).toISOString(),
          appointment_id: '1',
          professional_name: 'Dr. Mbaka Jean',
          appointment_date: new Date(Date.now() + 86400000).toISOString()
        },
        {
          id: '2',
          type: 'new_appointment',
          title: 'Nouveau rendez-vous confirmé',
          message: 'Votre rendez-vous avec Mme. Mutombo a été confirmé',
          read: false,
          created_at: new Date(Date.now() - 86400000).toISOString(),
          appointment_id: '2',
          professional_name: 'Mme. Mutombo Sarah',
          appointment_date: new Date(Date.now() + 432000000).toISOString()
        },
        {
          id: '3',
          type: 'cancelled',
          title: 'Rendez-vous annulé',
          message: 'Votre rendez-vous du 15 mars a été annulé',
          read: true,
          created_at: new Date(Date.now() - 172800000).toISOString(),
          appointment_id: '4',
          professional_name: 'Me. Lukusa Ernest',
          appointment_date: new Date(Date.now() - 86400000).toISOString()
        }
      ];

      // Mettre à jour l'état des notifications
      setNotifications(mockNotifications);

      // Compter les notifications non lues
      const unreadCount = mockNotifications.filter(notif => !notif.read).length;
      setNotificationCount(unreadCount);

      // Définir si des notifications sont disponibles
      setHasNotifications(mockNotifications.length > 0);
    } catch (error) {
      console.error('Erreur lors du chargement des notifications:', error);
    }
  };

  useEffect(() => {
    loadAppointments();
    fetchNotifications(); // Charger les notifications

    // Vérifier périodiquement les nouvelles notifications
    const notificationsInterval = setInterval(() => {
      fetchNotifications();
    }, 60000);

    return () => {
      clearInterval(notificationsInterval);
    };
  }, []);

  // Ajouter un effet pour enregistrer la visite de la page des rendez-vous
  useEffect(() => {
    if (user) {
      recordActivity('page_view', { page: 'appointments' });
    }
  }, [user]);

  const loadAppointments = async () => {
    try {
      setLoading(true);
      setError(null);

      // Utiliser la fonction RPC get_user_appointments
      // La fonction RPC inclut déjà l'ordre par date_time ASC, donc pas besoin de .order()
      const { data, error: appointmentsError } = await supabase
        .rpc('get_user_appointments', { user_uuid: user?.id });

      if (appointmentsError) {
        console.error('Error calling get_user_appointments:', appointmentsError);
        // Fallback à la méthode manuelle en cas d'erreur
        await loadAppointmentsManually();
        return;
      }

      if (!data || data.length === 0) {
        setAppointments([]);
        return;
      }

      // Vérifier la structure des données retournées
      console.log('RPC data structure:', JSON.stringify(data[0]));

      // Formater les données pour correspondre au type Appointment
      const formattedAppointments = data.map((item: any) => {
        // S'assurer que toutes les valeurs sont des types primitifs
        const id = typeof item.id === 'string' ? item.id : String(item.id);
        const date_time = typeof item.date_time === 'string' ? item.date_time : String(item.date_time);
        const status = typeof item.status === 'string' ? item.status : String(item.status);
        const type = typeof item.type === 'string' ? item.type : String(item.type);
        const professional_id = typeof item.professional_id === 'string' ? item.professional_id : String(item.professional_id);
        const professional_type = typeof item.professional_type === 'string' ? item.professional_type : 'doctor';
        const professional_speciality = typeof item.professional_speciality === 'string' ? item.professional_speciality : 'Non spécifié';
        const professional_user_first_name = typeof item.professional_user_first_name === 'string' ? item.professional_user_first_name : 'Professionnel';
        const professional_user_last_name = typeof item.professional_user_last_name === 'string' ? item.professional_user_last_name : 'de santé';

        return {
          id,
          date_time,
          status,
          type,
          professional: {
            id: professional_id,
            type: professional_type as 'doctor' | 'lawyer',
            speciality: professional_speciality,
            user: {
              first_name: professional_user_first_name,
              last_name: professional_user_last_name
            }
          }
        };
      }) as Appointment[];

      setAppointments(formattedAppointments);
    } catch (err) {
      console.error('Error loading appointments:', err);
      setError('Erreur lors du chargement des rendez-vous');
      // Fallback à la méthode manuelle en cas d'erreur
      await loadAppointmentsManually();
    } finally {
      setLoading(false);
    }
  };

  // Méthode pour charger les rendez-vous manuellement
  const loadAppointmentsManually = async () => {
    try {
      const { data, error: appointmentsError } = await supabase
        .from('appointments')
        .select(`
          id,
          date_time,
          status,
          type,
          professional_id
        `)
        .eq('user_id', user?.id)
        .order('date_time', { ascending: true });

      if (appointmentsError) throw appointmentsError;

      if (!data || data.length === 0) {
        setAppointments([]);
        return;
      }

      // Créer des rendez-vous avec des informations minimales
      const formattedData = data.map(item => {
        // S'assurer que toutes les valeurs sont des chaînes de caractères
        const id = typeof item.id === 'string' ? item.id : String(item.id);
        const date_time = typeof item.date_time === 'string' ? item.date_time : String(item.date_time);
        const status = typeof item.status === 'string' ? item.status : String(item.status);
        const type = typeof item.type === 'string' ? item.type : String(item.type);
        const professional_id = typeof item.professional_id === 'string' ? item.professional_id : String(item.professional_id);

        return {
          id,
          date_time,
          status,
          type,
          professional: {
            id: professional_id,
            type: 'doctor' as 'doctor' | 'lawyer',
            speciality: 'Non spécifié',
            user: {
              first_name: 'Professionnel',
              last_name: 'de santé'
            }
          }
        };
      }) as Appointment[];

      setAppointments(formattedData);

      // Essayer de récupérer les informations des professionnels en arrière-plan
      // sans bloquer l'affichage des rendez-vous
      loadProfessionalDetails(data, formattedData);
    } catch (err) {
      console.error('Error in manual loading:', err);
      throw err;
    }
  };

  // Charger les détails des professionnels en arrière-plan
  const loadProfessionalDetails = async (appointmentsData: any[], initialAppointments: Appointment[]) => {
    try {
      const professionalIds = appointmentsData.map(item => item.professional_id);

      // Requête simple pour récupérer les professionnels sans utiliser de relations
      const { data: professionalsData, error: professionalsError } = await supabase
        .from('professionals')
        .select('id, type, speciality, user_id')
        .in('id', professionalIds);

      if (professionalsError || !professionalsData) {
        console.error('Error fetching professionals:', professionalsError);
        return; // Garder les données minimales
      }

      // Récupérer les user_ids des professionnels
      const userIds = professionalsData
        .map(prof => prof.user_id)
        .filter(id => id !== null && id !== undefined);

      // Si nous avons des user_ids, récupérer les informations des utilisateurs depuis profiles
      let profilesData: any[] = [];
      if (userIds.length > 0) {
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, first_name, last_name')
          .in('id', userIds);

        if (!profilesError && profiles) {
          profilesData = profiles;
        } else {
          console.error('Error fetching profiles:', profilesError);
        }
      }

      // Créer un dictionnaire pour un accès rapide aux données des profils
      const profilesById: Record<string, any> = {};
      profilesData.forEach(profile => {
        if (profile.id) {
          profilesById[profile.id] = profile;
        }
      });

      // Mettre à jour les rendez-vous avec les informations des professionnels et des profils
      const updatedAppointments = initialAppointments.map(appointment => {
        // Trouver le professionnel correspondant
        const professional = professionalsData.find(p => p.id === appointment.professional.id);
        if (!professional) return appointment;

        // S'assurer que toutes les valeurs sont des chaînes de caractères
        const professionalType = typeof professional.type === 'string'
          ? professional.type as 'doctor' | 'lawyer'
          : 'doctor';

        const speciality = typeof professional.speciality === 'string'
          ? professional.speciality
          : 'Non spécifié';

        // Récupérer les informations du profil si disponibles
        let firstName = 'Professionnel';
        let lastName = 'de santé';

        if (professional.user_id && profilesById[professional.user_id]) {
          const profile = profilesById[professional.user_id];
          firstName = profile.first_name || firstName;
          lastName = profile.last_name || lastName;
        }

        // Mettre à jour les informations du professionnel
        return {
          ...appointment,
          professional: {
            ...appointment.professional,
            type: professionalType,
            speciality: speciality,
            user: {
              first_name: firstName,
              last_name: lastName
            }
          }
        };
      });

      setAppointments(updatedAppointments);
    } catch (error) {
      console.error('Error loading professional details:', error);
      // Ne pas échouer, garder les données minimales
    }
  };

  const handleBookAppointment = () => {
    router.push('/appointments/book');
  };

  const handleViewHistory = () => {
    router.push('/appointments/history');
  };

  const formatAppointmentDate = (date: string) => {
    try {
      // Vérifier si la date est valide
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) {
        console.error('Invalid date:', date);
        return 'Date non disponible';
      }

      // Formater la date
      return format(dateObj, 'PPP à HH:mm');
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date non disponible';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return theme.colors.primary;
      case 'pending':
        return theme.colors.secondary;
      case 'cancelled':
        return theme.colors.error;
      default:
        return theme.colors.gray[500];
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'Confirmé';
      case 'pending':
        return 'En attente';
      case 'cancelled':
        return 'Annulé';
      case 'completed':
        return 'Terminé';
      default:
        return status;
    }
  };

  // Ouvrir le panneau de notifications avec animation
  const openNotificationsPanel = () => {
    setShowNotificationsPanel(true);
    Animated.parallel([
      Animated.timing(notificationsPanelAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true
      }),
      Animated.timing(notificationBackdropAnim, {
        toValue: 0.5,
        duration: 300,
        useNativeDriver: true
      })
    ]).start();
  };

  // Masquer le panneau de notifications avec animation
  const hideNotificationsPanel = () => {
    Animated.parallel([
      Animated.timing(notificationsPanelAnim, {
        toValue: -400,
        duration: 300,
        useNativeDriver: true
      }),
      Animated.timing(notificationBackdropAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true
      })
    ]).start(() => {
      setShowNotificationsPanel(false);
    });
  };

  // Naviguer vers les notifications
  const navigateToNotifications = () => {
    if (showNotificationsPanel) {
      hideNotificationsPanel();
    } else {
      openNotificationsPanel();
    }
  };

  // Marquer une notification comme lue
  const markNotificationAsRead = (notificationId: string) => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      )
    );

    // Mettre à jour le compteur de notifications non lues
    const updatedNotifications = notifications.map(notification =>
      notification.id === notificationId
        ? { ...notification, read: true }
        : notification
    );
    const unreadCount = updatedNotifications.filter(notif => !notif.read).length;
    setNotificationCount(unreadCount);
  };

  // Marquer toutes les notifications comme lues
  const markAllNotificationsAsRead = () => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notification => ({ ...notification, read: true }))
    );
    setNotificationCount(0);
  };

  // Gérer l'appui sur une notification
  const handleNotificationPress = (notification: AppointmentNotification) => {
    // Marquer la notification comme lue
    markNotificationAsRead(notification.id);

    // Si la notification concerne un rendez-vous, naviguer vers les détails du rendez-vous
    if (notification.appointment_id) {
      hideNotificationsPanel();
      // Attendre que le panneau soit fermé avant de naviguer
      setTimeout(() => {
        handleViewAppointmentDetails(notification.appointment_id!);
      }, 300);
    }
  };

  // Formater le temps de la notification
  const formatNotificationTime = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      const now = new Date();

      // Si c'est aujourd'hui, afficher l'heure
      if (date.getDate() === now.getDate() &&
          date.getMonth() === now.getMonth() &&
          date.getFullYear() === now.getFullYear()) {
        return format(date, 'HH:mm', { locale: fr });
      }

      // Si c'est hier
      const yesterday = new Date(now);
      yesterday.setDate(now.getDate() - 1);
      if (date.getDate() === yesterday.getDate() &&
          date.getMonth() === yesterday.getMonth() &&
          date.getFullYear() === yesterday.getFullYear()) {
        return 'Hier';
      }

      // Sinon, afficher la date
      return format(date, 'dd/MM/yyyy', { locale: fr });
    } catch (error) {
      console.error('Erreur lors du formatage de la date:', error);
      return 'Date inconnue';
    }
  };

  // Obtenir l'icône pour une notification en fonction de son type
  const getNotificationIcon = (type: AppointmentNotification['type']) => {
    switch (type) {
      case 'new_appointment':
        return <Ionicons name="calendar" size={24} color={theme.colors.primary} />;
      case 'reminder':
        return <Ionicons name="alarm" size={24} color={theme.colors.secondary} />;
      case 'cancelled':
        return <Ionicons name="close-circle" size={24} color={theme.colors.error} />;
      case 'rescheduled':
        return <Ionicons name="refresh-circle" size={24} color={theme.colors.warning} />;
      case 'confirmation':
        return <Ionicons name="checkmark-circle" size={24} color={theme.colors.success} />;
      default:
        return <Ionicons name="notifications" size={24} color={theme.colors.primary} />;
    }
  };

  // Modifier la fonction de création de rendez-vous pour enregistrer l'activité
  const handleCreateAppointment = async (appointmentData: {
    professionalId: string;
    dateTime: string;
    duration: number;
    type: string;
    notes?: string;
  }) => {
    try {
      setLoading(true);

      if (!user) {
        throw new Error("Utilisateur non connecté");
      }

      // Code existant pour créer le rendez-vous
      const { data, error } = await supabase
        .from('appointments')
        .insert({
          user_id: user.id,
          professional_id: appointmentData.professionalId,
          date_time: appointmentData.dateTime,
          duration: appointmentData.duration,
          type: appointmentData.type,
          notes: appointmentData.notes,
          status: 'pending'
        })
        .select()
        .single();

      if (error) throw error;

      // Enregistrer l'activité de création de rendez-vous
      if (user) {
        recordActivity('appointment_created', {
          appointment_id: data.id,
          professional_id: data.professional_id,
          appointment_type: data.type,
          appointment_date: data.date_time
        });
      }

      // Rafraîchir la liste des rendez-vous
      loadAppointments();

      return data;
    } catch (error) {
      console.error('Erreur lors de la création du rendez-vous:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Modifier la fonction d'annulation de rendez-vous pour enregistrer l'activité
  const handleCancelAppointment = async (appointmentId: string) => {
    try {
      setLoading(true);

      // Code existant pour annuler le rendez-vous
      const { data, error } = await supabase
        .from('appointments')
        .update({ status: 'cancelled' })
        .eq('id', appointmentId)
        .select()
        .single();

      if (error) throw error;

      // Enregistrer l'activité d'annulation de rendez-vous
      if (user) {
        recordActivity('appointment_cancelled', {
          appointment_id: data.id,
          professional_id: data.professional_id,
          appointment_type: data.type,
          appointment_date: data.date_time
        });
      }

      // Rafraîchir la liste des rendez-vous
      loadAppointments();

      return data;
    } catch (error) {
      console.error('Erreur lors de l\'annulation du rendez-vous:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Exemple de données de rendez-vous
  const dummyAppointments: AppointmentType[] = [
    {
      id: '1',
      title: 'Consultation Psychologique',
      description: 'Séance de suivi avec Dr. Mbaka',
      date: '25 Mars 2025',
      time: '14:30',
      provider: 'Dr. Mbaka Jean',
      location: 'Centre médical Bomoko, Kinshasa',
      status: 'confirmed'
    },
    {
      id: '2',
      title: 'Évaluation Bien-être',
      description: 'Évaluation mensuelle avec l\'équipe d\'assistance',
      date: '30 Mars 2025',
      time: '10:00',
      provider: 'Mme. Mutombo Sarah',
      location: 'Centre communautaire, Kinshasa',
      status: 'pending'
    },
    {
      id: '3',
      title: 'Suivi Médical',
      description: 'Contrôle de santé régulier',
      date: '10 Février 2025',
      time: '09:15',
      provider: 'Dr. Kalala Pierre',
      location: 'Hôpital Général de Kinshasa',
      status: 'completed'
    },
    {
      id: '4',
      title: 'Consultation Juridique',
      description: 'Assistance juridique pour démarches administratives',
      date: '15 Février 2025',
      time: '11:30',
      provider: 'Me. Lusengo Paul',
      location: 'Bureau d\'aide juridique, Kinshasa',
      status: 'completed'
    }
  ];

  // Filtrer les rendez-vous selon l'onglet sélectionné
  const currentDate = new Date();
  const appointmentsToDisplay = dummyAppointments.filter(appointment => {
    const appointmentDate = new Date(appointment.date.replace('Mars', 'March').replace('Février', 'February'));
    if (selectedTab === 'upcoming') {
      return appointmentDate >= currentDate || appointment.status === 'pending' || appointment.status === 'confirmed';
    } else {
      return appointmentDate < currentDate || appointment.status === 'completed' || appointment.status === 'cancelled';
    }
  });

  const handleViewAppointmentDetails = (id: string) => {
    // @ts-ignore - expo-router typing issues
    router.navigate({
      pathname: "/(app)/appointments/[id]",
      params: { id }
    });
  };

  const renderAppointmentItem = ({ item }: { item: AppointmentType }) => (
    <TouchableOpacity
      style={[styles.appointmentCard, { backgroundColor: theme.colors.surface }]}
      onPress={() => handleViewAppointmentDetails(item.id)}
    >
      <View style={styles.appointmentHeader}>
        <View style={styles.appointmentTitleContainer}>
          <Text style={[styles.appointmentTitle, { color: theme.colors.text }]}>{item.title}</Text>
          <Text style={[styles.appointmentProvider, { color: theme.colors.text }]}>{item.provider}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>
      <Text style={[styles.appointmentDescription, { color: theme.colors.text }]}>{item.description}</Text>
      <View style={styles.appointmentDetails}>
        <View style={styles.appointmentDetailItem}>
          <Ionicons name="calendar-outline" size={16} color={theme.colors.primary} />
          <Text style={[styles.appointmentDetailText, { color: theme.colors.text }]}>{item.date}</Text>
        </View>
        <View style={styles.appointmentDetailItem}>
          <Ionicons name="time-outline" size={16} color={theme.colors.primary} />
          <Text style={[styles.appointmentDetailText, { color: theme.colors.text }]}>{item.time}</Text>
        </View>
        <View style={styles.appointmentDetailItem}>
          <Ionicons name="location-outline" size={16} color={theme.colors.primary} />
          <Text style={[styles.appointmentDetailText, { color: theme.colors.text }]}>{item.location}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

            return (
    <>
      <Stack.Screen
        options={{
          title: 'Rendez-vous',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      />
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        {/* Tabs pour filtrer les rendez-vous */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[
              styles.tabButton,
              selectedTab === 'upcoming' && [styles.activeTab, { borderColor: theme.colors.primary }]
            ]}
            onPress={() => setSelectedTab('upcoming')}
          >
            <Text
              style={[
                styles.tabText,
                selectedTab === 'upcoming' && [styles.activeTabText, { color: theme.colors.primary }],
                { color: selectedTab === 'upcoming' ? theme.colors.primary : theme.colors.text }
              ]}
            >
              À venir
                  </Text>
          </TouchableOpacity>
          <TouchableOpacity
                    style={[
              styles.tabButton,
              selectedTab === 'past' && [styles.activeTab, { borderColor: theme.colors.primary }]
            ]}
            onPress={() => setSelectedTab('past')}
          >
                  <Text
                    style={[
                styles.tabText,
                selectedTab === 'past' && [styles.activeTabText, { color: theme.colors.primary }],
                { color: selectedTab === 'past' ? theme.colors.primary : theme.colors.text }
              ]}
            >
              Passés
                  </Text>
          </TouchableOpacity>
                </View>

        {/* Liste des rendez-vous */}
        {appointmentsToDisplay.length > 0 ? (
          <FlatList
            data={appointmentsToDisplay}
            renderItem={renderAppointmentItem}
            keyExtractor={item => item.id}
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="calendar-outline" size={80} color={theme.colors.primary} style={{ opacity: 0.5 }} />
            <Text style={[styles.emptyText, { color: theme.colors.text }]}>
              Aucun rendez-vous {selectedTab === 'upcoming' ? 'à venir' : 'passé'}
                  </Text>
                </View>
        )}

        {/* Bouton pour ajouter un nouveau rendez-vous */}
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleBookAppointment}
        >
          <Ionicons name="add" size={24} color="#fff" />
        </TouchableOpacity>
            </View>
    </>
  );




}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
  },
  activeTabText: {
    fontWeight: 'bold',
  },
  listContainer: {
    padding: 15,
  },
  appointmentCard: {
    borderRadius: 12,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  appointmentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  appointmentTitleContainer: {
    flex: 1,
  },
  appointmentTitle: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  appointmentProvider: {
    fontSize: 14,
    opacity: 0.7,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  appointmentDescription: {
    marginBottom: 10,
    fontSize: 14,
  },
  appointmentDetails: {
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
    paddingTop: 10,
  },
  appointmentDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  appointmentDetailText: {
    marginLeft: 8,
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    marginTop: 10,
    fontSize: 16,
    textAlign: 'center',
  },
  addButton: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
});