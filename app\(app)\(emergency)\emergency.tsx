import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Alert,
  ActivityIndicator,
  Pressable,
  Vibration,
  ScrollView,
  Animated,
  PermissionsAndroid,
} from 'react-native';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router'; // Utilisé pour la navigation
import * as Haptics from 'expo-haptics';
import * as Location from 'expo-location';
import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { supabase } from '../../../lib/supabase';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from '../../../components/Map';

type SOSStatus = 'inactive' | 'activating' | 'active' | 'error';

// Ajout des types pour le suivi SOS
type SOSEvent = {
  id: string;
  user_id: string;
  started_at: string;
  ended_at?: string;
  initial_location: any;
  status: 'active' | 'ended' | 'cancelled';
  metadata: any;
};

// Type pour le statut de mouvement
type MovementStatus = {
  isMoving: boolean;
  lastSignificantMovement: number; // timestamp en millisecondes
  consecutiveStableReadings: number;
  stableZone: {
    latitude: number;
    longitude: number;
    radius: number;
  } | null;
};

const ACTIVATION_DURATION = 3000; // 3 secondes pour l'activation
const LOCATION_UPDATE_INTERVAL = 60000; // 60 secondes (1 minute) comme demandé dans les spécifications
const FALLBACK_SOS_WEBHOOK_URL = "https://n8n-dw1u.onrender.com/webhook/sos"; // URL de secours

// Constantes pour l'enregistrement audio
const AUDIO_RECORDING_DURATION = 600000; // 10 minutes (600 secondes) par enregistrement pour plus de fiabilité
const AUDIO_QUALITY = Audio.RecordingOptionsPresets.HIGH_QUALITY; // Qualité d'enregistrement
const AUDIO_DIRECTORY = FileSystem.documentDirectory + 'sos_recordings/'; // Dossier de stockage

// Constantes pour la détection des déplacements
const LOCATION_CONFIG = {
  UPDATE_INTERVAL: 2000,        // 2 secondes pour plus de réactivité pendant la phase d'observation
  DISTANCE_THRESHOLD: 0.5,      // 0.5 mètre pour détecter des mouvements plus petits
  ACCURACY_THRESHOLD: 25,       // 25 mètres (augmenté pour s'adapter aux conditions réelles)
  SPEED_THRESHOLD: 0.05,        // vitesse minimale en m/s (réduite pour détecter des mouvements plus lents)
  POSITION_HISTORY_SIZE: 5,     // nombre de positions à conserver (augmenté pour plus de précision)
  MIN_POSITIONS_FOR_MOVEMENT: 2, // minimum de positions pour confirmer
  FORCE_UPDATE_INTERVAL: 15000, // Force une mise à jour toutes les 15 secondes même sans mouvement
  TOTAL_DISTANCE_THRESHOLD: 1,  // 1 mètre de distance totale parcourue avant notification
  ROUTE_POINT_PRECISION: 1,     // 1 mètre minimum entre les points de trace

  // Constantes pour la détection précise des mouvements (parfaitement alignées avec l'agent N8N)
  SIGNIFICANT_MOVEMENT_THRESHOLD: 2.0, // Exactement 2 mètres pour un mouvement significatif (requis par l'agent N8N)
  STABLE_ZONE_RADIUS: 2.0,             // 2 mètres de rayon pour la zone de stabilité (aligné avec le seuil de l'agent N8N)
  CONSECUTIVE_STABLE_THRESHOLD: 3,     // Nombre de lectures stables consécutives pour confirmer l'immobilité
  MOVEMENT_ALERT_COOLDOWN: 2000,       // 2 secondes de délai entre les alertes de mouvement (réduit pour plus de réactivité)
  PERIODIC_UPDATE_INTERVAL: 60000,     // 60 secondes (1 minute) entre les mises à jour périodiques sans alerte
  INITIAL_OBSERVATION_PERIOD: 30000    // 30 secondes d'observation initiale avant de commencer les mises à jour régulières
};

// Type pour les points de localisation
type LocationPoint = {
  latitude: number;
  longitude: number;
  timestamp: number;
  accuracy?: number;
  altitude?: number | null;
  heading?: number | null;
  speed?: number | null;
};

export default function EmergencyScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const router = useRouter();
  const [status, setStatus] = useState<SOSStatus>('inactive');
  const [error, setError] = useState<string | null>(null);
  const [activationProgress, setActivationProgress] = useState(0);

  // États pour la géolocalisation
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [locationPermission, setLocationPermission] = useState<boolean>(false);
  const [mapVisible, setMapVisible] = useState<boolean>(true);
  const mapRef = useRef<MapView | null>(null);
  const locationSubscription = useRef<Location.LocationSubscription | null>(null);

  // État pour le webhook
  const [webhookUrl, setWebhookUrl] = useState<string | null>(null);
  const [webhookLoaded, setWebhookLoaded] = useState<boolean>(false);

  // État pour le profil utilisateur
  const [userProfile, setUserProfile] = useState<any>(null);
  const [profileLoaded, setProfileLoaded] = useState<boolean>(false);

  // Ajout des états pour le suivi SOS
  const [currentSOSEvent, setCurrentSOSEvent] = useState<SOSEvent | null>(null);
  const [locationUpdateInterval, setLocationUpdateInterval] = useState<NodeJS.Timeout | null>(null);
  const [lastSentLocation, setLastSentLocation] = useState<{latitude: number, longitude: number, timestamp?: number} | null>(null);

  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  const progressTimer = useRef<NodeJS.Timeout | null>(null);

  // Animations
  const buttonScale = useRef(new Animated.Value(1)).current;
  const buttonOpacity = useRef(new Animated.Value(1)).current;
  const infoSectionOpacity = useRef(new Animated.Value(0)).current;

  // État pour le suivi de la distance
  const [totalDistanceTraveled, setTotalDistanceTraveled] = useState<number>(0);

  // États pour afficher la trajectoire sur la carte
  const [displayedTrajectory, setDisplayedTrajectory] = useState<Array<{latitude: number, longitude: number}>>([]);
  const [showTrajectory, setShowTrajectory] = useState<boolean>(false);
  const [trajectoryDistance, setTrajectoryDistance] = useState<{
    millimeters: number,
    kilometers: number,
    lastSegment: number
  }>({
    millimeters: 0,
    kilometers: 0,
    lastSegment: 0
  });

  // Variables pour le suivi du tracé (utilisées localement, pas besoin d'état)
  const gpsTraceRef = useRef<Array<{latitude: number, longitude: number, timestamp: number}>>([]);
  const lastNotificationDistanceRef = useRef<number>(0);
  const trajectoryUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // États pour l'enregistrement audio
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [audioPermission, setAudioPermission] = useState<boolean>(false);
  const [recordingSession, setRecordingSession] = useState<number>(0);
  const recordingInterval = useRef<NodeJS.Timeout | null>(null);
  const [isRecording, setIsRecording] = useState<boolean>(false);

  // État pour le suivi précis des mouvements
  const [movementStatus, setMovementStatus] = useState<MovementStatus>({
    isMoving: false,
    lastSignificantMovement: 0,
    consecutiveStableReadings: 0,
    stableZone: null
  });

  // Fonction pour démarrer l'intervalle de mise à jour de la trajectoire
  const startTrajectoryUpdateInterval = () => {
    // Arrêter l'intervalle existant s'il y en a un
    stopTrajectoryUpdateInterval();

    // Démarrer un nouvel intervalle
    trajectoryUpdateIntervalRef.current = setInterval(() => {
      if (status === 'active' && showTrajectory && gpsTraceRef.current.length > 0) {
        updateDisplayedTrajectory();

        logEvent('AUTO_TRAJECTORY_UPDATE', {
          message: 'Mise à jour automatique de la trajectoire',
          gpsTracePoints: gpsTraceRef.current.length,
          displayedTrajectoryPoints: displayedTrajectory.length
        });
      }
    }, 2000); // Mise à jour toutes les 2 secondes

    logEvent('TRAJECTORY_UPDATE_INTERVAL_STARTED', {
      message: 'Intervalle de mise à jour de la trajectoire démarré',
      updateFrequency: '2 secondes'
    });
  };

  // Fonction pour arrêter l'intervalle de mise à jour de la trajectoire
  const stopTrajectoryUpdateInterval = () => {
    if (trajectoryUpdateIntervalRef.current) {
      clearInterval(trajectoryUpdateIntervalRef.current);
      trajectoryUpdateIntervalRef.current = null;

      logEvent('TRAJECTORY_UPDATE_INTERVAL_STOPPED', {
        message: 'Intervalle de mise à jour de la trajectoire arrêté'
      });
    }
  };

  // Fonction pour forcer la mise à jour de la trajectoire
  const forceTrajectoryUpdate = () => {
    if (status === 'active' && gpsTraceRef.current.length > 0) {
      // Activer l'affichage de la trajectoire si ce n'est pas déjà fait
      if (!showTrajectory) {
        setShowTrajectory(true);

        // Démarrer l'intervalle de mise à jour automatique
        startTrajectoryUpdateInterval();
      }

      // Mettre à jour la trajectoire immédiatement
      updateDisplayedTrajectory();

      logEvent('FORCE_TRAJECTORY_UPDATE', {
        message: 'Mise à jour forcée de la trajectoire',
        gpsTracePoints: gpsTraceRef.current.length,
        displayedTrajectoryPoints: displayedTrajectory.length
      });

      return true;
    } else {
      logEvent('FORCE_TRAJECTORY_UPDATE_FAILED', {
        message: 'Impossible de forcer la mise à jour de la trajectoire',
        sosStatus: status,
        gpsTracePoints: gpsTraceRef.current.length
      });

      return false;
    }
  };

  // Fonction pour tester le webhook
  const testWebhook = async () => {
    try {
      if (!webhookUrl) {
        await loadWebhookConfiguration();
        if (!webhookUrl) {
          logEvent('TEST_WEBHOOK_ERROR', {
            message: 'Impossible de tester le webhook - URL manquante'
          });
          return false;
        }
      }

      logEvent('TEST_WEBHOOK', {
        message: 'Test du webhook',
        url: webhookUrl
      });

      const testPayload = {
        event_type: 'webhook_test',
        timestamp: new Date().toISOString(),
        message: 'Ceci est un test du webhook SOS',
        user_id: user?.id,
        app_version: '1.0',
        device_info: Platform.OS
      };

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testPayload)
      });

      if (response.ok) {
        logEvent('TEST_WEBHOOK_SUCCESS', {
          message: 'Test du webhook réussi',
          status: response.status
        });
        return true;
      } else {
        logEvent('TEST_WEBHOOK_ERROR', {
          message: 'Erreur lors du test du webhook',
          status: response.status,
          statusText: response.statusText
        });
        return false;
      }
    } catch (err) {
      logEvent('TEST_WEBHOOK_ERROR', {
        message: 'Exception lors du test du webhook',
        error: err instanceof Error ? err.message : 'Erreur inconnue'
      });
      return false;
    }
  };

  // Charger les ressources au démarrage
  useEffect(() => {
    if (!user) return;

    // Charger les données nécessaires
    requestLocationPermission();
    requestAudioPermission();
    loadWebhookConfiguration();
    loadUserProfile();

    // Créer le dossier pour les enregistrements audio
    createAudioDirectory();

    // Tester le webhook après chargement
    setTimeout(async () => {
      if (webhookLoaded) {
        await testWebhook();
      }
    }, 2000);

    return () => {
      // Nettoyer lors du démontage
      cleanup();
    };
  }, [user]);

  // Effet pour gérer l'affichage de la trajectoire lorsque le mode SOS est actif
  useEffect(() => {
    // Si le mode SOS est actif, activer l'affichage de la trajectoire
    if (status === 'active' && !showTrajectory) {
      setShowTrajectory(true);

      // Log pour le débogage
      logEvent('TRAJECTORY_DISPLAY_ACTIVATED', {
        message: 'Affichage de la trajectoire activé',
        sosStatus: status,
        gpsTracePoints: gpsTraceRef.current.length
      });

      // Mettre à jour la trajectoire affichée si des points sont disponibles
      if (gpsTraceRef.current.length > 0) {
        updateDisplayedTrajectory();
      }

      // Démarrer l'intervalle de mise à jour automatique
      startTrajectoryUpdateInterval();
    }

    // Si le mode SOS est désactivé, désactiver l'affichage de la trajectoire
    if (status !== 'active' && showTrajectory) {
      setShowTrajectory(false);
      setDisplayedTrajectory([]);

      // Arrêter l'intervalle de mise à jour automatique
      stopTrajectoryUpdateInterval();

      // Log pour le débogage
      logEvent('TRAJECTORY_DISPLAY_DEACTIVATED', {
        message: 'Affichage de la trajectoire désactivé',
        sosStatus: status
      });
    }
  }, [status, showTrajectory]);

  // Effet pour nettoyer l'intervalle de mise à jour de la trajectoire lors du démontage
  useEffect(() => {
    return () => {
      // Arrêter l'intervalle de mise à jour automatique
      stopTrajectoryUpdateInterval();
    };
  }, []);

  // Créer le dossier pour les enregistrements audio
  const createAudioDirectory = async () => {
    try {
      const dirInfo = await FileSystem.getInfoAsync(AUDIO_DIRECTORY);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(AUDIO_DIRECTORY, { intermediates: true });
        logEvent('AUDIO_DIR_CREATED', {
          message: 'Dossier d\'enregistrements audio créé',
          path: AUDIO_DIRECTORY
        });
      }
    } catch (err) {
      logEvent('AUDIO_DIR_ERROR', {
        message: 'Erreur lors de la création du dossier audio',
        error: err instanceof Error ? err.message : 'Erreur inconnue'
      });
    }
  };

  // Vérifier et obtenir silencieusement la permission d'enregistrement audio
  const requestAudioPermission = async () => {
    try {
      // Vérifier d'abord si la permission est déjà accordée
      let hasPermission = false;

      if (Platform.OS === 'android') {
        hasPermission = await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO);

        if (!hasPermission) {
          // Demander la permission silencieusement
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
            {
              title: "Accès au microphone",
              message: "Nécessaire pour le mode SOS",
              buttonPositive: "OK"
            }
          );
          hasPermission = granted === PermissionsAndroid.RESULTS.GRANTED;
        }
      } else {
        // iOS
        const { status: existingStatus } = await Audio.getPermissionsAsync();
        hasPermission = existingStatus === 'granted';

        if (!hasPermission) {
          const { status } = await Audio.requestPermissionsAsync();
          hasPermission = status === 'granted';
        }
      }

      setAudioPermission(hasPermission);
    } catch (err) {
      logEvent('AUDIO_PERMISSION_ERROR', {
        message: 'Erreur lors de la vérification des permissions audio',
        error: err instanceof Error ? err.message : 'Erreur inconnue'
      });
    }
  };

  // Vérifier et obtenir silencieusement la permission de localisation
  const requestLocationPermission = async () => {
    try {
      // Vérifier d'abord si la permission est déjà accordée
      const { status: existingStatus } = await Location.getForegroundPermissionsAsync();
      let hasPermission = existingStatus === 'granted';

      if (!hasPermission) {
        // Demander la permission si nécessaire
        const { status } = await Location.requestForegroundPermissionsAsync();
        hasPermission = status === 'granted';
      }

      setLocationPermission(hasPermission);

      if (hasPermission) {
        // Vérifier si les services de localisation sont activés
        try {
          const providerStatus = await Location.hasServicesEnabledAsync();

          if (!providerStatus) {
            // Les services de localisation sont désactivés
            logEvent('LOCATION_SERVICES_DISABLED', {
              message: 'Services de localisation désactivés sur l\'appareil'
            });

            // Afficher une alerte pour informer l'utilisateur
            Alert.alert(
              "Services de localisation désactivés",
              "Veuillez activer les services de localisation dans les paramètres de votre appareil pour utiliser le mode SOS.",
              [{ text: "OK" }]
            );
            return;
          }

          // Démarrer le suivi de localisation
          startLocationTracking();
        } catch (serviceErr) {
          logEvent('LOCATION_SERVICES_ERROR', {
            message: 'Erreur lors de la vérification des services de localisation',
            error: serviceErr instanceof Error ? serviceErr.message : 'Erreur inconnue'
          });
          setError('Impossible de vérifier les services de localisation');
        }
      } else {
        // Log silencieux au lieu d'une alerte
        logEvent('LOCATION_PERMISSION_DENIED', {
          message: 'Permission de localisation non accordée'
        });
      }
    } catch (err) {
      logEvent('LOCATION_PERMISSION_ERROR', {
        message: 'Erreur lors de la vérification des permissions de localisation',
        error: err instanceof Error ? err.message : 'Erreur inconnue'
      });
      setError('Impossible d\'accéder à votre position');
    }
  };



  // Fonction améliorée pour détecter les mouvements significatifs avec plus de précision
  const detectSignificantMovement = (newLocation: Location.LocationObject): boolean => {
    const currentTime = new Date().getTime();

    // Si c'est la première position, initialiser la zone de stabilité
    if (!movementStatus.stableZone) {
      const initialStableZone = {
        latitude: newLocation.coords.latitude,
        longitude: newLocation.coords.longitude,
        radius: LOCATION_CONFIG.STABLE_ZONE_RADIUS
      };

      setMovementStatus({
        ...movementStatus,
        stableZone: initialStableZone,
        consecutiveStableReadings: 1,
        lastSignificantMovement: currentTime,
        isMoving: false
      });

      logEvent('MOVEMENT_STABLE_ZONE_INITIALIZED', {
        stableZone: initialStableZone,
        timestamp: new Date(currentTime).toISOString()
      });

      return false;
    }

    // Vérifier que la zone de stabilité contient des coordonnées valides
    if (typeof movementStatus.stableZone.latitude !== 'number' ||
        typeof movementStatus.stableZone.longitude !== 'number' ||
        isNaN(movementStatus.stableZone.latitude) ||
        isNaN(movementStatus.stableZone.longitude)) {

      // Réinitialiser la zone de stabilité avec les coordonnées actuelles
      const fixedStableZone = {
        latitude: newLocation.coords.latitude,
        longitude: newLocation.coords.longitude,
        radius: LOCATION_CONFIG.STABLE_ZONE_RADIUS
      };

      setMovementStatus({
        ...movementStatus,
        stableZone: fixedStableZone,
        consecutiveStableReadings: 1,
        lastSignificantMovement: currentTime,
        isMoving: false
      });

      logEvent('MOVEMENT_STABLE_ZONE_FIXED', {
        message: 'Zone de stabilité invalide détectée et corrigée',
        previousZone: movementStatus.stableZone,
        newZone: fixedStableZone
      });

      return false;
    }

    // Calculer la distance par rapport à la zone de stabilité
    const distanceFromStableZone = calculateDistance(
      movementStatus.stableZone.latitude,
      movementStatus.stableZone.longitude,
      newLocation.coords.latitude,
      newLocation.coords.longitude
    );

    // Vérifier si l'utilisateur est sorti de la zone de stabilité
    // Utiliser exactement le seuil de 2.0 mètres comme spécifié dans les exigences
    const isOutsideStableZone = distanceFromStableZone >= LOCATION_CONFIG.SIGNIFICANT_MOVEMENT_THRESHOLD;

    // Vérifier si le temps de refroidissement est passé
    const cooldownPassed = (currentTime - movementStatus.lastSignificantMovement) >
                           LOCATION_CONFIG.MOVEMENT_ALERT_COOLDOWN;

    // Log détaillé pour le débogage des mouvements
    logEvent('MOVEMENT_EVALUATION_DETAIL', {
      currentPosition: {
        latitude: newLocation.coords.latitude,
        longitude: newLocation.coords.longitude
      },
      stableZone: movementStatus.stableZone,
      distanceFromStableZone: `${distanceFromStableZone.toFixed(2)}m`,
      isOutsideStableZone: isOutsideStableZone,
      cooldownPassed: cooldownPassed,
      threshold: LOCATION_CONFIG.SIGNIFICANT_MOVEMENT_THRESHOLD,
      timeSinceLastMovement: (currentTime - movementStatus.lastSignificantMovement) / 1000,
      consecutiveStableReadings: movementStatus.consecutiveStableReadings,
      isMoving: movementStatus.isMoving
    });

    // Si l'utilisateur est dans la zone de stabilité, augmenter le compteur de lectures stables
    if (!isOutsideStableZone) {
      const updatedStatus = {
        ...movementStatus,
        consecutiveStableReadings: (movementStatus.consecutiveStableReadings || 0) + 1,
        isMoving: false
      };

      setMovementStatus(updatedStatus);

      logEvent('MOVEMENT_STABLE_READING', {
        message: 'Position stable détectée',
        consecutiveStableReadings: updatedStatus.consecutiveStableReadings,
        distanceFromStableZone: distanceFromStableZone.toFixed(2)
      });

      return false;
    }

    // Si l'utilisateur est sorti de la zone de stabilité et que le refroidissement est passé
    if (isOutsideStableZone && cooldownPassed) {
      // Créer une nouvelle zone de stabilité centrée sur la position actuelle
      const newStableZone = {
        latitude: newLocation.coords.latitude,
        longitude: newLocation.coords.longitude,
        radius: LOCATION_CONFIG.STABLE_ZONE_RADIUS
      };

      const updatedStatus = {
        isMoving: true,
        lastSignificantMovement: currentTime,
        consecutiveStableReadings: 0,
        stableZone: newStableZone
      };

      setMovementStatus(updatedStatus);

      // Calculer la distance exacte parcourue pour le message d'alerte
      const distanceMoved = distanceFromStableZone.toFixed(2);

      logEvent('SIGNIFICANT_MOVEMENT_DETECTED', {
        previousZone: movementStatus.stableZone,
        newStableZone: newStableZone,
        newPosition: {
          latitude: newLocation.coords.latitude,
          longitude: newLocation.coords.longitude
        },
        distanceMoved: `${distanceMoved}m`,
        timestamp: new Date().toISOString(),
        movementStatus: updatedStatus
      });

      return true;
    }

    // Si l'utilisateur est sorti de la zone mais que le refroidissement n'est pas passé
    if (isOutsideStableZone && !cooldownPassed) {
      logEvent('MOVEMENT_COOLDOWN_ACTIVE', {
        message: 'Mouvement détecté mais en période de refroidissement',
        distanceFromStableZone: distanceFromStableZone.toFixed(2),
        timeRemaining: (LOCATION_CONFIG.MOVEMENT_ALERT_COOLDOWN - (currentTime - movementStatus.lastSignificantMovement)) / 1000
      });
    }

    return false;
  };



  // Fonction utilitaire pour les logs
  const logEvent = (type: string, details: any) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${type}]`, JSON.stringify(details, null, 2));
  };

  // Démarrer l'enregistrement audio automatiquement avec une meilleure gestion de la continuité
  const startAudioRecording = async () => {
    try {
      // Vérifier et demander la permission si nécessaire
      if (!audioPermission) {
        await requestAudioPermission();
      }

      // Si toujours pas de permission après la demande, log silencieux et sortie
      if (!audioPermission) {
        logEvent('AUDIO_RECORDING_PERMISSION', {
          message: 'Enregistrement audio non disponible - permission non accordée'
        });
        return;
      }

      // Configurer l'audio avec des paramètres optimisés pour l'enregistrement en arrière-plan
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        staysActiveInBackground: true,
        // Utiliser des valeurs numériques au lieu des constantes pour éviter les erreurs
        interruptionModeIOS: 1, // Équivalent à INTERRUPTION_MODE_IOS_DO_NOT_MIX
        interruptionModeAndroid: 1, // Équivalent à INTERRUPTION_MODE_ANDROID_DO_NOT_MIX
        shouldDuckAndroid: false, // Ne pas réduire le volume des autres applications
      });

      // Arrêter tout enregistrement en cours
      if (recording) {
        await stopAudioRecording();
      }

      // Incrémenter le numéro de session
      setRecordingSession(prev => prev + 1);

      // Créer un nouveau fichier pour l'enregistrement avec un format de nom plus détaillé
      const timestamp = new Date().getTime();
      const fileName = `sos_${currentSOSEvent?.id || 'emergency'}_${timestamp}_${recordingSession}.m4a`;
      const fileUri = AUDIO_DIRECTORY + fileName;

      logEvent('AUDIO_RECORDING_START', {
        message: 'Démarrage de l\'enregistrement audio',
        fileUri,
        duration: AUDIO_RECORDING_DURATION / 1000, // en secondes
        session: recordingSession,
        sos_event_id: currentSOSEvent?.id
      });

      // Démarrer l'enregistrement avec une qualité optimisée pour la durée
      const newRecording = new Audio.Recording();

      // Utiliser des options personnalisées pour optimiser la taille du fichier et la durée de la batterie
      const recordingOptions = {
        ...AUDIO_QUALITY,
        android: {
          ...AUDIO_QUALITY.android,
          extension: '.m4a',
          outputFormat: Audio.AndroidOutputFormat.MPEG_4,
          audioEncoder: Audio.AndroidAudioEncoder.AAC,
          sampleRate: 22050, // Réduire la fréquence d'échantillonnage pour économiser la batterie
          numberOfChannels: 1, // Mono au lieu de stéréo
          bitRate: 64000, // Réduire le débit pour économiser l'espace
        },
        ios: {
          ...AUDIO_QUALITY.ios,
          extension: '.m4a',
          outputFormat: Audio.IOSOutputFormat.MPEG4AAC,
          audioQuality: Audio.IOSAudioQuality.MEDIUM, // Qualité moyenne pour économiser la batterie
          sampleRate: 22050,
          numberOfChannels: 1,
          bitRate: 64000,
          linearPCMBitDepth: 16,
          linearPCMIsBigEndian: false,
          linearPCMIsFloat: false,
        },
      };

      await newRecording.prepareToRecordAsync(recordingOptions);
      await newRecording.startAsync();

      setRecording(newRecording);
      setIsRecording(true);

      // Configurer un timer pour arrêter l'enregistrement après la durée spécifiée
      // et assurer la continuité des enregistrements
      const recordingTimeout = setTimeout(async () => {
        if (status === 'active' && isRecording) {
          logEvent('AUDIO_RECORDING_SEGMENT_COMPLETE', {
            message: 'Segment d\'enregistrement audio terminé',
            duration: AUDIO_RECORDING_DURATION / 1000, // en secondes
            session: recordingSession,
            sos_event_id: currentSOSEvent?.id
          });

          const uri = await stopAudioRecording();
          if (uri) {
            // Envoyer l'enregistrement au webhook et l'enregistrer en base de données
            await sendAudioRecording(uri);

            // Redémarrer immédiatement un nouvel enregistrement si le mode SOS est toujours actif
            // pour assurer une continuité maximale
            if (status === 'active') {
              // Petit délai pour éviter les conflits potentiels
              setTimeout(() => {
                startAudioRecording();
              }, 500);
            }
          }
        }
      }, AUDIO_RECORDING_DURATION);

      // Stocker la référence du timeout pour pouvoir l'annuler si nécessaire
      recordingInterval.current = recordingTimeout as unknown as NodeJS.Timeout;

    } catch (err) {
      logEvent('AUDIO_RECORDING_ERROR', {
        message: 'Erreur lors du démarrage de l\'enregistrement audio',
        error: err instanceof Error ? err.message : 'Erreur inconnue'
      });
      setIsRecording(false);

      // En cas d'erreur, essayer de redémarrer l'enregistrement après un court délai
      if (status === 'active') {
        setTimeout(() => {
          startAudioRecording();
        }, 5000); // Attendre 5 secondes avant de réessayer
      }
    }
  };

  // Arrêter l'enregistrement audio
  const stopAudioRecording = async (): Promise<string | null> => {
    if (!recording) {
      return null;
    }

    try {
      logEvent('AUDIO_RECORDING_STOP', {
        message: 'Arrêt de l\'enregistrement audio'
      });

      // Arrêter l'enregistrement
      await recording.stopAndUnloadAsync();

      // Obtenir l'URI du fichier enregistré
      const uri = recording.getURI();

      if (!uri) {
        throw new Error('URI d\'enregistrement non disponible');
      }

      // Copier le fichier vers notre dossier de stockage
      const fileName = `sos_recording_${new Date().getTime()}_${recordingSession}.m4a`;
      const destinationUri = AUDIO_DIRECTORY + fileName;

      await FileSystem.copyAsync({
        from: uri,
        to: destinationUri
      });

      // Enregistrer le fichier audio (nous n'utilisons plus de liste d'état)

      logEvent('AUDIO_RECORDING_SAVED', {
        message: 'Enregistrement audio sauvegardé',
        uri: destinationUri
      });

      setRecording(null);
      setIsRecording(false);

      return destinationUri;

    } catch (err) {
      logEvent('AUDIO_RECORDING_STOP_ERROR', {
        message: 'Erreur lors de l\'arrêt de l\'enregistrement audio',
        error: err instanceof Error ? err.message : 'Erreur inconnue'
      });
      setRecording(null);
      setIsRecording(false);
      return null;
    }
  };

  // Envoyer l'enregistrement audio au webhook et l'enregistrer en base de données
  const sendAudioRecording = async (fileUri: string) => {
    if (!webhookUrl || !currentSOSEvent) {
      logEvent('AUDIO_SEND_SKIP', {
        message: 'Envoi audio ignoré',
        hasWebhook: !!webhookUrl,
        hasSOSEvent: !!currentSOSEvent
      });
      return;
    }

    try {
      // Lire le fichier en base64
      const fileInfo = await FileSystem.getInfoAsync(fileUri);
      const fileContent = await FileSystem.readAsStringAsync(fileUri, {
        encoding: FileSystem.EncodingType.Base64
      });

      // Créer l'enregistrement dans la base de données
      const { data: audioRecord, error: dbError } = await supabase
        .from('sos_audio_recordings')
        .insert({
          sos_event_id: currentSOSEvent.id,
          file_path: fileUri,
          duration: Math.floor(AUDIO_RECORDING_DURATION / 1000), // Convertir en secondes
          started_at: new Date().toISOString(),
          metadata: {
            file_size: fileInfo.exists ? (fileInfo as any).size || 0 : 0,
            file_type: 'audio/m4a',
            device_info: Platform.OS
          }
        })
        .select()
        .single();

      if (dbError) {
        logEvent('AUDIO_DB_ERROR', {
          message: 'Erreur lors de l\'enregistrement audio en base de données',
          error: dbError.message
        });
      }

      // Envoyer l'audio au webhook
      const audioPayload = {
        event_id: currentSOSEvent.id,
        user_id: user?.id,
        update_type: 'audio_recording',
        timestamp: new Date().toISOString(),
        help_message: "Enregistrement audio d'urgence. Veuillez écouter pour évaluer la situation.",
        audio_data: {
          file_content: fileContent,
          file_type: 'audio/m4a',
          duration: Math.floor(AUDIO_RECORDING_DURATION / 1000),
          file_name: fileUri.split('/').pop()
        },
        location: location ? {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          accuracy: location.coords.accuracy,
          timestamp: location.timestamp
        } : null,
        user_profile: userProfile
      };

      logEvent('AUDIO_WEBHOOK_REQUEST', {
        message: 'Envoi de l\'enregistrement audio au webhook',
        fileSize: fileInfo.exists ? (fileInfo as any).size || 0 : 0,
        fileName: fileUri.split('/').pop()
      });

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(audioPayload)
      });

      if (response.ok) {
        logEvent('AUDIO_WEBHOOK_SUCCESS', {
          message: 'Enregistrement audio envoyé avec succès',
          status: response.status
        });

        // Mettre à jour l'enregistrement dans la base de données
        if (audioRecord) {
          await supabase
            .from('sos_audio_recordings')
            .update({
              uploaded_at: new Date().toISOString()
            })
            .eq('id', audioRecord.id);
        }
      } else {
        logEvent('AUDIO_WEBHOOK_ERROR', {
          message: 'Erreur lors de l\'envoi de l\'enregistrement audio',
          status: response.status,
          statusText: response.statusText
        });
      }
    } catch (err) {
      logEvent('AUDIO_SEND_ERROR', {
        message: 'Erreur lors de l\'envoi de l\'enregistrement audio',
        error: err instanceof Error ? err.message : 'Erreur inconnue'
      });
    }
  };

  // Fonction pour mettre à jour la trajectoire affichée sur la carte
  const updateDisplayedTrajectory = () => {
    try {
      // Log pour le débogage des conditions d'affichage
      logEvent('TRAJECTORY_DISPLAY_CHECK', {
        showTrajectory: showTrajectory,
        gpsTracePointsCount: gpsTraceRef.current.length,
        sosStatus: status,
        displayedTrajectoryCount: displayedTrajectory.length,
        totalDistanceTraveled: totalDistanceTraveled
      });

      if (!showTrajectory) {
        logEvent('TRAJECTORY_DISPLAY_SKIPPED', {
          message: 'Affichage de la trajectoire désactivé',
          reason: 'showTrajectory is false'
        });
        return;
      }

      if (gpsTraceRef.current.length === 0) {
        logEvent('TRAJECTORY_DISPLAY_SKIPPED', {
          message: 'Aucun point GPS disponible',
          reason: 'gpsTraceRef.current.length is 0'
        });
        return;
      }

      // Convertir les points du tracé GPS en points pour la trajectoire affichée
      const trajectoryPoints = gpsTraceRef.current.map(point => ({
        latitude: point.latitude,
        longitude: point.longitude
      }));

      // Vérifier que les points sont valides
      const validPoints = trajectoryPoints.filter(
        point => typeof point.latitude === 'number' &&
                !isNaN(point.latitude) &&
                typeof point.longitude === 'number' &&
                !isNaN(point.longitude)
      );

      if (validPoints.length !== trajectoryPoints.length) {
        logEvent('TRAJECTORY_INVALID_POINTS', {
          message: 'Points de trajectoire invalides détectés et filtrés',
          totalPoints: trajectoryPoints.length,
          validPoints: validPoints.length
        });
      }

      if (validPoints.length === 0) {
        logEvent('TRAJECTORY_DISPLAY_SKIPPED', {
          message: 'Aucun point GPS valide disponible',
          reason: 'Tous les points sont invalides'
        });
        return;
      }

      // Log des points de trajectoire pour le débogage
      logEvent('TRAJECTORY_POINTS', {
        message: 'Points de trajectoire générés',
        count: validPoints.length,
        firstPoint: validPoints[0],
        lastPoint: validPoints[validPoints.length - 1]
      });

      // Mettre à jour la trajectoire affichée
      setDisplayedTrajectory(validPoints);

      // Calculer les distances pour l'affichage
      if (validPoints.length > 1) {
        // Convertir la distance totale en millimètres et kilomètres
        const millimeters = Math.round(totalDistanceTraveled * 1000);
        const kilometers = totalDistanceTraveled / 1000;

        // Calculer la distance du dernier segment
        const lastPoint = validPoints[validPoints.length - 1];
        const previousPoint = validPoints[validPoints.length - 2];
        const lastSegmentDistance = calculateDistance(
          previousPoint.latitude,
          previousPoint.longitude,
          lastPoint.latitude,
          lastPoint.longitude
        );

        // Mettre à jour les distances
        setTrajectoryDistance({
          millimeters,
          kilometers,
          lastSegment: lastSegmentDistance
        });

        logEvent('TRAJECTORY_DISPLAY_UPDATE', {
          message: 'Trajectoire mise à jour avec succès',
          pointsCount: validPoints.length,
          totalDistanceMeters: totalDistanceTraveled.toFixed(2),
          totalDistanceMillimeters: millimeters,
          totalDistanceKilometers: kilometers.toFixed(6),
          lastSegmentDistance: lastSegmentDistance.toFixed(2),
          timestamp: new Date().toISOString()
        });
      } else {
        // Même avec un seul point, mettre à jour les distances avec les valeurs actuelles
        const millimeters = Math.round(totalDistanceTraveled * 1000);
        const kilometers = totalDistanceTraveled / 1000;

        setTrajectoryDistance({
          millimeters,
          kilometers,
          lastSegment: 0
        });

        logEvent('TRAJECTORY_DISPLAY_SINGLE_POINT', {
          message: 'Un seul point de trajectoire disponible, impossible de calculer le dernier segment',
          point: validPoints[0],
          totalDistanceMeters: totalDistanceTraveled.toFixed(2)
        });
      }
    } catch (error) {
      // Capturer toute erreur qui pourrait se produire
      logEvent('TRAJECTORY_UPDATE_ERROR', {
        message: 'Erreur lors de la mise à jour de la trajectoire',
        error: error instanceof Error ? error.message : 'Erreur inconnue',
        stack: error instanceof Error ? error.stack : undefined
      });
    }
  };

  // Fonction améliorée pour mettre à jour le tracé GPS et calculer la distance
  const updateGpsTrace = (newLocation: Location.LocationObject) => {
    // Ne pas ajouter de point si la précision est insuffisante
    if (newLocation.coords.accuracy &&
        newLocation.coords.accuracy > LOCATION_CONFIG.ACCURACY_THRESHOLD) {
      logEvent('TRACE_SKIPPED_LOW_ACCURACY', {
        accuracy: newLocation.coords.accuracy,
        threshold: LOCATION_CONFIG.ACCURACY_THRESHOLD
      });
      return;
    }

    // Ajouter le point au tracé local
    const newPoint = {
      latitude: newLocation.coords.latitude,
      longitude: newLocation.coords.longitude,
      timestamp: newLocation.timestamp
    };

    // Calculer la distance si nous avons des points précédents
    if (gpsTraceRef.current.length > 0) {
      const lastPoint = gpsTraceRef.current[gpsTraceRef.current.length - 1];
      const distance = calculateDistance(
        lastPoint.latitude,
        lastPoint.longitude,
        newPoint.latitude,
        newPoint.longitude
      );

      // Toujours ajouter le point au tracé, même si la distance est petite
      gpsTraceRef.current.push(newPoint);

      // Mettre à jour la distance totale parcourue
      // Utiliser une mise à jour synchrone pour garantir que la valeur est disponible immédiatement
      const newTotalDistance = totalDistanceTraveled + distance;
      setTotalDistanceTraveled(newTotalDistance);

      // Mettre à jour la trajectoire affichée si le mode SOS est actif
      if (status === 'active' && showTrajectory) {
        updateDisplayedTrajectory();
      }

      logEvent('TRACE_UPDATE', {
        coords: {
          latitude: newLocation.coords.latitude,
          longitude: newLocation.coords.longitude,
          accuracy: newLocation.coords.accuracy
        },
        distance: `${distance.toFixed(2)}m`,
        totalDistance: `${newTotalDistance.toFixed(2)}m`,
        timestamp: new Date(newLocation.timestamp).toISOString()
      });

      // Retourner la distance calculée pour permettre son utilisation ailleurs
      return {
        distance: distance,
        totalDistance: newTotalDistance
      };
    } else {
      // Premier point du tracé
      gpsTraceRef.current.push(newPoint);

      // Mettre à jour la trajectoire affichée si le mode SOS est actif
      if (status === 'active' && showTrajectory) {
        updateDisplayedTrajectory();
      }

      logEvent('TRACE_FIRST_POINT', {
        coords: {
          latitude: newLocation.coords.latitude,
          longitude: newLocation.coords.longitude,
          accuracy: newLocation.coords.accuracy
        },
        timestamp: new Date(newLocation.timestamp).toISOString()
      });

      return {
        distance: 0,
        totalDistance: 0
      };
    }
  };


 // A SUPRIMMER
  // Fonction simplifiée pour le suivi de localisation (utilisant uniquement le nouveau système)
  const startLocationTracking = async () => {
    try {
      logEvent('TRACKING_START', {
        message: 'Démarrage du suivi de localisation (NOUVEAU SYSTÈME)',
        mode: status,
        config: LOCATION_CONFIG
      });

      // Vérifier si les services de localisation sont activés
      const servicesEnabled = await Location.hasServicesEnabledAsync();
      if (!servicesEnabled) {
        logEvent('LOCATION_SERVICES_DISABLED', {
          message: 'Services de localisation désactivés lors du démarrage du suivi'
        });

        // Afficher une alerte pour informer l'utilisateur
        Alert.alert(
          "Services de localisation désactivés",
          "Veuillez activer les services de localisation dans les paramètres de votre appareil pour utiliser le mode SOS.",
          [{ text: "OK" }]
        );

        setError('Services de localisation désactivés');
        return;
      }

      let currentLocation: Location.LocationObject;

      try {
        // Obtenir la position initiale avec un délai maximum de 15 secondes
        const options: Location.LocationOptions = {
          accuracy: Location.Accuracy.BestForNavigation,
          mayShowUserSettingsDialog: true // Permet d'afficher la boîte de dialogue des paramètres si nécessaire
        };

        // Ajouter un timeout manuel
        const locationPromise = Location.getCurrentPositionAsync(options);
        const timeoutPromise = new Promise<null>((_, reject) => {
          setTimeout(() => reject(new Error('Timeout: Impossible d\'obtenir la position dans le délai imparti')), 15000);
        });

        // Utiliser Promise.race pour implémenter un timeout
        currentLocation = await Promise.race([locationPromise, timeoutPromise]) as Location.LocationObject;

        logEvent('INITIAL_LOCATION', {
          coords: {
            latitude: currentLocation.coords.latitude,
            longitude: currentLocation.coords.longitude,
            accuracy: currentLocation.coords.accuracy,
            speed: currentLocation.coords.speed
          },
          timestamp: new Date(currentLocation.timestamp).toISOString()
        });

        // Initialiser la zone de stabilité avec la position initiale
        if (!movementStatus.stableZone) {
          setMovementStatus({
            ...movementStatus,
            stableZone: {
              latitude: currentLocation.coords.latitude,
              longitude: currentLocation.coords.longitude,
              radius: LOCATION_CONFIG.STABLE_ZONE_RADIUS
            },
            consecutiveStableReadings: 1,
            lastSignificantMovement: new Date().getTime()
          });
        }

        // Mettre à jour l'état de la position
        setLocation(currentLocation);

        // Initialiser le tracé GPS avec la position initiale
        if (status === 'active') {
          // Réinitialiser le tracé GPS
          gpsTraceRef.current = [{
            latitude: currentLocation.coords.latitude,
            longitude: currentLocation.coords.longitude,
            timestamp: currentLocation.timestamp
          }];
          setTotalDistanceTraveled(0);
          lastNotificationDistanceRef.current = 0;
        }

        // Centrer la carte sur la position initiale
        if (mapRef.current && currentLocation) {
          mapRef.current.animateToRegion({
            latitude: currentLocation.coords.latitude,
            longitude: currentLocation.coords.longitude,
            latitudeDelta: 0.005,
            longitudeDelta: 0.005
          }, 1000);
        }
      } catch (locError) {
        logEvent('INITIAL_LOCATION_ERROR', {
          message: 'Erreur lors de l\'obtention de la position initiale',
          error: locError instanceof Error ? locError.message : 'Erreur inconnue'
        });

        // Afficher une alerte plus informative
        Alert.alert(
          "Localisation indisponible",
          "Impossible d'obtenir votre position actuelle. Veuillez vérifier que les services de localisation sont activés et que l'application a accès à votre position.",
          [{ text: "OK" }]
        );

        setError('Localisation indisponible');
        return;
      }

      locationSubscription.current = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.BestForNavigation,
          timeInterval: LOCATION_CONFIG.UPDATE_INTERVAL,
          distanceInterval: 0, // Mise à jour basée sur le temps, pas la distance
        },
        async (newLocation) => {
          // Vérifier la précision minimale requise
          if (newLocation.coords.accuracy &&
              newLocation.coords.accuracy > LOCATION_CONFIG.ACCURACY_THRESHOLD) {
            logEvent('ACCURACY_ERROR', {
              accuracy: newLocation.coords.accuracy,
              threshold: LOCATION_CONFIG.ACCURACY_THRESHOLD,
              message: 'Précision insuffisante'
            });
            return;
          }

          // Mettre à jour la position actuelle
          setLocation(newLocation);

          // Utiliser notre nouvelle fonction de détection de mouvement
          const isSignificantMove = detectSignificantMovement(newLocation);

          if (isSignificantMove) {
            // Calculer la distance depuis la dernière zone stable
            const distanceFromStableZone = calculateDistance(
              movementStatus.stableZone?.latitude || 0,
              movementStatus.stableZone?.longitude || 0,
              newLocation.coords.latitude,
              newLocation.coords.longitude
            );

            logEvent('NEW_SYSTEM_MOVEMENT_DETECTED', {
              message: 'Mouvement significatif détecté par le nouveau système',
              coords: {
                latitude: newLocation.coords.latitude,
                longitude: newLocation.coords.longitude,
                accuracy: newLocation.coords.accuracy
              },
              stableZone: movementStatus.stableZone,
              distanceFromStableZone: `${distanceFromStableZone.toFixed(2)}m`,
              timestamp: new Date().toISOString()
            });
          }

          // Mettre à jour la carte si visible
          if (mapRef.current && mapVisible) {
            mapRef.current.animateToRegion({
              latitude: newLocation.coords.latitude,
              longitude: newLocation.coords.longitude,
              latitudeDelta: 0.005,
              longitudeDelta: 0.005
            }, 1000);
          }

          // Si le mode SOS est actif, envoyer les mises à jour nécessaires
          if (status === 'active') {
            // Mettre à jour le tracé GPS
            updateGpsTrace(newLocation);

            // Envoyer les mises à jour au webhook et à la base de données
            // en utilisant notre nouvelle logique de détection de mouvement
            try {
              const updatePromises = [];

              // Envoyer la mise à jour au webhook si nécessaire
              if (webhookUrl) {
                // La fonction sendLocationUpdate contient déjà notre nouvelle logique
                // et ne sera exécutée que si nécessaire
                updatePromises.push(sendLocationUpdate(newLocation));
              }

              // Mettre à jour la base de données
              if (currentSOSEvent) {
                updatePromises.push(updateSOSLocation(currentSOSEvent.id, newLocation));
              }

              await Promise.all(updatePromises);
            } catch (error) {
              logEvent('SOS_UPDATE_ERROR', {
                message: 'Erreur lors de la mise à jour SOS',
                error: error instanceof Error ? error.message : 'Erreur inconnue'
              });
            }
          }
        }
      );
    } catch (err) {
      logEvent('TRACKING_ERROR', {
        message: 'Erreur lors du démarrage du suivi',
        error: err instanceof Error ? err.message : 'Erreur inconnue'
      });
      setError('Impossible de suivre votre position');
    }
  };

  // Arrêter le suivi de localisation
  const stopLocationTracking = () => {
    if (locationSubscription.current) {
      locationSubscription.current.remove();
      locationSubscription.current = null;
    }
  };

  // Charger la configuration du webhook
  const loadWebhookConfiguration = async () => {
    try {
      // Rechercher d'abord la configuration spécifique pour SOS WEBHOOK URL
      const { data: sosData, error: sosError } = await supabase
        .from('webhook_configurations')
        .select('*')
        .eq('name', 'SOS WEBHOOK URL')
        .single();

      if (!sosError && sosData && sosData.url) {
        setWebhookUrl(sosData.url);
        setWebhookLoaded(true);
        logEvent('WEBHOOK_LOADED', {
          message: 'Webhook URL chargée depuis SOS WEBHOOK URL',
          url: sosData.url
        });
        return;
      }

      // Si la première recherche échoue, essayer avec l'ancien nom
      const { data, error } = await supabase
        .from('webhook_configurations')
        .select('*')
        .eq('name', 'n8n_emergency_webhook')
        .single();

      if (error) {
        logEvent('WEBHOOK_LOAD_ERROR', {
          message: 'Erreur lors du chargement du webhook',
          error: error.message
        });
        setWebhookUrl(FALLBACK_SOS_WEBHOOK_URL);
      } else if (data) {
        if (data.url) {
          setWebhookUrl(data.url);
        } else if (data.current_url) {
          setWebhookUrl(data.current_url);
        } else {
          setWebhookUrl(FALLBACK_SOS_WEBHOOK_URL);
        }
      } else {
        setWebhookUrl(FALLBACK_SOS_WEBHOOK_URL);
      }

      setWebhookLoaded(true);
      logEvent('WEBHOOK_LOADED', {
        message: 'Webhook URL chargée',
        url: webhookUrl || FALLBACK_SOS_WEBHOOK_URL
      });
    } catch (err) {
      logEvent('WEBHOOK_LOAD_ERROR', {
        message: 'Erreur lors du chargement du webhook',
        error: err instanceof Error ? err.message : 'Erreur inconnue'
      });
      setWebhookUrl(FALLBACK_SOS_WEBHOOK_URL);
      setWebhookLoaded(true);
    }
  };

  // Charger le profil utilisateur
  const loadUserProfile = async () => {
    try {
      if (!user) return;

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Erreur lors du chargement du profil:', error);
      } else if (data) {
        setUserProfile(data);
        console.log('Profil utilisateur chargé:', data);
      }

      setProfileLoaded(true);
    } catch (err) {
      console.error('Erreur lors du chargement du profil:', err);
      setProfileLoaded(true);
    }
  };

  // Fonction améliorée pour envoyer les mises à jour de position au webhook
  const sendLocationUpdate = async (locationData: Location.LocationObject) => {
    // Vérifier si l'URL du webhook est disponible
    if (!webhookUrl) {
      logEvent('WEBHOOK_MISSING', {
        message: 'Envoi webhook impossible - URL manquante',
        webhookLoaded: webhookLoaded,
        fallbackAvailable: !!FALLBACK_SOS_WEBHOOK_URL
      });

      // Essayer de recharger la configuration du webhook
      await loadWebhookConfiguration();

      // Vérifier à nouveau si l'URL est disponible après rechargement
      if (!webhookUrl) {
        logEvent('WEBHOOK_STILL_MISSING', {
          message: 'URL du webhook toujours manquante après rechargement',
          usingFallback: webhookUrl === FALLBACK_SOS_WEBHOOK_URL
        });

        // Utiliser l'URL de secours si disponible
        if (FALLBACK_SOS_WEBHOOK_URL) {
          setWebhookUrl(FALLBACK_SOS_WEBHOOK_URL);
          logEvent('USING_FALLBACK_WEBHOOK', {
            message: 'Utilisation de l\'URL de secours pour le webhook',
            fallbackUrl: FALLBACK_SOS_WEBHOOK_URL
          });
        } else {
          return; // Impossible d'envoyer sans URL
        }
      }
    }

    // Vérifier que l'URL est bien formée
    if (webhookUrl) {
      try {
        new URL(webhookUrl);
      } catch (e) {
        logEvent('INVALID_WEBHOOK_URL', {
          message: 'URL du webhook invalide',
          url: webhookUrl
        });

        // Utiliser l'URL de secours
        if (FALLBACK_SOS_WEBHOOK_URL) {
          setWebhookUrl(FALLBACK_SOS_WEBHOOK_URL);
          logEvent('USING_FALLBACK_WEBHOOK', {
            message: 'Utilisation de l\'URL de secours après détection d\'URL invalide',
            fallbackUrl: FALLBACK_SOS_WEBHOOK_URL
          });
        }
      }
    }

    // Charger le profil si nécessaire
    if (!userProfile) {
      try {
        logEvent('LOADING_USER_PROFILE', {
          message: 'Chargement du profil utilisateur pour l\'envoi webhook',
          userId: user?.id
        });

        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user?.id)
          .single();

        if (!error && data) {
          setUserProfile(data);
          logEvent('USER_PROFILE_LOADED', {
            message: 'Profil utilisateur chargé avec succès',
            profile: {
              id: data.id,
              email: data.email,
              hasName: !!data.first_name
            }
          });
        } else {
          logEvent('USER_PROFILE_LOAD_ERROR', {
            message: 'Erreur lors du chargement du profil utilisateur',
            error: error?.message
          });
        }
      } catch (err) {
        logEvent('PROFILE_LOAD_ERROR', {
          message: 'Exception lors du chargement du profil pour l\'envoi',
          error: err instanceof Error ? err.message : 'Erreur inconnue'
        });
      }
    }

    try {
      // Déterminer le type de mise à jour
      let isSignificantMovement = false;
      let isInitialAlert = false;
      let isPeriodicUpdate = false;
      let distanceFromLast = 0;
      let moveMessage = "";

      // Vérifier si c'est le premier envoi
      const isFirstSend = !lastSentLocation;

      if (isFirstSend) {
        // Si c'est le premier envoi, c'est une alerte initiale
        isInitialAlert = true;
        isSignificantMovement = false; // Pas un mouvement, mais une position initiale

        // Message d'alerte initiale avec plus d'informations pour l'agent IA
        moveMessage = `ALERTE SOS INITIALE! Une personne en danger a déclenché une alerte d'urgence. La victime est située aux coordonnées: ${locationData.coords.latitude.toFixed(6)}, ${locationData.coords.longitude.toFixed(6)}. Précision: ${locationData.coords.accuracy?.toFixed(2)}m. Veuillez envoyer de l'aide immédiatement à cette position.`;

        logEvent('INITIAL_SOS_ALERT', {
          message: 'Envoi de l\'alerte SOS initiale',
          coords: {
            latitude: locationData.coords.latitude,
            longitude: locationData.coords.longitude
          }
        });

        // Initialiser la zone de stabilité
        setMovementStatus({
          isMoving: false,
          lastSignificantMovement: new Date().getTime(),
          consecutiveStableReadings: 1,
          stableZone: {
            latitude: locationData.coords.latitude,
            longitude: locationData.coords.longitude,
            radius: LOCATION_CONFIG.STABLE_ZONE_RADIUS
          }
        });
      } else {
        // Si ce n'est pas le premier envoi, vérifier si c'est un mouvement significatif
        // en utilisant notre nouvelle fonction de détection précise
        isSignificantMovement = detectSignificantMovement(locationData);

        if (isSignificantMovement) {
          // Calculer la distance exacte depuis la dernière zone stable
          distanceFromLast = calculateDistance(
            movementStatus.stableZone?.latitude || lastSentLocation.latitude,
            movementStatus.stableZone?.longitude || lastSentLocation.longitude,
            locationData.coords.latitude,
            locationData.coords.longitude
          );

          // Message d'alerte de mouvement significatif avec plus d'informations pour l'agent IA
          moveMessage = `ALERTE DE MOUVEMENT! La victime s'est déplacée de ${distanceFromLast.toFixed(2)} mètres depuis la dernière position stable. Veuillez ajuster votre intervention en conséquence. Nouvelle position: ${locationData.coords.latitude.toFixed(6)}, ${locationData.coords.longitude.toFixed(6)}. Distance totale parcourue depuis le début: ${totalDistanceTraveled.toFixed(2)}m.`;
        } else {
          // Si ce n'est pas un mouvement significatif, vérifier si c'est une mise à jour périodique
          const timeSinceLastUpdate = lastSentLocation.timestamp
            ? (new Date().getTime() - lastSentLocation.timestamp) / 1000
            : 0;

          isPeriodicUpdate = timeSinceLastUpdate >= (LOCATION_CONFIG.PERIODIC_UPDATE_INTERVAL / 1000);

          if (isPeriodicUpdate) {
            // Vérifier s'il y a un petit mouvement non significatif
            if (distanceFromLast > 0.5) {
              moveMessage = `MISE À JOUR - LÉGER MOUVEMENT: La victime a effectué un léger déplacement de ${distanceFromLast.toFixed(2)} mètres. Position actuelle: ${locationData.coords.latitude.toFixed(6)}, ${locationData.coords.longitude.toFixed(6)}. La situation continue d'être surveillée.`;
            } else {
              moveMessage = `MISE À JOUR PÉRIODIQUE - POSITION STABLE: La victime est toujours aux coordonnées: ${locationData.coords.latitude.toFixed(6)}, ${locationData.coords.longitude.toFixed(6)}. Aucun mouvement significatif détecté. La situation continue d'être surveillée.`;
            }
          }
        }

        logEvent('MOVEMENT_EVALUATION', {
          isSignificantMovement,
          isPeriodicUpdate,
          currentPosition: {
            latitude: locationData.coords.latitude,
            longitude: locationData.coords.longitude
          },
          stableZone: movementStatus.stableZone,
          distanceFromStableZone: distanceFromLast.toFixed(2) + 'm',
          totalDistanceTraveled: totalDistanceTraveled.toFixed(2) + 'm'
        });
      }

      // Ne pas envoyer de mise à jour si ce n'est ni un mouvement significatif, ni une alerte initiale, ni une mise à jour périodique
      if (!isSignificantMovement && !isInitialAlert && !isPeriodicUpdate) {
        logEvent('UPDATE_SKIPPED', {
          message: 'Mise à jour ignorée - Aucun changement significatif',
          coords: {
            latitude: locationData.coords.latitude,
            longitude: locationData.coords.longitude
          }
        });
        return;
      }

      // Créer le payload avec l'ID de l'événement SOS si disponible
      const eventId = currentSOSEvent?.id || user?.id;
      const uniqueEventId = currentSOSEvent?.metadata?.unique_event_id || generateUniqueId();

      // Déterminer le type de mise à jour pour le payload avec plus de précision
      let updateType = 'location_update';

      if (isInitialAlert) {
        updateType = 'initial_alert';
      } else if (isSignificantMovement) {
        updateType = 'significant_movement';
      } else if (isPeriodicUpdate) {
        // Vérifier si c'est une mise à jour périodique avec ou sans mouvement
        if (distanceFromLast > 0.5) { // Petit mouvement détecté mais pas significatif
          updateType = 'minor_movement';
        } else {
          updateType = 'periodic_stationary';
        }
      }

      // Log pour le débogage du type de mise à jour
      logEvent('UPDATE_TYPE_DETERMINED', {
        updateType,
        isInitialAlert,
        isSignificantMovement,
        isPeriodicUpdate,
        distanceFromLast: distanceFromLast > 0 ? distanceFromLast : 0,
        totalDistanceTraveled: totalDistanceTraveled
      });

      // Calculer la distance totale parcourue depuis le début de l'alerte
      let totalDistanceFromStart = 0;
      if (currentSOSEvent && currentSOSEvent.initial_location) {
        totalDistanceFromStart = calculateDistance(
          currentSOSEvent.initial_location.latitude,
          currentSOSEvent.initial_location.longitude,
          locationData.coords.latitude,
          locationData.coords.longitude
        );
      }

      // Mettre à jour le tracé GPS et obtenir les distances calculées
      // Cette étape est cruciale pour s'assurer que totalDistanceTraveled est à jour
      const traceUpdate = updateGpsTrace(locationData);
      const currentTotalDistance = traceUpdate ? traceUpdate.totalDistance : totalDistanceTraveled;

      // Forcer la mise à jour de la trajectoire pour s'assurer que les distances sont calculées
      if (showTrajectory) {
        updateDisplayedTrajectory();
      }

      // Calculer la distance depuis le dernier point si ce n'est pas déjà fait
      if (distanceFromLast === 0 && lastSentLocation) {
        distanceFromLast = calculateDistance(
          lastSentLocation.latitude,
          lastSentLocation.longitude,
          locationData.coords.latitude,
          locationData.coords.longitude
        );
      }

      // Log pour le débogage des distances
      logEvent('DISTANCE_DEBUG', {
        totalDistanceTraveled: currentTotalDistance,
        totalDistanceFromStart: totalDistanceFromStart,
        distanceFromLast: distanceFromLast,
        isSignificantMovement: isSignificantMovement,
        hasTraceUpdate: !!traceUpdate,
        tracePointsCount: gpsTraceRef.current.length,
        lastSentLocation: lastSentLocation ? {
          latitude: lastSentLocation.latitude,
          longitude: lastSentLocation.longitude
        } : null
      });

      // Obtenir des informations supplémentaires
      const batteryLevel = await getBatteryLevel();
      const networkInfo = await getNetworkInfo();
      const currentDate = new Date();
      const localTimeString = currentDate.toLocaleString();

      // Créer un payload enrichi avec des informations supplémentaires pour l'agent IA
      const updatePayload = {
        event_id: eventId,
        unique_event_id: uniqueEventId,
        user_id: user?.id,
        update_type: updateType,
        timestamp: new Date().toISOString(),
        help_message: moveMessage || "J'ai besoin d'aide urgente! Voici ma position actualisée.",

        // Informations supplémentaires pour l'agent IA
        ai_agent_context: {
          alert_status: status,
          alert_type: updateType,
          is_first_alert: isInitialAlert,
          is_movement_alert: isSignificantMovement,
          is_periodic_update: isPeriodicUpdate,
          time_since_start: currentSOSEvent ?
            Math.floor((new Date().getTime() - new Date(currentSOSEvent.started_at).getTime()) / 1000) : 0,
          event_summary: `Alerte ${isInitialAlert ? 'initiale' : (isSignificantMovement ? 'de mouvement significatif' : 'de suivi')} pour une personne en danger.`
        },

        location: {
          latitude: locationData.coords.latitude,
          longitude: locationData.coords.longitude,
          accuracy: locationData.coords.accuracy,
          altitude: locationData.coords.altitude,
          heading: locationData.coords.heading,
          speed: locationData.coords.speed,
          timestamp: locationData.timestamp,
          // Ajouter des informations de localisation formatées pour l'agent IA
          formatted_coordinates: `${locationData.coords.latitude.toFixed(6)}, ${locationData.coords.longitude.toFixed(6)}`,
          google_maps_url: `https://www.google.com/maps/search/?api=1&query=${locationData.coords.latitude},${locationData.coords.longitude}`
        },

        user_profile: userProfile,

        movement_data: {
          is_significant: isSignificantMovement,
          // Toujours utiliser la distance calculée, qu'il s'agisse d'un mouvement significatif ou non
          distance_from_last: distanceFromLast > 0 ? distanceFromLast : (traceUpdate ? traceUpdate.distance : 0),
          distance_from_start: totalDistanceFromStart > 0 ? totalDistanceFromStart : 0,
          total_distance_traveled: currentTotalDistance > 0 ? currentTotalDistance : totalDistanceTraveled,
          previous_position: lastSentLocation ? {
            latitude: lastSentLocation.latitude,
            longitude: lastSentLocation.longitude,
            timestamp: lastSentLocation.timestamp ? new Date(lastSentLocation.timestamp).toISOString() : null
          } : (movementStatus.stableZone ? {
            latitude: movementStatus.stableZone.latitude,
            longitude: movementStatus.stableZone.longitude
          } : null),
          stable_zone_radius: movementStatus.stableZone?.radius || LOCATION_CONFIG.STABLE_ZONE_RADIUS,
          consecutive_stable_readings: movementStatus.consecutiveStableReadings || 1,
          // Ajouter des informations de mouvement formatées pour l'agent IA
          movement_summary: isSignificantMovement ?
            `Déplacement significatif de ${distanceFromLast.toFixed(2)}m détecté.` :
            (distanceFromLast > 0.5 ? `Léger déplacement de ${distanceFromLast.toFixed(2)}m détecté.` : `Position stable, aucun mouvement significatif.`),
          total_distance_formatted: {
            meters: totalDistanceTraveled.toFixed(2) + 'm',
            kilometers: (totalDistanceTraveled / 1000).toFixed(3) + 'km',
            millimeters: Math.round(totalDistanceTraveled * 1000) + 'mm'
          }
        },

        device_info: {
          platform: Platform.OS,
          battery_level: batteryLevel,
          network_status: networkInfo,
          local_time: localTimeString,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        },

        sos_event_id: currentSOSEvent?.id, // Ajouter l'ID de l'événement SOS

        // Ajouter des métadonnées pour l'agent IA
        metadata: {
          app_version: '1.0',
          webhook_version: '2.0',
          data_format: 'enhanced_for_ai_agent',
          alert_sequence: currentSOSEvent ?
            ((await supabase.from('sos_location_updates').select('id').eq('sos_event_id', currentSOSEvent.id)).data || []).length + 1 : 1
        }
      };

      // Log détaillé des données de mouvement pour le débogage
      logEvent('MOVEMENT_DATA_DEBUG', {
        is_significant: isSignificantMovement,
        distance_from_last: distanceFromLast > 0 ? distanceFromLast : (traceUpdate ? traceUpdate.distance : 0),
        distance_from_start: totalDistanceFromStart > 0 ? totalDistanceFromStart : 0,
        total_distance_traveled: currentTotalDistance > 0 ? currentTotalDistance : totalDistanceTraveled,
        previous_position: lastSentLocation ? {
          latitude: lastSentLocation.latitude,
          longitude: lastSentLocation.longitude
        } : (movementStatus.stableZone ? {
          latitude: movementStatus.stableZone.latitude,
          longitude: movementStatus.stableZone.longitude
        } : null),
        stable_zone_radius: movementStatus.stableZone?.radius || LOCATION_CONFIG.STABLE_ZONE_RADIUS,
        consecutive_stable_readings: movementStatus.consecutiveStableReadings || 1,
        current_position: {
          latitude: locationData.coords.latitude,
          longitude: locationData.coords.longitude
        }
      });

      logEvent('WEBHOOK_REQUEST', {
        url: webhookUrl,
        eventId: eventId,
        updateType: updateType,
        isMovementAlert: isSignificantMovement,
        isInitialAlert: isInitialAlert,
        isPeriodicUpdate: isPeriodicUpdate,
        coords: {
          latitude: locationData.coords.latitude,
          longitude: locationData.coords.longitude,
          accuracy: locationData.coords.accuracy
        },
        movement_data_summary: {
          is_significant: isSignificantMovement,
          distance_from_last: distanceFromLast > 0 ? distanceFromLast : (traceUpdate ? traceUpdate.distance : 0),
          total_distance_traveled: currentTotalDistance > 0 ? currentTotalDistance : totalDistanceTraveled
        }
      });

      // Vérifier que l'URL du webhook est disponible avant d'envoyer
      if (!webhookUrl) {
        logEvent('WEBHOOK_URL_MISSING', {
          message: 'Impossible d\'envoyer la mise à jour - URL du webhook manquante'
        });
        return;
      }

      // Ajouter un timeout pour éviter les blocages
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 secondes de timeout

      try {
        // URL du webhook garantie non-null à ce stade
        const webhookUrlString = webhookUrl as string;

        logEvent('WEBHOOK_SENDING', {
          message: 'Envoi de la requête au webhook',
          url: webhookUrlString,
          payloadSize: JSON.stringify(updatePayload).length
        });

        const response = await fetch(webhookUrlString, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updatePayload),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          // Mettre à jour la dernière position envoyée avec un timestamp
          const newLastSentLocation = {
            latitude: locationData.coords.latitude,
            longitude: locationData.coords.longitude,
            timestamp: new Date().getTime() // Ajouter le timestamp en millisecondes
          };

          setLastSentLocation(newLastSentLocation);

          // Log pour le débogage de la mise à jour de lastSentLocation
          logEvent('LAST_SENT_LOCATION_UPDATED', {
            previous: lastSentLocation,
            current: newLastSentLocation,
            movement_data: {
              is_significant: isSignificantMovement,
              distance_from_last: distanceFromLast > 0 ? distanceFromLast : (traceUpdate ? traceUpdate.distance : 0),
              total_distance_traveled: currentTotalDistance > 0 ? currentTotalDistance : totalDistanceTraveled
            }
          });

          logEvent('WEBHOOK_SUCCESS', {
            message: 'Mise à jour webhook réussie',
            status: response.status,
            isMovementAlert: isSignificantMovement,
            coords: {
              latitude: locationData.coords.latitude,
              longitude: locationData.coords.longitude
            }
          });
        } else {
          logEvent('WEBHOOK_ERROR', {
            message: 'Erreur webhook',
            status: response.status,
            statusText: response.statusText
          });
        }
      } catch (fetchErr) {
        clearTimeout(timeoutId);
        throw fetchErr;
      }
    } catch (err) {
      logEvent('WEBHOOK_ERROR', {
        message: 'Erreur lors de l\'envoi au webhook',
        error: err instanceof Error ? err.message : 'Erreur inconnue'
      });

      // En cas d'échec, essayer d'utiliser l'URL de secours
      if (webhookUrl !== FALLBACK_SOS_WEBHOOK_URL) {
        try {
          logEvent('WEBHOOK_FALLBACK', {
            message: 'Tentative avec l\'URL de secours',
            url: FALLBACK_SOS_WEBHOOK_URL
          });

          // Sauvegarder l'URL actuelle
          const currentUrl = webhookUrl;

          // Utiliser l'URL de secours
          setWebhookUrl(FALLBACK_SOS_WEBHOOK_URL);

          // Réessayer avec l'URL de secours
          await sendLocationUpdate(locationData);

          // Restaurer l'URL originale
          setWebhookUrl(currentUrl);
        } catch (fallbackErr) {
          logEvent('WEBHOOK_FALLBACK_ERROR', {
            message: 'Erreur avec l\'URL de secours',
            error: fallbackErr instanceof Error ? fallbackErr.message : 'Erreur inconnue'
          });
        }
      }
    }
  };

  // Animation de pulsation pour le bouton SOS actif
  useEffect(() => {
    let pulseAnimation: Animated.CompositeAnimation;

    if (status === 'active') {
      // Créer une animation de pulsation
      pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(buttonScale, {
            toValue: 1.05,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(buttonScale, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      );

      // Démarrer l'animation
      pulseAnimation.start();
    } else {
      // Réinitialiser l'animation
      Animated.timing(buttonScale, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }

    // Nettoyer l'animation lors du démontage
    return () => {
      if (pulseAnimation) {
        pulseAnimation.stop();
      }
    };
  }, [status, buttonScale]);

  // Animation d'entrée pour la section d'informations
  useEffect(() => {
    // Animer l'entrée de la section d'informations
    Animated.timing(infoSectionOpacity, {
      toValue: 1,
      duration: 800,
      delay: 300,
      useNativeDriver: true,
    }).start();
  }, [infoSectionOpacity]);

  // Animation pour le bouton lors de l'appui
  const animateButtonPress = (pressed: boolean) => {
    Animated.spring(buttonOpacity, {
      toValue: pressed ? 0.8 : 1,
      useNativeDriver: true,
    }).start();
  };

  // Fonction pour obtenir le niveau de batterie
  const getBatteryLevel = async (): Promise<number | null> => {
    try {
      // Cette fonction est une simulation car React Native Expo n'a pas d'API de batterie native
      // Dans une implémentation réelle, vous utiliseriez une bibliothèque comme expo-battery
      // Retourne une valeur aléatoire entre 0.1 et 1.0 pour simuler le niveau de batterie
      return Math.round(Math.random() * 90 + 10) / 100;
    } catch (err) {
      console.error('Erreur lors de la récupération du niveau de batterie:', err);
      return null;
    }
  };

  // Fonction pour obtenir le type de connexion réseau
  const getNetworkInfo = async (): Promise<string> => {
    try {
      // Cette fonction est une simulation car React Native Expo n'a pas d'API réseau native
      // Dans une implémentation réelle, vous utiliseriez une bibliothèque comme @react-native-community/netinfo
      // Retourne une valeur aléatoire parmi les types de connexion possibles
      const networkTypes = ['wifi', 'cellular', '4g', '3g', '2g', 'unknown'];
      return networkTypes[Math.floor(Math.random() * networkTypes.length)];
    } catch (err) {
      console.error('Erreur lors de la récupération des informations réseau:', err);
      return 'unknown';
    }
  };

  // Fonction pour générer un ID unique
  const generateUniqueId = (): string => {
    return 'sos-' +
      Date.now().toString(36) + '-' +
      Math.random().toString(36).substring(2, 9) + '-' +
      (user?.id ? user.id.substring(0, 8) : 'anon');
  };

  // Fonction pour créer un nouvel événement SOS
  const createSOSEvent = async (initialLocation: Location.LocationObject) => {
    try {
      // Obtenir des informations supplémentaires
      const batteryLevel = await getBatteryLevel();
      const networkInfo = await getNetworkInfo();
      const uniqueEventId = generateUniqueId();
      const currentDate = new Date();
      const localTimeString = currentDate.toLocaleString();

      // Créer l'événement SOS avec plus d'informations
      const { data: sosEvent, error: sosError } = await supabase
        .from('sos_events')
        .insert({
          user_id: user?.id,
          started_at: currentDate.toISOString(),
          initial_location: {
            latitude: initialLocation.coords.latitude,
            longitude: initialLocation.coords.longitude,
            accuracy: initialLocation.coords.accuracy,
            altitude: initialLocation.coords.altitude,
            heading: initialLocation.coords.heading,
            speed: initialLocation.coords.speed,
            timestamp: initialLocation.timestamp
          },
          status: 'active',
          metadata: {
            device_info: Platform.OS,
            app_version: '1.0',
            battery_level: batteryLevel,
            network_status: networkInfo,
            unique_event_id: uniqueEventId,
            local_time: localTimeString,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
          }
        })
        .select()
        .single();

      if (sosError) throw sosError;

      // Enregistrer l'activité avec plus de détails
      const { error: activityError } = await supabase
        .rpc('record_user_activity', {
          p_user_id: user?.id,
          p_activity_type: 'sos_created',
          p_activity_data: {
            sos_event_id: sosEvent.id,
            unique_event_id: uniqueEventId,
            location: {
              latitude: initialLocation.coords.latitude,
              longitude: initialLocation.coords.longitude,
              accuracy: initialLocation.coords.accuracy,
              altitude: initialLocation.coords.altitude,
              heading: initialLocation.coords.heading,
              speed: initialLocation.coords.speed
            },
            battery_level: batteryLevel,
            network_status: networkInfo,
            local_time: localTimeString
          },
          p_metadata: {
            device_info: Platform.OS,
            app_version: '1.0',
            unique_event_id: uniqueEventId
          }
        });

      if (activityError) {
        console.warn('Erreur lors de l\'enregistrement de l\'activité:', activityError);
        // Ne pas bloquer le processus si l'enregistrement de l'activité échoue
      }

      // Stocker l'événement SOS dans l'état local
      setCurrentSOSEvent(sosEvent);

      // Log pour le débogage
      logEvent('SOS_EVENT_CREATED', {
        event_id: sosEvent.id,
        unique_event_id: uniqueEventId,
        user_id: user?.id,
        battery_level: batteryLevel,
        network_status: networkInfo,
        local_time: localTimeString
      });

      return sosEvent;
    } catch (err) {
      console.error('Erreur lors de la création de l\'événement SOS:', err);
      throw err;
    }
  };

  // Fonction améliorée pour mettre à jour la position dans la base de données
  const updateSOSLocation = async (sosEventId: string, locationData: Location.LocationObject) => {
    try {
      // Vérifier que les données de localisation sont valides
      if (!locationData || !locationData.coords) {
        logEvent('DB_UPDATE_ERROR', {
          message: 'Données de localisation invalides',
          sosEventId
        });
        return;
      }

      logEvent('DB_UPDATE_START', {
        sosEventId,
        coords: {
          latitude: locationData.coords.latitude,
          longitude: locationData.coords.longitude,
          accuracy: locationData.coords.accuracy
        }
      });

      // Créer un timestamp ISO valide
      const timestamp = locationData.timestamp
        ? new Date(locationData.timestamp).toISOString()
        : new Date().toISOString();

      const { error } = await supabase
        .from('sos_location_updates')
        .insert({
          sos_event_id: sosEventId,
          latitude: locationData.coords.latitude,
          longitude: locationData.coords.longitude,
          accuracy: locationData.coords.accuracy,
          timestamp: timestamp,
          metadata: {
            altitude: locationData.coords.altitude,
            heading: locationData.coords.heading,
            speed: locationData.coords.speed,
            device_info: Platform.OS,
            app_version: '1.0',
            is_real_time: true
          }
        });

      if (error) {
        logEvent('DB_UPDATE_ERROR', {
          message: 'Erreur base de données',
          error: error instanceof Error ? error.message : 'Erreur inconnue',
          code: error?.code
        });

        // Si l'erreur est liée à la table manquante, essayer de la créer
        if (error?.code === '42P01') {
          logEvent('DB_TABLE_MISSING', {
            message: 'Table sos_location_updates manquante'
          });
        }

        throw error;
      }

      logEvent('DB_UPDATE_SUCCESS', {
        message: 'Position enregistrée dans la base de données',
        sosEventId,
        coords: {
          latitude: locationData.coords.latitude,
          longitude: locationData.coords.longitude
        }
      });

      // Mettre à jour l'état local
      setLocation(locationData);

    } catch (err) {
      logEvent('DB_UPDATE_ERROR', {
        message: 'Erreur lors de la mise à jour de la position',
        error: err instanceof Error ? err.message : 'Erreur inconnue'
      });
    }
  };

  // Fonction pour terminer l'événement SOS et envoyer une notification de fin
  const endSOSEvent = async (sosEventId: string) => {
    try {
      // Mettre à jour l'événement SOS dans la base de données
      const { error } = await supabase
        .from('sos_events')
        .update({
          ended_at: new Date().toISOString(),
          status: 'ended'
        })
        .eq('id', sosEventId);

      if (error) throw error;

      // Envoyer une notification de fin d'alerte au webhook
      if (webhookUrl && currentSOSEvent) {
        try {
          const endEventPayload = {
            event_id: sosEventId,
            unique_event_id: currentSOSEvent.metadata?.unique_event_id || generateUniqueId(),
            user_id: user?.id,
            update_type: 'sos_ended',
            timestamp: new Date().toISOString(),
            help_message: "ALERTE TERMINÉE: L'alerte SOS a été désactivée par l'utilisateur. Aucune autre action n'est requise.",

            // Informations supplémentaires pour l'agent IA
            ai_agent_context: {
              alert_status: 'ended',
              alert_type: 'sos_ended',
              is_first_alert: false,
              is_movement_alert: false,
              is_periodic_update: false,
              time_since_start: currentSOSEvent ?
                Math.floor((new Date().getTime() - new Date(currentSOSEvent.started_at).getTime()) / 1000) : 0,
              event_summary: "Fin d'alerte SOS. L'utilisateur a désactivé l'alerte manuellement."
            },

            location: location ? {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
              accuracy: location.coords.accuracy,
              timestamp: location.timestamp,
              formatted_coordinates: `${location.coords.latitude.toFixed(6)}, ${location.coords.longitude.toFixed(6)}`,
              google_maps_url: `https://www.google.com/maps/search/?api=1&query=${location.coords.latitude},${location.coords.longitude}`
            } : null,

            user_profile: userProfile,

            movement_data: {
              total_distance_traveled: totalDistanceTraveled,
              total_distance_formatted: {
                meters: totalDistanceTraveled.toFixed(2) + 'm',
                kilometers: (totalDistanceTraveled / 1000).toFixed(3) + 'km',
                millimeters: Math.round(totalDistanceTraveled * 1000) + 'mm'
              }
            },

            sos_event_id: sosEventId,

            metadata: {
              app_version: '1.0',
              webhook_version: '2.0',
              data_format: 'enhanced_for_ai_agent',
              event_duration_seconds: currentSOSEvent ?
                Math.floor((new Date().getTime() - new Date(currentSOSEvent.started_at).getTime()) / 1000) : 0
            }
          };

          logEvent('SOS_END_NOTIFICATION', {
            message: 'Envoi de la notification de fin d\'alerte SOS',
            sosEventId,
            totalDistance: totalDistanceTraveled.toFixed(2) + 'm'
          });

          await fetch(webhookUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(endEventPayload)
          });

        } catch (webhookErr) {
          logEvent('SOS_END_NOTIFICATION_ERROR', {
            message: 'Erreur lors de l\'envoi de la notification de fin d\'alerte',
            error: webhookErr instanceof Error ? webhookErr.message : 'Erreur inconnue'
          });
        }
      }

      // Arrêter les mises à jour de position
      if (locationUpdateInterval) {
        clearInterval(locationUpdateInterval);
        setLocationUpdateInterval(null);
      }

      setCurrentSOSEvent(null);

      logEvent('SOS_EVENT_ENDED', {
        message: 'Événement SOS terminé avec succès',
        sosEventId,
        totalDistance: totalDistanceTraveled.toFixed(2) + 'm',
        duration: currentSOSEvent ?
          Math.floor((new Date().getTime() - new Date(currentSOSEvent.started_at).getTime()) / 1000) + 's' : 'inconnu'
      });

    } catch (err) {
      logEvent('SOS_EVENT_END_ERROR', {
        message: 'Erreur lors de la fin de l\'événement SOS',
        error: err instanceof Error ? err.message : 'Erreur inconnue',
        sosEventId
      });
    }
  };

  // Modifier la fonction handleEmergencyTrigger
  const handleEmergencyTrigger = async () => {
    try {
      cleanup();
      setStatus('activating');
      setError(null);

      // Vérifier et attendre la position
      let currentLocation = location;
      if (!currentLocation) {
        try {
          // Vérifier d'abord si les services de localisation sont activés
          const servicesEnabled = await Location.hasServicesEnabledAsync();
          if (!servicesEnabled) {
            logEvent('SOS_LOCATION_SERVICES_DISABLED', {
              message: 'Services de localisation désactivés lors de l\'activation du SOS'
            });

            Alert.alert(
              "Services de localisation désactivés",
              "Impossible d'activer le mode SOS. Veuillez activer les services de localisation dans les paramètres de votre appareil et réessayer.",
              [{ text: "OK" }]
            );

            throw new Error('Services de localisation désactivés');
          }

          // Ajouter un timeout manuel pour éviter les attentes infinies
          const locationPromise = Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.High,
            mayShowUserSettingsDialog: true
          });

          const timeoutPromise = new Promise<null>((_, reject) => {
            setTimeout(() => reject(new Error('Timeout: Impossible d\'obtenir la position dans le délai imparti')), 10000);
          });

          // Utiliser Promise.race pour implémenter un timeout
          currentLocation = await Promise.race([locationPromise, timeoutPromise]) as Location.LocationObject;
          setLocation(currentLocation);

          logEvent('SOS_LOCATION_ACQUIRED', {
            coords: {
              latitude: currentLocation.coords.latitude,
              longitude: currentLocation.coords.longitude,
              accuracy: currentLocation.coords.accuracy
            }
          });
        } catch (locError) {
          logEvent('SOS_LOCATION_ERROR', {
            message: 'Erreur lors de la récupération de la position pour le SOS',
            error: locError instanceof Error ? locError.message : 'Erreur inconnue'
          });

          Alert.alert(
            "Localisation indisponible",
            "Impossible d'activer le mode SOS. Veuillez vérifier que les services de localisation sont activés et que l'application a accès à votre position.",
            [{ text: "OK" }]
          );

          throw new Error('Impossible d\'obtenir votre position. Veuillez vérifier que la localisation est activée.');
        }
      }

      setStatus('active');

      // Créer un nouvel événement SOS avec la position obtenue
      try {
        const sosEvent = await createSOSEvent(currentLocation);

        // Initialiser le tracé GPS
        gpsTraceRef.current = [{
          latitude: currentLocation.coords.latitude,
          longitude: currentLocation.coords.longitude,
          timestamp: currentLocation.timestamp
        }];
        setTotalDistanceTraveled(0);
        lastNotificationDistanceRef.current = 0;

        // Implémenter la période d'observation initiale de 30 secondes
        logEvent('INITIAL_OBSERVATION_PERIOD_START', {
          message: 'Début de la période d\'observation initiale de 30 secondes',
          duration: LOCATION_CONFIG.INITIAL_OBSERVATION_PERIOD / 1000,
          sos_event_id: sosEvent.id
        });

        // Créer un tableau pour stocker les positions pendant la période d'observation
        const observationPositions: Location.LocationObject[] = [];

        // Fonction pour observer la position pendant 30 secondes
        const observePosition = () => {
          const observationInterval = setInterval(async () => {
            try {
              const currentPos = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.BestForNavigation
              });

                  // Ajouter la position au tableau d'observation
              observationPositions.push(currentPos);

              // Mettre à jour la position dans la base de données
              await updateSOSLocation(sosEvent.id, currentPos);

              // Mettre à jour le tracé GPS et obtenir les distances calculées
              const traceUpdate = updateGpsTrace(currentPos);

              // Calculer la distance depuis le dernier point si disponible
              let distanceFromLast = 0;
              if (observationPositions.length > 1) {
                const prevPos = observationPositions[observationPositions.length - 2];
                distanceFromLast = calculateDistance(
                  prevPos.coords.latitude,
                  prevPos.coords.longitude,
                  currentPos.coords.latitude,
                  currentPos.coords.longitude
                );
              }

              logEvent('OBSERVATION_POSITION_RECORDED', {
                message: 'Position enregistrée pendant la période d\'observation',
                coords: {
                  latitude: currentPos.coords.latitude,
                  longitude: currentPos.coords.longitude,
                  accuracy: currentPos.coords.accuracy
                },
                distanceFromLast: `${distanceFromLast.toFixed(2)}m`,
                totalDistance: traceUpdate ? `${traceUpdate.totalDistance.toFixed(2)}m` : '0m',
                positionsCount: observationPositions.length
              });
            } catch (err) {
              logEvent('OBSERVATION_POSITION_ERROR', {
                message: 'Erreur lors de l\'enregistrement de la position d\'observation',
                error: err instanceof Error ? err.message : 'Erreur inconnue'
              });
            }
          }, 5000); // Enregistrer une position toutes les 5 secondes pendant la période d'observation

          // Arrêter l'observation après 30 secondes et démarrer le suivi régulier
          setTimeout(() => {
            clearInterval(observationInterval);

            logEvent('INITIAL_OBSERVATION_PERIOD_END', {
              message: 'Fin de la période d\'observation initiale',
              positionsCount: observationPositions.length,
              sos_event_id: sosEvent.id
            });

            // Calculer la distance totale parcourue pendant la période d'observation
            let totalObservationDistance = 0;
            if (observationPositions.length > 1) {
              for (let i = 1; i < observationPositions.length; i++) {
                const prevPos = observationPositions[i-1];
                const currPos = observationPositions[i];
                const distance = calculateDistance(
                  prevPos.coords.latitude,
                  prevPos.coords.longitude,
                  currPos.coords.latitude,
                  currPos.coords.longitude
                );
                totalObservationDistance += distance;
              }
            }

            // Mettre à jour la distance totale parcourue
            setTotalDistanceTraveled(totalObservationDistance);

            logEvent('OBSERVATION_DISTANCE_CALCULATED', {
              message: 'Distance parcourue pendant la période d\'observation',
              totalDistance: `${totalObservationDistance.toFixed(2)}m`,
              positionsCount: observationPositions.length
            });

            // Démarrer le suivi régulier après la période d'observation
            startRegularTracking(sosEvent.id);
          }, LOCATION_CONFIG.INITIAL_OBSERVATION_PERIOD);
        };

        // Fonction pour démarrer le suivi régulier après la période d'observation
        const startRegularTracking = (sosEventId: string) => {
          // Configurer les mises à jour de position régulières toutes les 60 secondes
          const interval = setInterval(async () => {
            // Récupérer la position actuelle plutôt que d'utiliser la variable location
            try {
              const currentPos = await Location.getCurrentPositionAsync({
                accuracy: Location.Accuracy.BestForNavigation
              });

              // Mettre à jour la position dans la base de données
              await updateSOSLocation(sosEventId, currentPos);

              // Mettre à jour le tracé GPS et obtenir les distances calculées
              const traceUpdate = updateGpsTrace(currentPos);

              // Log pour le débogage des distances pendant le suivi régulier
              logEvent('REGULAR_TRACKING_DISTANCE', {
                totalDistanceTraveled: traceUpdate ? traceUpdate.totalDistance : totalDistanceTraveled,
                distanceFromLast: traceUpdate ? traceUpdate.distance : 0,
                hasTraceUpdate: !!traceUpdate,
                tracePointsCount: gpsTraceRef.current.length
              });

              // Utiliser notre nouvelle logique de détection de mouvement
              // Vérifier si c'est un mouvement significatif ou s'il faut envoyer une mise à jour périodique
              const isSignificantMovement = detectSignificantMovement(currentPos);

              let shouldSendUpdate = false;

              if (isSignificantMovement) {
                // Si c'est un mouvement significatif, envoyer immédiatement une mise à jour
                shouldSendUpdate = true;

                logEvent('SIGNIFICANT_MOVEMENT_DETECTED_PERIODIC', {
                  message: 'Mouvement significatif détecté lors de la vérification périodique',
                  coords: {
                    latitude: currentPos.coords.latitude,
                    longitude: currentPos.coords.longitude
                  },
                  stableZone: movementStatus.stableZone,
                  distanceMoved: calculateDistance(
                    movementStatus.stableZone?.latitude || 0,
                    movementStatus.stableZone?.longitude || 0,
                    currentPos.coords.latitude,
                    currentPos.coords.longitude
                  ).toFixed(2) + 'm'
                });
              } else if (lastSentLocation) {
                // Si ce n'est pas un mouvement significatif, vérifier s'il faut envoyer une mise à jour périodique
                const timeSinceLastUpdate = lastSentLocation.timestamp
                  ? (new Date().getTime() - lastSentLocation.timestamp) / 1000
                  : 0;

                // Envoyer une mise à jour périodique toutes les 60 secondes
                shouldSendUpdate = timeSinceLastUpdate >= (LOCATION_CONFIG.PERIODIC_UPDATE_INTERVAL / 1000);

                logEvent('PERIODIC_UPDATE_CHECK', {
                  timeSinceLastUpdate: `${timeSinceLastUpdate.toFixed(1)}s`,
                  threshold: LOCATION_CONFIG.PERIODIC_UPDATE_INTERVAL / 1000,
                  shouldSendUpdate: shouldSendUpdate,
                  isMoving: movementStatus.isMoving,
                  consecutiveStableReadings: movementStatus.consecutiveStableReadings
                });
              } else {
                // Si c'est la première mise à jour, l'envoyer
                shouldSendUpdate = true;
              }

              // Envoyer la mise à jour au webhook si nécessaire
              if (webhookUrl && shouldSendUpdate) {
                await sendLocationUpdate(currentPos);
              }

              logEvent('PERIODIC_LOCATION_UPDATE', {
                message: 'Mise à jour périodique de la position',
                coords: {
                  latitude: currentPos.coords.latitude,
                  longitude: currentPos.coords.longitude,
                  accuracy: currentPos.coords.accuracy
                },
                totalDistanceTraveled: `${totalDistanceTraveled.toFixed(2)}m`,
                sentToWebhook: shouldSendUpdate
              });
            } catch (err) {
              logEvent('PERIODIC_LOCATION_ERROR', {
                message: 'Erreur lors de la mise à jour périodique',
                error: err instanceof Error ? err.message : 'Erreur inconnue'
              });
            }
          }, LOCATION_UPDATE_INTERVAL);

          setLocationUpdateInterval(interval);
        };

        // Activer l'affichage de la trajectoire
        setShowTrajectory(true);

        // Démarrer l'intervalle de mise à jour automatique de la trajectoire
        startTrajectoryUpdateInterval();

        // Log pour le débogage
        logEvent('SOS_TRAJECTORY_ACTIVATED', {
          message: 'Affichage de la trajectoire activé lors de l\'activation du mode SOS',
          sosStatus: 'active',
          gpsTracePoints: gpsTraceRef.current.length,
          autoUpdateEnabled: true
        });

        // Démarrer la période d'observation
        observePosition();

        // Envoyer la position initiale au webhook
        if (webhookUrl) {
          await sendLocationUpdate(currentLocation);
          // Initialiser les dernières coordonnées envoyées avec un timestamp
          setLastSentLocation({
            latitude: currentLocation.coords.latitude,
            longitude: currentLocation.coords.longitude,
            timestamp: new Date().getTime() // Ajouter le timestamp en millisecondes
          });
        }

        // Redémarrer le suivi de localisation pour des mises à jour plus fréquentes
        stopLocationTracking();
        startLocationTracking();

        // Démarrer l'enregistrement audio en continu (sans demander d'autorisation explicite)
        try {
          // Démarrer l'enregistrement avec un délai pour éviter les conflits
          // La fonction startAudioRecording vérifiera et demandera les permissions si nécessaire
          setTimeout(() => {
            startAudioRecording();

            // Configurer un intervalle pour redémarrer l'enregistrement périodiquement
            // au cas où il s'arrêterait
            recordingInterval.current = setInterval(() => {
              if (status === 'active' && !isRecording) {
                logEvent('AUDIO_RESTART', {
                  message: 'Redémarrage de l\'enregistrement audio'
                });
                startAudioRecording();
              }
            }, AUDIO_RECORDING_DURATION + 5000); // Vérifier 5 secondes après la fin prévue
          }, 1000);
        } catch (audioErr) {
          logEvent('AUDIO_SETUP_ERROR', {
            message: 'Erreur lors de la configuration de l\'audio',
            error: audioErr instanceof Error ? audioErr.message : 'Erreur inconnue'
          });
        }

        // Alerte de confirmation discrète
        Alert.alert(
          'Mode SOS activé',
          `Alerte déclenchée. Les services d'urgence ont été notifiés.`,
          [
            {
              text: 'OK',
              onPress: () => setStatus('active')
            },
            {
              text: 'Désactiver',
              style: 'destructive',
              onPress: () => showCancellationReasons()
            }
          ]
        );

        // Retour haptique
        if (Platform.OS !== 'web') {
          try {
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            Vibration.vibrate([0, 500, 200, 500]);
          } catch (err) {
            // Ignorer les erreurs sur les plateformes non supportées
          }
        }
      } catch (sosError: any) {
        console.error('Erreur lors de la création de l\'événement SOS:', sosError);

        // Si l'erreur est liée à la table manquante, utiliser le webhook uniquement
        if (sosError?.code === '42P01') {
          // Envoyer uniquement au webhook sans enregistrement en base
          if (webhookUrl) {
            await sendLocationUpdate(currentLocation);
            setStatus('active');
            return;
          }
        }

        throw sosError;
      }
    } catch (error: any) {
      console.error('Erreur lors du déclenchement du mode SOS:', error);
      setStatus('error');
      setError(error?.message || 'Une erreur est survenue lors du déclenchement du mode SOS.');

      Alert.alert(
        'Erreur',
        error?.message || 'Une erreur est survenue lors du déclenchement du mode SOS. Veuillez réessayer ou contacter directement les services d\'urgence.',
        [{ text: 'OK' }]
      );
    }
  };

  const handlePressIn = () => {
    // Si le mode SOS est déjà actif, demander confirmation avant de désactiver
    if (status === 'active') {
      showCancelConfirmation();
      return;
    }

    cleanup();
    setStatus('activating');
    setError(null);

    // Vibration immédiate
    if (Platform.OS !== 'web') {
      try {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
        Vibration.vibrate(100);
      } catch (err) {
        console.log('Erreur de retour haptique:', err);
      }
    }

    // Animation de progression
    let progress = 0;
    progressTimer.current = setInterval(() => {
      progress += 3.33;
      const newProgress = Math.min(progress, 100);
      setActivationProgress(newProgress);

      if (newProgress >= 100) {
        if (longPressTimer.current) {
          clearTimeout(longPressTimer.current);
          longPressTimer.current = null;
        }
        handleEmergencyTrigger();
      }
    }, 100);

    // Timer de déclenchement
    longPressTimer.current = setTimeout(() => {
      handleEmergencyTrigger();
    }, ACTIVATION_DURATION - 100);
  };

  // Nouvelle fonction pour gérer la confirmation d'annulation
  const showCancelConfirmation = () => {
    Alert.alert(
      'Confirmation',
      'Voulez-vous vraiment désactiver le mode SOS ? Les services d\'urgence ne recevront plus vos données de localisation et d\'audio.',
      [
        {
          text: 'Continuer SOS',
          style: 'cancel'
        },
        {
          text: 'Désactiver',
          style: 'destructive',
          onPress: () => showCancellationReasons()
        }
      ]
    );
  };

  // Fonction pour demander la raison de l'annulation
  const showCancellationReasons = () => {
    Alert.alert(
      'Raison de l\'annulation',
      'Pourquoi annulez-vous l\'alerte SOS ?',
      [
        {
          text: 'Fausse alerte',
          onPress: () => handleSOSCancellation('false_alarm')
        },
        {
          text: 'Situation résolue',
          onPress: () => handleSOSCancellation('situation_resolved')
        },
        {
          text: 'Aide arrivée',
          onPress: () => handleSOSCancellation('help_arrived')
        },
        {
          text: 'Test accidentel',
          onPress: () => handleSOSCancellation('accidental_test')
        },
        {
          text: 'Autre raison',
          onPress: () => handleSOSCancellation('other')
        }
      ]
    );
  };

  // Fonction pour gérer l'annulation du SOS avec raison
  const handleSOSCancellation = async (reason: string) => {
    try {
      // Envoyer une notification d'annulation au webhook
      if (webhookUrl) {
        const cancellationPayload = {
          event_id: user?.id,
          user_id: user?.id,
          update_type: 'sos_cancelled',
          timestamp: new Date().toISOString(),
          cancellation_reason: reason,
          help_message: `Alerte SOS annulée. Raison: ${reason}`,
          location: location ? {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
            accuracy: location.coords.accuracy,
            timestamp: location.timestamp
          } : null,
          user_profile: userProfile
        };

        logEvent('SOS_CANCELLATION', {
          reason: reason,
          timestamp: new Date().toISOString()
        });

        await fetch(webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(cancellationPayload)
        });
      }

      // Mettre à jour l'événement SOS dans la base de données
      if (currentSOSEvent) {
        await supabase
          .from('sos_events')
          .update({
            ended_at: new Date().toISOString(),
            status: 'cancelled',
            metadata: {
              ...currentSOSEvent.metadata,
              cancellation_reason: reason,
              cancelled_at: new Date().toISOString()
            }
          })
          .eq('id', currentSOSEvent.id);
      }

      // Nettoyer et réinitialiser
      cleanup();
      setStatus('inactive');
      setError(null);

      // Confirmation à l'utilisateur
      Alert.alert(
        'SOS désactivé',
        'Votre alerte SOS a été désactivée avec succès.',
        [{ text: 'OK' }]
      );

    } catch (err) {
      console.error('Erreur lors de l\'annulation du SOS:', err);
      // Nettoyer quand même en cas d'erreur
      cleanup();
      setStatus('inactive');
    }
  };

  const handlePressOut = () => {
    if (status === 'active') return;

    if (status === 'activating') {
      cleanup();
      setStatus('inactive');

      if (Platform.OS !== 'web') {
        try {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
        } catch (err) {
          // Ignorer les erreurs
        }
      }
    }
  };

  // Composants pour l'interface
  const emergencyActions = [
    {
      icon: 'location' as const,
      text: 'Votre position sera partagée avec vos contacts d\'urgence',
    },
    {
      icon: 'notifications' as const,
      text: 'Les autorités seront notifiées immédiatement',
    },
    {
      icon: 'people' as const,
      text: 'Vos contacts d\'urgence seront alertés',
    },
    {
      icon: 'mic' as const,
      text: 'L\'audio ambiant sera enregistré pour aider les secours',
    },
  ];

  // Modifier la fonction cleanup existante
  const cleanup = async () => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }

    if (progressTimer.current) {
      clearInterval(progressTimer.current);
      progressTimer.current = null;
    }

    if (locationUpdateInterval) {
      clearInterval(locationUpdateInterval);
      setLocationUpdateInterval(null);
    }

    // Arrêter l'enregistrement audio en cours
    if (recording) {
      try {
        await stopAudioRecording();
        logEvent('AUDIO_CLEANUP', {
          message: 'Enregistrement audio arrêté lors du nettoyage'
        });
      } catch (err) {
        logEvent('AUDIO_CLEANUP_ERROR', {
          message: 'Erreur lors de l\'arrêt de l\'enregistrement audio',
          error: err instanceof Error ? err.message : 'Erreur inconnue'
        });
      }
    }

    // Arrêter les enregistrements audio périodiques
    if (recordingInterval.current) {
      clearInterval(recordingInterval.current);
      recordingInterval.current = null;
    }

    if (currentSOSEvent) {
      endSOSEvent(currentSOSEvent.id);
    }

    // Réinitialiser les dernières coordonnées envoyées
    setLastSentLocation(null);

    // Réinitialiser le tracé GPS
    gpsTraceRef.current = [];
    setTotalDistanceTraveled(0);
    lastNotificationDistanceRef.current = 0;

    // Désactiver l'affichage de la trajectoire
    setShowTrajectory(false);
    setDisplayedTrajectory([]);

    // Arrêter l'intervalle de mise à jour automatique
    stopTrajectoryUpdateInterval();

    stopLocationTracking();
    setActivationProgress(0);
  };

  // Fonction utilitaire pour calculer la distance entre deux points (formule de Haversine)
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    // Vérifier les entrées pour éviter les calculs avec des valeurs invalides
    if (typeof lat1 !== 'number' || typeof lon1 !== 'number' ||
        typeof lat2 !== 'number' || typeof lon2 !== 'number' ||
        isNaN(lat1) || isNaN(lon1) || isNaN(lat2) || isNaN(lon2)) {
      logEvent('DISTANCE_CALCULATION_ERROR', {
        message: 'Tentative de calcul de distance avec des coordonnées invalides',
        coordinates: { lat1, lon1, lat2, lon2 }
      });
      return 0; // Retourner 0 en cas de coordonnées invalides
    }

    // Si les coordonnées sont identiques, retourner 0 directement
    if (lat1 === lat2 && lon1 === lon2) {
      return 0;
    }

    try {
      const R = 6371e3; // Rayon de la Terre en mètres
      const φ1 = lat1 * Math.PI/180;
      const φ2 = lat2 * Math.PI/180;
      const Δφ = (lat2-lat1) * Math.PI/180;
      const Δλ = (lon2-lon1) * Math.PI/180;

      const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

      const distance = R * c; // Distance en mètres

      // Log pour le débogage des calculs de distance
      if (distance > LOCATION_CONFIG.SIGNIFICANT_MOVEMENT_THRESHOLD) {
        logEvent('SIGNIFICANT_DISTANCE_CALCULATED', {
          from: { lat: lat1, lon: lon1 },
          to: { lat: lat2, lon: lon2 },
          distance: `${distance.toFixed(2)}m`,
          formula: 'Haversine'
        });
      }

      return distance;
    } catch (err) {
      logEvent('DISTANCE_CALCULATION_ERROR', {
        message: 'Erreur lors du calcul de distance',
        error: err instanceof Error ? err.message : 'Erreur inconnue',
        coordinates: { lat1, lon1, lat2, lon2 }
      });
      return 0; // Retourner 0 en cas d'erreur
    }
  };

  return (
    <ScrollView
      style={[styles.scrollContainer, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.scrollContentContainer}
      showsVerticalScrollIndicator={true}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Bouton d'Urgence SOS
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.gray[600] }]}>
            {status === 'active'
              ? 'Mode SOS activé'
              : status === 'activating'
              ? 'Maintenez le bouton pendant 3 secondes'
              : 'Appuyez longuement pour activer le mode SOS'}
          </Text>
        </View>

        {/* Carte pour afficher la position en temps réel */}
        {mapVisible && location && (
          <View style={styles.mapContainer}>
            <View style={styles.mapHeader}>
              <Text style={styles.mapTitle}>
                {status === 'active' && showTrajectory
                  ? 'Trajectoire en temps réel'
                  : 'Votre position en temps réel'}
              </Text>
              <View style={styles.mapHeaderButtons}>
                {status === 'active' && (
                  <TouchableOpacity
                    onPress={forceTrajectoryUpdate}
                    style={styles.mapButton}
                  >
                    <Ionicons name="refresh" size={20} color="#FF3B30" />
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  onPress={() => setMapVisible(!mapVisible)}
                  style={styles.mapToggle}
                >
                  <Ionicons name="chevron-up" size={24} color="#666" />
                </TouchableOpacity>
              </View>
            </View>
            <MapView
              ref={mapRef}
              provider={PROVIDER_GOOGLE}
              style={styles.map}
              initialRegion={{
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
                latitudeDelta: 0.005,
                longitudeDelta: 0.005,
              }}
              showsUserLocation={true}
              showsMyLocationButton={true}
              followsUserLocation={true}
            >
              <Marker
                coordinate={{
                  latitude: location.coords.latitude,
                  longitude: location.coords.longitude,
                }}
                title="Votre position"
                description={`Précision: ${location.coords.accuracy?.toFixed(2)}m`}
              />

              {/* Afficher la trajectoire si le mode SOS est actif et que showTrajectory est true */}
              {status === 'active' && showTrajectory && displayedTrajectory.length > 0 && (
                <Polyline
                  coordinates={displayedTrajectory}
                  strokeColor="#FF0000" // Rouge
                  strokeWidth={6}
                  lineDashPattern={[1]} // Ligne continue
                  lineCap="round"
                  geodesic={true}
                />
              )}

              {/* Marqueur pour le dernier point de la trajectoire */}
              {status === 'active' && showTrajectory && displayedTrajectory.length > 1 && (
                <Marker
                  coordinate={displayedTrajectory[displayedTrajectory.length - 1]}
                  title="Dernière position"
                  description={`Distance parcourue: ${totalDistanceTraveled.toFixed(2)}m`}
                  pinColor="blue"
                />
              )}
            </MapView>

            {/* Afficher les informations de distance si le mode SOS est actif et que showTrajectory est true */}
            {status === 'active' && showTrajectory && (
              <View style={styles.trajectoryInfoContainer}>
                <Text style={styles.trajectoryInfoTitle}>Trajectoire de la victime</Text>

                {displayedTrajectory.length > 1 ? (
                  <>
                    <View style={styles.trajectoryInfoRow}>
                      <Text style={styles.trajectoryInfoLabel}>Distance totale:</Text>
                      <Text style={styles.trajectoryInfoValue}>
                        {trajectoryDistance.kilometers.toFixed(3)} km ({trajectoryDistance.millimeters} mm)
                      </Text>
                    </View>
                    <View style={styles.trajectoryInfoRow}>
                      <Text style={styles.trajectoryInfoLabel}>Dernier déplacement:</Text>
                      <Text style={styles.trajectoryInfoValue}>
                        {trajectoryDistance.lastSegment.toFixed(2)} m
                      </Text>
                    </View>
                  </>
                ) : (
                  <View style={styles.trajectoryInfoRow}>
                    <Text style={styles.trajectoryInfoLabel}>En attente de déplacement...</Text>
                    <Text style={styles.trajectoryInfoValue}>
                      0.00 m
                    </Text>
                  </View>
                )}

                <View style={styles.trajectoryInfoRow}>
                  <Text style={styles.trajectoryInfoLabel}>Points enregistrés:</Text>
                  <Text style={styles.trajectoryInfoValue}>
                    {displayedTrajectory.length}
                  </Text>
                </View>
              </View>
            )}
            <View style={styles.coordinatesContainer}>
              <Text style={styles.coordinatesText}>
                Latitude: {location.coords.latitude.toFixed(6)}
              </Text>
              <Text style={styles.coordinatesText}>
                Longitude: {location.coords.longitude.toFixed(6)}
              </Text>
              {location.coords.accuracy && (
                <Text style={styles.coordinatesText}>
                  Précision: {location.coords.accuracy.toFixed(2)}m
                </Text>
              )}
            </View>
          </View>
        )}

        {/* Statut des connexions */}
        <View style={styles.statusContainer}>
          <View style={styles.statusItem}>
            <Ionicons
              name={locationPermission ? "checkmark-circle" : "close-circle"}
              size={18}
              color={locationPermission ? "#10B981" : "#F43F5E"}
            />
            <Text style={styles.statusText}>
              Localisation: {locationPermission ? "Active" : "Désactivée"}
            </Text>
          </View>
          <View style={styles.statusItem}>
            <Ionicons
              name={webhookLoaded ? "checkmark-circle" : "time"}
              size={18}
              color={webhookLoaded ? "#10B981" : "#F59E0B"}
            />
            <Text style={styles.statusText}>
              Service d'urgence: {webhookLoaded ? "Connecté" : "En attente"}
            </Text>
          </View>
          <View style={styles.statusItem}>
            <Ionicons
              name={profileLoaded ? "checkmark-circle" : "time"}
              size={18}
              color={profileLoaded ? "#10B981" : "#F59E0B"}
            />
            <Text style={styles.statusText}>
              Profil: {profileLoaded && userProfile ? "Chargé" : "En attente"}
            </Text>
          </View>
        </View>

        {/* Bouton pour accéder à l'historique des alertes */}
        <TouchableOpacity
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: 15,
            backgroundColor: theme.colors.cardBackground || '#f9fafb',
            borderRadius: 10,
            marginHorizontal: 20,
            marginBottom: 15,
            borderWidth: 1,
            borderColor: '#e5e7eb'
          }}
          onPress={() => {
            // Utiliser la navigation pour aller à la page d'historique
            router.push('/(app)/(emergency)/history');
          }}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Ionicons name="time-outline" size={20} color={theme.colors.primary} />
            <Text style={{
              marginLeft: 10,
              fontSize: 16,
              fontWeight: '500',
              color: theme.colors.text
            }}>
              Historique des alertes
            </Text>
          </View>
          <Ionicons name="chevron-forward" size={18} color={theme.colors.gray[500]} />
        </TouchableOpacity>

        {/* Bouton d'urgence avec animation */}
        <Animated.View
          style={{
            transform: [{ scale: buttonScale }],
            opacity: buttonOpacity,
          }}
        >
          <Pressable
            onPressIn={() => {
              animateButtonPress(true);
              handlePressIn();
            }}
            onPressOut={() => {
              animateButtonPress(false);
              handlePressOut();
            }}
            style={[
              styles.emergencyButton,
              {
                backgroundColor: status === 'active'
                  ? theme.colors.error
                  : status === 'activating'
                  ? theme.colors.secondary
                  : theme.colors.primary,
              },
            ]}
            delayLongPress={ACTIVATION_DURATION}
          >
            {status === 'activating' && (
              <View style={[
                styles.progressRing,
                { borderColor: theme.colors.white }
              ]}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      backgroundColor: theme.colors.white,
                      width: `${activationProgress}%`,
                    }
                  ]}
                />
              </View>
            )}
            {status === 'active' && (
              <View style={styles.recordingIndicator}>
                <ActivityIndicator color="white" />
                {isRecording && (
                  <View style={styles.audioRecordingIndicator}>
                    <Ionicons name="mic" size={14} color="white" />
                  </View>
                )}
              </View>
            )}
            <Ionicons
              name={status === 'active' ? 'stop-circle' : 'alert'}
              size={48}
              color="white"
            />
            <Text style={styles.buttonText}>
              {status === 'active' ? 'ARRÊTER SOS' :
              status === 'activating' ? 'MAINTENIR...' :
              'URGENCE'}
            </Text>
          </Pressable>
        </Animated.View>

        {error && (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: theme.colors.error }]}>
              {error}
            </Text>
            <TouchableOpacity
              style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => {
                setError(null);
                setStatus('inactive');
                cleanup();
              }}>
              <Text style={styles.retryButtonText}>Réessayer</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Section d'informations avec animation */}
        <Animated.View style={[styles.infoSection, { opacity: infoSectionOpacity }]}>
          <Text style={[styles.infoTitle, { color: theme.colors.text }]}>
            En appuyant sur ce bouton:
          </Text>
          <View style={styles.infoList}>
            {emergencyActions.map((action, index) => (
              <View key={index} style={styles.infoItem}>
                <Ionicons
                  name={action.icon}
                  size={24}
                  color={theme.colors.primary}
                  style={styles.infoIcon}
                />
                <Text style={[styles.infoText, { color: theme.colors.gray[700] }]}>
                  {action.text}
                </Text>
              </View>
            ))}
          </View>

          {/* Bouton pour tester le webhook */}
          <TouchableOpacity
            style={[styles.testWebhookButton, { backgroundColor: theme.colors.primary }]}
            onPress={async () => {
              const success = await testWebhook();
              Alert.alert(
                success ? "Test réussi" : "Échec du test",
                success ?
                  "Le webhook SOS fonctionne correctement. URL: " + webhookUrl :
                  "Impossible de contacter le webhook. Veuillez vérifier la connexion internet et l'URL du webhook."
              );
            }}
          >
            <Text style={styles.testWebhookButtonText}>Tester la connexion au service d'urgence</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: Platform.OS === 'ios' ? 30 : 20,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingBottom: 40,
    flexGrow: 1,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  mapContainer: {
    marginHorizontal: 20,
    marginBottom: 15,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  mapHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#f3f4f6',
  },
  mapTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  mapHeaderButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  mapButton: {
    padding: 5,
    marginRight: 5,
  },
  mapToggle: {
    padding: 5,
  },
  map: {
    height: 250,
    width: '100%',
  },
  coordinatesContainer: {
    padding: 10,
    backgroundColor: '#f3f4f6',
  },
  coordinatesText: {
    fontSize: 12,
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    color: '#333',
  },
  statusContainer: {
    margin: 20,
    marginTop: 5,
    marginBottom: 10,
    backgroundColor: '#f9fafb',
    borderRadius: 10,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  statusText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#4b5563',
  },
  emergencyButton: {
    width: 200,
    height: 200,
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    marginVertical: 30,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  progressRing: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 100,
    borderWidth: 3,
    overflow: 'hidden',
  },
  progressFill: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    borderRadius: 100,
  },
  buttonText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 8,
  },
  recordingIndicator: {
    position: 'absolute',
    top: 20,
    right: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  audioRecordingIndicator: {
    backgroundColor: '#FF3B30',
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 5,
  },
  infoSection: {
    padding: 20,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  infoList: {
    gap: 15,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#f3f4f6',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  infoIcon: {
    marginRight: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 16,
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
    marginHorizontal: 20,
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#FF3B30',
  },
  errorText: {
    fontSize: 16,
    marginBottom: 10,
    textAlign: 'center',
  },
  retryButton: {
    padding: 8,
    paddingHorizontal: 16,
    borderRadius: 16,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  testWebhookButton: {
    marginTop: 20,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  testWebhookButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  // Styles pour la trajectoire et les informations de distance
  trajectoryInfoContainer: {
    backgroundColor: 'rgba(255, 0, 0, 0.1)',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  trajectoryInfoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF3B30',
    marginBottom: 8,
    textAlign: 'center',
  },
  trajectoryInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
    paddingVertical: 3,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(229, 231, 235, 0.5)',
  },
  trajectoryInfoLabel: {
    fontSize: 13,
    color: '#4b5563',
    fontWeight: '600',
  },
  trajectoryInfoValue: {
    fontSize: 13,
    color: '#1F2937',
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    fontWeight: '500',
  }
});