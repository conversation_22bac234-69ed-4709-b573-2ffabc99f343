import React from 'react';
import { Stack } from 'expo-router';
import { useTheme } from '../../../context/theme';

export default function EmergencyLayout() {
  const { theme } = useTheme();
  
  return (
    <Stack
      screenOptions={{
        headerShown: true,
        headerStyle: {
          backgroundColor: theme.colors.error,
        },
        headerTintColor: theme.colors.white,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
        animation: 'slide_from_right',
      }}
    >
      <Stack.Screen 
        name="index" 
        options={{
          title: "Urgence"
        }}
      />
      <Stack.Screen 
        name="emergency" 
        options={{
          title: "Alerte SOS",
          presentation: "modal",
          animation: "slide_from_bottom"
        }}
      />
      <Stack.Screen 
        name="first-aid" 
        options={{
          title: "Premiers Secours"
        }}
      />
    </Stack>
  );
} 