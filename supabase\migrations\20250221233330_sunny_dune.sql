/*
  # Fix webhook configuration and status check

  1. Changes
    - Update webhook URL configuration
    - Improve error handling in status check function
    - Add better type safety
    
  2. Security
    - Maintain existing RLS policies
    - Ensure secure URL validation
*/

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS check_webhook_status(text);

-- <PERSON>reate improved webhook status check function
CREATE OR REPLACE FUNCTION check_webhook_status(webhook_name TEXT)
RETURNS TABLE (
    is_available boolean,
    current_url varchar(500),
    error_details text
) AS $$
DECLARE
    webhook_record webhook_urls%ROWTYPE;
BEGIN
    -- Get webhook record
    SELECT * INTO webhook_record
    FROM webhook_urls
    WHERE name = webhook_name
    AND is_active = true;

    -- Check if webhook exists and is properly configured
    IF webhook_record IS NULL THEN
        RETURN QUERY SELECT 
            false::boolean, 
            NULL::varchar(500), 
            'Configuration non disponible'::text;
        RETURN;
    END IF;

    -- Return status based on webhook health
    RETURN QUERY
    SELECT 
        (webhook_record.is_active AND webhook_record.error_count < webhook_record.max_retries)::boolean,
        CASE 
            WHEN webhook_record.error_count >= webhook_record.max_retries AND webhook_record.backup_url IS NOT NULL 
            THEN webhook_record.backup_url::varchar(500)
            ELSE webhook_record.url::varchar(500)
        END,
        COALESCE(webhook_record.error_message, '')::text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update webhook configuration
UPDATE webhook_urls
SET 
    url = 'https://n8n-dw1u.onrender.com/webhook-test/chat',
    backup_url = 'https://backup-n8n-dw1u.onrender.com/webhook-test/chat',
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    consecutive_failures = 0,
    last_successful_response = CURRENT_TIMESTAMP,
    health_check_enabled = true,
    max_retries = 3,
    retry_delay_base = 2000,
    health_check_interval = 300
WHERE name = 'n8n_chatbot_agent';