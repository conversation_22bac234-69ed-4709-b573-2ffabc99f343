import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
  Alert,
  TextInput,
  Image,
} from 'react-native';
import { router } from 'expo-router';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { supabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { format, addDays, setHours, setMinutes } from 'date-fns';
import { fr } from 'date-fns/locale';
import { showAnimatedErrorAlert } from '../../../app/components/SweetAlert';

type Professional = {
  id: string;
  type: 'doctor' | 'lawyer';
  speciality: string;
  user: {
    first_name: string;
    last_name: string;
  };
  availability: string[];
};

type TimeSlot = {
  time: Date;
  available: boolean;
};

export default function BookAppointmentScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedTime, setSelectedTime] = useState<Date | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [bookingInProgress, setBookingInProgress] = useState(false);

  // États pour le formulaire de rendez-vous
  const [appointmentNote, setAppointmentNote] = useState<string>('');
  const [appointmentReason, setAppointmentReason] = useState<string>('');
  const [appointmentType, setAppointmentType] = useState<'medical' | 'legal'>('medical');
  const [uploadingImage, setUploadingImage] = useState(false);

  useEffect(() => {
    // On charge toujours les professionnels en arrière-plan pour l'agent AI
    loadProfessionals();
  }, []);

  const loadProfessionals = async () => {
    try {
      setLoading(true);
      setError(null);

      // Requête simple pour récupérer les professionnels sans utiliser de relations
      const { data: professionalsData, error: professionalsError } = await supabase
        .from('professionals')
        .select('id, type, speciality, availability, user_id')
        .order('type');

      if (professionalsError) throw professionalsError;

      if (!professionalsData || professionalsData.length === 0) {
        setLoading(false);
        return;
      }

      // Récupérer les user_ids des professionnels
      const userIds = professionalsData
        .map(prof => prof.user_id)
        .filter(id => id !== null && id !== undefined);

      // Si nous avons des user_ids, récupérer les informations des utilisateurs depuis profiles
      let profilesData: any[] = [];
      if (userIds.length > 0) {
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, first_name, last_name')
          .in('id', userIds);

        if (!profilesError && profiles) {
          profilesData = profiles;
        } else {
          console.error('Error fetching profiles:', profilesError);
        }
      }

      // Créer un dictionnaire pour un accès rapide aux données des profils
      const profilesById: Record<string, any> = {};
      profilesData.forEach(profile => {
        if (profile.id) {
          profilesById[profile.id] = profile;
        }
      });

      // Associer les professionnels à leurs profils utilisateur
      const formattedData = professionalsData.map(professional => {
        let firstName = 'Professionnel';
        let lastName = 'de santé';

        if (professional.user_id && profilesById[professional.user_id]) {
          const profile = profilesById[professional.user_id];
          firstName = profile.first_name || firstName;
          lastName = profile.last_name || lastName;
        }

        return {
          id: professional.id,
          type: professional.type as 'doctor' | 'lawyer',
          speciality: professional.speciality || 'Non spécifié',
          availability: professional.availability || [],
          user: {
            first_name: firstName,
            last_name: lastName
          }
        };
      });

      // Les professionnels sont chargés mais nous n'avons plus besoin de les stocker
    } catch (err) {
      console.error('Error loading professionals:', err);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  const getAvailableTimeSlots = (date: Date): TimeSlot[] => {
    const slots: TimeSlot[] = [];
    const startHour = 9;
    const endHour = 17;
    const interval = 30; // minutes

    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute = 0; minute < 60; minute += interval) {
        const time = setMinutes(setHours(date, hour), minute);
        slots.push({
          time,
          available: true, // You would check against actual availability here
        });
      }
    }

    return slots;
  };





  const handleBookAppointment = async () => {
    // Ajouter des logs pour déboguer le problème
    console.log("Tentative de prise de rendez-vous");
    console.log("Heure sélectionnée:", selectedTime);
    console.log("Motif:", appointmentReason);

    if (!selectedTime) {
      setError('Veuillez sélectionner un horaire');
      return;
    }

    if (!appointmentReason) {
      setError('Veuillez indiquer le motif de votre rendez-vous');
      return;
    }

    try {
      setBookingInProgress(true);
      setError(null);

      // Générer un ID unique pour cette demande de rendez-vous
      const requestId = `req_${Date.now()}_${Math.floor(Math.random() * 1000)}`;

      // Enregistrer l'activité de demande de rendez-vous
      if (user) {
        await supabase.rpc(
          'record_user_activity',
          {
            p_activity_type: 'appointment_requested',
            p_metadata: JSON.stringify({
              requested_date: selectedTime.toISOString(),
              appointment_reason: appointmentReason,
              appointment_type: appointmentType,
              has_notes: !!appointmentNote.trim(),
              request_id: requestId
            })
          }
        );
      }

      // Rediriger vers la page de conversation avec l'agent AI
      const params: {
        requestId: string;
        dateTime: string;
        reason: string;
        type: string;
        notes?: string;
      } = {
        requestId,
        dateTime: selectedTime.toISOString(),
        reason: appointmentReason.trim(),
        type: appointmentType
      };

      // Ajouter des notes si présentes
      if (appointmentNote.trim()) {
        params.notes = appointmentNote.trim();
      }

      // Rediriger vers la page de conversation AI
      router.push({
        pathname: '/(app)/appointments/ai-conversation',
        params
      });

    } catch (err: any) {
      console.error('Error initiating appointment request:', err);
      setError('Erreur lors de l\'initialisation de la demande de rendez-vous: ' + (err.message || err));

      showAnimatedErrorAlert(
        'Une erreur est survenue lors de l\'initialisation de votre demande. Veuillez réessayer.',
        'D\'accord'
      );
    } finally {
      setBookingInProgress(false);
    }
  };

  // Ajoutons une fonction pour gérer spécifiquement les clics du bouton
  const onBookButtonPress = () => {
    console.log("Bouton de réservation cliqué");
    console.log("État actuel - loading:", loading, "bookingInProgress:", bookingInProgress, "uploadingImage:", uploadingImage);
    console.log("Heure sélectionnée:", selectedTime ? selectedTime.toISOString() : null);
    console.log("Motif saisi:", appointmentReason);

    // Vérifications explicites
    if (!selectedTime) {
      showAnimatedErrorAlert(
        "Veuillez sélectionner un horaire pour le rendez-vous",
        "Compris"
      );
      return;
    }

    if (!appointmentReason) {
      showAnimatedErrorAlert(
        "Veuillez indiquer le motif de votre rendez-vous",
        "D'accord"
      );
      return;
    }

    // Si tout est bon, appeler la fonction de réservation
    handleBookAppointment();
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}>
          <Ionicons
            name="arrow-back"
            size={24}
            color={theme.colors.text}
          />
        </TouchableOpacity>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Prendre rendez-vous
        </Text>
      </View>

      {error ? (
        <View style={styles.centerContent}>
          <Text style={[styles.error, { color: theme.colors.error }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
            onPress={loadProfessionals}>
            <Text style={styles.retryButtonText}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : loading ? (
        <View style={styles.centerContent}>
          <Text style={{ color: theme.colors.text }}>
            Chargement...
          </Text>
        </View>
      ) : (
        <ScrollView style={styles.content}>
          <Text style={[styles.sectionHeader, { color: theme.colors.text }]}>
            Bomoko Mobile  vous attribuera automatiquement le professionnel le plus adapté à votre besoin
          </Text>

          {/* Type de rendez-vous */}
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Type de consultation
          </Text>

          <View style={styles.typeButtonsContainer}>
            <TouchableOpacity
              style={[
                styles.typeButton,
                {
                  backgroundColor: appointmentType === 'medical'
                    ? theme.colors.primary
                    : theme.colors.surface,
                },
              ]}
              onPress={() => setAppointmentType('medical')}>
              <Ionicons
                name="medical-outline"
                size={24}
                color={appointmentType === 'medical' ? 'white' : theme.colors.text}
              />
              <Text
                style={[
                  styles.typeButtonText,
                  {
                    color: appointmentType === 'medical' ? 'white' : theme.colors.text,
                  },
                ]}>
                Médicale
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.typeButton,
                {
                  backgroundColor: appointmentType === 'legal'
                    ? theme.colors.primary
                    : theme.colors.surface,
                },
              ]}
              onPress={() => setAppointmentType('legal')}>
              <Ionicons
                name="document-text-outline"
                size={24}
                color={appointmentType === 'legal' ? 'white' : theme.colors.text}
              />
              <Text
                style={[
                  styles.typeButtonText,
                  {
                    color: appointmentType === 'legal' ? 'white' : theme.colors.text,
                  },
                ]}>
                Juridique
              </Text>
            </TouchableOpacity>
          </View>

          {/* Motif du rendez-vous */}
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Motif du rendez-vous *
          </Text>

          <TextInput
            style={[
              styles.reasonInput,
              {
                backgroundColor: theme.colors.surface,
                color: theme.colors.text,
                borderColor: theme.colors.gray[300]
              }
            ]}
            placeholder="Décrivez brièvement le motif de votre rendez-vous..."
            placeholderTextColor={theme.colors.gray[400]}
            value={appointmentReason}
            onChangeText={setAppointmentReason}
            maxLength={100}
          />

          {/* Sélection de date */}
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Choisir une date
          </Text>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.datesList}>
            {Array.from({ length: 7 }).map((_, index) => {
              const date = addDays(new Date(), index);
              return (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.dateCard,
                    {
                      backgroundColor:
                        selectedDate.toDateString() === date.toDateString()
                          ? theme.colors.primary
                          : theme.colors.surface,
                    },
                  ]}
                  onPress={() => setSelectedDate(date)}>
                  <Text
                    style={[
                      styles.dateDay,
                      {
                        color:
                          selectedDate.toDateString() === date.toDateString()
                            ? 'white'
                            : theme.colors.text,
                      },
                    ]}>
                    {format(date, 'EEE')}
                  </Text>
                  <Text
                    style={[
                      styles.dateNumber,
                      {
                        color:
                          selectedDate.toDateString() === date.toDateString()
                            ? 'white'
                            : theme.colors.text,
                      },
                    ]}>
                    {format(date, 'd')}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </ScrollView>

          {/* Sélection d'horaire */}
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Choisir un horaire
          </Text>

          <View style={styles.timeSlotsList}>
            {getAvailableTimeSlots(selectedDate).map((slot, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.timeSlot,
                  {
                    backgroundColor:
                      selectedTime?.getTime() === slot.time.getTime()
                        ? theme.colors.primary
                        : theme.colors.surface,
                    opacity: slot.available ? 1 : 0.5,
                  },
                ]}
                disabled={!slot.available}
                onPress={() => setSelectedTime(slot.time)}>
                <Text
                  style={[
                    styles.timeText,
                    {
                      color:
                        selectedTime?.getTime() === slot.time.getTime()
                          ? 'white'
                          : theme.colors.text,
                    },
                  ]}>
                  {format(slot.time, 'HH:mm')}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* NOUVEAU: Champ pour les notes */}
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Informations complémentaires (optionnel)
          </Text>

          <TextInput
            style={[
              styles.noteInput,
              {
                backgroundColor: theme.colors.surface,
                color: theme.colors.text,
                borderColor: theme.colors.gray[300]
              }
            ]}
            placeholder="Détails supplémentaires pour aider à mieux comprendre votre demande..."
            placeholderTextColor={theme.colors.gray[400]}
            value={appointmentNote}
            onChangeText={setAppointmentNote}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />


          <TouchableOpacity
            style={[
              styles.bookButton,
              {
                backgroundColor: theme.colors.primary,
                opacity: (bookingInProgress || uploadingImage || !selectedTime || !appointmentReason) ? 0.7 : 1,
              },
            ]}
            onPress={onBookButtonPress}
            disabled={bookingInProgress || uploadingImage}>
            <Text style={styles.bookButtonText}>
              {bookingInProgress
                ? 'Envoi en cours...'
                : uploadingImage
                ? 'Téléchargement de l\'image...'
                : 'Demander un rendez-vous'}
            </Text>
          </TouchableOpacity>
        </ScrollView>
      )}
    </View>
  );
}

// Styles existants et nouveaux
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
  },
  backButton: {
    marginRight: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  error: {
    marginBottom: 15,
    textAlign: 'center',
  },
  retryButton: {
    padding: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: '400',
    marginBottom: 20,
    textAlign: 'center',
    lineHeight: 22,
  },
  typeButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  typeButton: {
    flex: 0.48,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 8,
    gap: 10,
  },
  typeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  reasonInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
    fontSize: 16,
  },
  noteInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  imageButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  imageButton: {
    flex: 0.48,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 8,
    gap: 10,
  },
  imageButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  imagePreviewContainer: {
    position: 'relative',
    marginBottom: 20,
    borderRadius: 8,
    overflow: 'hidden',
  },
  imagePreview: {
    width: '100%',
    height: 200,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    padding: 5,
  },
  uploadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadingText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  datesList: {
    marginBottom: 20,
  },
  dateCard: {
    width: 60,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    marginRight: 10,
  },
  dateDay: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  dateNumber: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  timeSlotsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginBottom: 20,
  },
  timeSlot: {
    width: '30%',
    padding: 10,
    borderRadius: 8,
    alignItems: 'center',
  },
  timeText: {
    fontSize: 16,
    fontWeight: '600',
  },
  bookButton: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  bookButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});