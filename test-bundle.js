// Test bundle to verify ws polyfill is working
console.log('Testing ws polyfill...');

// Test direct ws import
try {
  const ws = require('./ws-polyfill/index.js');
  console.log('✅ ws polyfill loaded successfully');
  console.log('ws type:', typeof ws);
  console.log('ws.WebSocket type:', typeof ws.WebSocket);
  console.log('ws.CONNECTING:', ws.CONNECTING);
} catch (error) {
  console.error('❌ Error loading ws polyfill:', error.message);
}

// Test ws/lib/stream import
try {
  const wsStream = require('./ws-polyfill/lib/stream.js');
  console.log('✅ ws/lib/stream polyfill loaded successfully');
  console.log('wsStream type:', typeof wsStream);
} catch (error) {
  console.error('❌ Error loading ws/lib/stream polyfill:', error.message);
}

// Test that the polyfill prevents Node.js module errors
try {
  // This would normally fail in React Native
  const MockWebSocket = require('./ws-polyfill/index.js');
  const ws = new MockWebSocket('ws://example.com');
  console.log('✅ MockWebSocket instantiated successfully');
  console.log('WebSocket readyState:', ws.readyState);
} catch (error) {
  console.error('❌ Error instantiating MockWebSocket:', error.message);
}

console.log('✅ All tests completed successfully - ws polyfill is working!');
