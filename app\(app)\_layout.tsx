import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Stack } from 'expo-router';
import { useTheme } from '../../context/theme';
import ProfileHeader from '../components/ProfileHeader';

export default function AppLayout() {
  const { theme } = useTheme();

  return (
    <View style={styles.container}>
      <ProfileHeader />
      <Stack
        screenOptions={{
          headerShown: false, // Cacher l'en-tête par défaut car nous utilisons notre propre en-tête
          headerStyle: {
            backgroundColor: theme.colors.background,
          },
          headerTintColor: theme.colors.primary,
          headerTitleStyle: {
            fontWeight: 'bold',
          },
          headerBackTitle: '',
          contentStyle: {
            backgroundColor: theme.colors.background,
          }
        }}
      >
        <Stack.Screen
          name="(emergency)"
          options={{
            title: "Urgence",
            headerShown: true,
          }}
        />
        <Stack.Screen
          name="index"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="onboarding/profile-setup"
          options={{
            title: "Configuration du profil",
            headerShown: true,
          }}
        />
        <Stack.Screen
          name="denunciation/index"
          options={{
            title: "Signaler une violence",
            headerShown: true,
          }}
        />
        <Stack.Screen
          name="denunciation/success"
          options={{
            title: "Signalement envoyé",
            headerShown: true,
          }}
        />
      </Stack>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});