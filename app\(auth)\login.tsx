import React, { useState, useRef, useEffect, useCallback } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Platform, ActivityIndicator, Alert } from 'react-native';
import { Link, useLocalSearchParams } from 'expo-router';
import { supabase } from '../../lib/supabase';
import { useTheme } from '../../context/theme';
import { Ionicons } from '@expo/vector-icons';
import * as WebBrowser from 'expo-web-browser';
import * as Google from 'expo-auth-session/providers/google';
import { useAuth } from '../../context/auth';
import { BlurView } from 'expo-blur';

WebBrowser.maybeCompleteAuthSession();

export default function LoginScreen() {
  const { signIn } = useAuth();
  const { theme } = useTheme();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [authRequired, setAuthRequired] = useState(false);
  
  const passwordInputRef = useRef<TextInput>(null);
  const params = useLocalSearchParams();
  const isMounted = useRef(true);

  useEffect(() => {
    // Vérifier si l'utilisateur a été redirigé vers la page de connexion
    if (params.authRequired === 'true') {
      setAuthRequired(true);
      setError('Veuillez vous connecter pour accéder à cette page');
    }
    
    return () => {
      // Marquer le composant comme démonté
      isMounted.current = false;
    };
  }, [params]);

  useEffect(() => {
    const checkSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        console.log('Current session:', session ? 'Exists' : 'None');
      } catch (error) {
        console.error('Erreur lors de la vérification de session:', error);
      }
    };
    // Exécuter en arrière-plan pour ne pas bloquer l'interface utilisateur
    setTimeout(checkSession, 100);
  }, []);

  const validateForm = useCallback(() => {
    if (!email.trim() || !password.trim()) {
      setError('Veuillez remplir tous les champs');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Veuillez entrer une adresse email valide');
      return false;
    }

    if (password.length < 6) {
      setError('Le mot de passe doit contenir au moins 6 caractères');
      return false;
    }

    return true;
  }, [email, password]);

  const handleLogin = useCallback(async () => {
    if (isLoading) return; // Éviter les doubles soumissions
    
    try {
      setIsLoading(true);
      setError('');
      setSuccess('');

      if (!validateForm()) {
        setIsLoading(false);
        return;
      }

      // Réduire le délai de connexion pour une meilleure expérience utilisateur
      await new Promise(resolve => setTimeout(resolve, 300));
      console.log('Tentative de connexion...');

      // Tentative de connexion
      const { error } = await signIn(email.trim(), password);

      if (error) {
        console.log('Erreur de connexion:', error.message);
        if (isMounted.current) setError(error.message);
        return;
      }

      console.log('Connexion réussie, redirection...');
      if (isMounted.current) setSuccess('Connexion réussie !');
      // La redirection est gérée automatiquement par le contexte d'authentification
    } catch (err: any) {
      console.error('Erreur inattendue lors de la connexion:', err);
      if (isMounted.current) setError('Une erreur est survenue. Veuillez réessayer plus tard.');
    } finally {
      if (isMounted.current) setIsLoading(false);
    }
  }, [email, password, validateForm, signIn, isLoading]);

  const computedStyles = {
    container: [styles.container, { backgroundColor: theme.colors.background }],
    title: [styles.title, { color: theme.colors.primary }],
    subtitle: [styles.subtitle, { color: theme.colors.text }],
    authRequiredContainer: [styles.authRequiredContainer, { backgroundColor: theme.colors.error + '20' }],
    authRequiredText: [styles.authRequiredText, { color: theme.colors.error }],
    error: [styles.error, { color: theme.colors.error }],
    success: [styles.success, { color: theme.colors.success }],
    input: [styles.input, { 
      backgroundColor: theme.colors.inputBackground,
      borderColor: theme.colors.inputBorder,
      color: theme.colors.text
    }],
    button: [
      styles.button, 
      { backgroundColor: theme.colors.primary },
      isLoading && styles.buttonDisabled
    ],
    linkText: [styles.linkText, { color: theme.colors.primary }]
  };

  return (
    <View style={computedStyles.container}>
      <View style={styles.header}>
        <Text style={computedStyles.title}>Bomoko Mobile</Text>
        <Text style={computedStyles.subtitle}>Votre soutien en toute sécurité</Text>
      </View>

      <View style={styles.form}>
        {authRequired && (
          <View style={computedStyles.authRequiredContainer}>
            <Ionicons name="lock-closed" size={24} color={theme.colors.error} />
            <Text style={computedStyles.authRequiredText}>
              Authentification requise pour accéder à cette page
            </Text>
          </View>
        )}
        
        {error ? <Text style={computedStyles.error}>{error}</Text> : null}
        {success ? <Text style={computedStyles.success}>{success}</Text> : null}
        
        <TextInput
          style={computedStyles.input}
          placeholder="Email"
          placeholderTextColor={theme.colors.textSecondary}
          value={email}
          onChangeText={setEmail}
          autoCapitalize="none"
          keyboardType="email-address"
          editable={!isLoading}
          returnKeyType="next"
          onSubmitEditing={() => passwordInputRef.current?.focus()}
          blurOnSubmit={false}
        />

        <TextInput
          ref={passwordInputRef}
          style={computedStyles.input}
          placeholder="Mot de passe"
          placeholderTextColor={theme.colors.textSecondary}
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          editable={!isLoading}
          returnKeyType="go"
          onSubmitEditing={handleLogin}
        />

        <TouchableOpacity 
          style={computedStyles.button}
          onPress={handleLogin}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text style={styles.buttonText}>Se connecter</Text>
          )}
        </TouchableOpacity>

        <Link href="/register" asChild>
          <TouchableOpacity style={styles.linkButton} disabled={isLoading}>
            <Text style={computedStyles.linkText}>Créer un compte</Text>
          </TouchableOpacity>
        </Link>

        <Link href="/forgot-password" asChild>
          <TouchableOpacity style={styles.linkButton} disabled={isLoading}>
            <Text style={computedStyles.linkText}>Mot de passe oublié ?</Text>
          </TouchableOpacity>
        </Link>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    marginTop: Platform.OS === 'ios' ? 60 : 40,
    marginBottom: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  form: {
    width: '100%',
  },
  input: {
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    fontSize: 16,
    borderWidth: 1,
  },
  button: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 10,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  linkButton: {
    alignItems: 'center',
    padding: 15,
  },
  linkText: {
    fontSize: 16,
  },
  error: {
    padding: 10,
    borderRadius: 8,
    marginBottom: 15,
    textAlign: 'center',
  },
  success: {
    padding: 10,
    borderRadius: 8,
    marginBottom: 15,
    textAlign: 'center',
  },
  authRequiredContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
  },
  authRequiredText: {
    marginLeft: 10,
    flex: 1,
  },
});