import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Pressable } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/theme';
import * as Haptics from 'expo-haptics';

const ThemeSelector = () => {
  const { theme, themeMode, setThemeMode, toggleTheme } = useTheme();
  const [animatedValue] = React.useState(new Animated.Value(0));

  // Animer lors du changement de thème
  React.useEffect(() => {
    Animated.spring(animatedValue, {
      toValue: 1,
      friction: 5,
      tension: 40,
      useNativeDriver: true,
    }).start(() => {
      animatedValue.setValue(0);
    });
  }, [themeMode]);

  // Animation de rotation pour l'icône
  const rotate = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Animation de scale pour le conteneur
  const scale = animatedValue.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [1, 1.1, 1],
  });

  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    toggleTheme();
  };

  const selectSpecificTheme = (mode: 'light' | 'dark' | 'system') => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setThemeMode(mode);
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.surface }]}>
      <Text style={[styles.title, { color: theme.colors.text }]}>Apparence</Text>
      
      <Animated.View 
        style={[
          styles.currentThemeContainer, 
          { 
            backgroundColor: theme.colors.cardBackground,
            borderColor: theme.colors.cardBorder,
            transform: [{ scale }] 
          }
        ]}
      >
        <View style={styles.themeInfo}>
          <Animated.View style={{ transform: [{ rotate }] }}>
            <Ionicons 
              name={themeMode === 'light' ? 'sunny' : themeMode === 'dark' ? 'moon' : 'contrast'} 
              size={24} 
              color={theme.colors.primary} 
            />
          </Animated.View>
          <Text style={[styles.currentThemeText, { color: theme.colors.text }]}>
            {themeMode === 'light' ? 'Mode clair' : themeMode === 'dark' ? 'Mode sombre' : 'Mode système'}
          </Text>
        </View>
        <TouchableOpacity 
          onPress={handlePress}
          style={[styles.toggleButton, { backgroundColor: theme.colors.primary }]}
        >
          <Text style={styles.toggleButtonText}>Changer</Text>
        </TouchableOpacity>
      </Animated.View>

      <View style={styles.optionsContainer}>
        <Pressable 
          style={[
            styles.option, 
            { 
              backgroundColor: themeMode === 'light' ? theme.colors.primary : theme.colors.cardBackground,
              borderColor: theme.colors.cardBorder
            }
          ]}
          onPress={() => selectSpecificTheme('light')}
        >
          <Ionicons name="sunny" size={20} color={themeMode === 'light' ? theme.colors.white : theme.colors.text} />
          <Text 
            style={[
              styles.optionText, 
              { color: themeMode === 'light' ? theme.colors.white : theme.colors.text }
            ]}
          >
            Clair
          </Text>
        </Pressable>
        
        <Pressable 
          style={[
            styles.option, 
            { 
              backgroundColor: themeMode === 'dark' ? theme.colors.primary : theme.colors.cardBackground,
              borderColor: theme.colors.cardBorder 
            }
          ]}
          onPress={() => selectSpecificTheme('dark')}
        >
          <Ionicons name="moon" size={20} color={themeMode === 'dark' ? theme.colors.white : theme.colors.text} />
          <Text 
            style={[
              styles.optionText, 
              { color: themeMode === 'dark' ? theme.colors.white : theme.colors.text }
            ]}
          >
            Sombre
          </Text>
        </Pressable>
        
        <Pressable 
          style={[
            styles.option, 
            { 
              backgroundColor: themeMode === 'system' ? theme.colors.primary : theme.colors.cardBackground,
              borderColor: theme.colors.cardBorder 
            }
          ]}
          onPress={() => selectSpecificTheme('system')}
        >
          <Ionicons name="contrast" size={20} color={themeMode === 'system' ? theme.colors.white : theme.colors.text} />
          <Text 
            style={[
              styles.optionText, 
              { color: themeMode === 'system' ? theme.colors.white : theme.colors.text }
            ]}
          >
            Système
          </Text>
        </Pressable>
      </View>
      
      <View style={[styles.infoBox, { backgroundColor: theme.colors.cardBackground, borderColor: theme.colors.cardBorder }]}>
        <Ionicons name="information-circle-outline" size={20} color={theme.colors.primary} />
        <Text style={[styles.infoText, { color: theme.colors.textSecondary }]}>
          Le mode système s'adapte automatiquement au thème de votre appareil.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    margin: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  currentThemeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
  },
  themeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentThemeText: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
  toggleButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  toggleButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  option: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginHorizontal: 4,
    borderRadius: 8,
    borderWidth: 1,
  },
  optionText: {
    marginTop: 4,
    fontWeight: '500',
  },
  infoBox: {
    flexDirection: 'row',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  infoText: {
    marginLeft: 8,
    flex: 1,
    fontSize: 13,
  }
});

export default ThemeSelector; 