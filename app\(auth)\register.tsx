import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { Link, router } from 'expo-router';
import { useAuth } from '../../context/auth';
import { supabase } from '../../lib/supabase';
import { colors } from '../../constants/theme';
import { Ionicons } from '@expo/vector-icons';
import * as WebBrowser from 'expo-web-browser';
import * as Google from 'expo-auth-session/providers/google';

WebBrowser.maybeCompleteAuthSession();

export default function RegisterScreen() {
  const { signUp } = useAuth();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const [request, response, promptAsync] = Google.useAuthRequest({
    androidClientId: 'YOUR_ANDROID_CLIENT_ID',
    iosClientId: 'YOUR_IOS_CLIENT_ID',
    webClientId: 'YOUR_WEB_CLIENT_ID',
  });

  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      setError('');
      const result = await promptAsync();
      if (result?.type === 'success') {
        const token = result.authentication?.accessToken;
        
        if (!token) {
          throw new Error('Token d\'authentification manquant');
        }
        
        // Utiliser supabase depuis le contexte importé
        const { error } = await supabase.auth.signInWithIdToken({
          provider: 'google',
          token: token,
        });

        if (error) throw error;

        setSuccess('Inscription réussie ! Redirection vers la configuration du profil...');
        setTimeout(() => {
          router.replace('/(app)/onboarding/profile-setup');
        }, 1500);
      }
    } catch (err) {
      setError('Échec de la connexion avec Google. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  const validateForm = () => {
    if (!firstName.trim() || !lastName.trim() || !email.trim() || !password || !confirmPassword) {
      setError('Veuillez remplir tous les champs.');
      return false;
    }

    if (password !== confirmPassword) {
      setError('Les mots de passe ne correspondent pas.');
      return false;
    }

    if (password.length < 6) {
      setError('Le mot de passe doit contenir au moins 6 caractères.');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Veuillez entrer une adresse email valide.');
      return false;
    }

    return true;
  };

  const handleRegister = async () => {
    try {
      setIsLoading(true);
      setError('');
      setSuccess('');

      if (!validateForm()) {
        setIsLoading(false);
        return;
      }

      // Ajouter un court délai avant l'inscription
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Tentative d\'inscription...');

      // Une seule tentative d'inscription simple
      const result = await signUp(email.trim(), password);
      const { data, error } = result;
      
      if (error) {
        console.log('Erreur d\'inscription:', error.message);
        
        // Si l'erreur indique que l'utilisateur existe déjà, proposer de se connecter
        if (error.message.includes('déjà utilisé') || 
            error.message.includes('already registered') || 
            error.message.includes('already exists')) {
          setError(error.message);
          console.log('Redirection vers la page de connexion dans 3 secondes...');
          setTimeout(() => {
            router.push('/login');
          }, 3000);
          return;
        }
        
        setError(error.message);
        return;
      }

      // Vérifier si l'email a besoin d'être confirmé
      if (data?.user && data.user.identities && data.user.identities.length === 0) {
        setError('Cet email est déjà utilisé. Veuillez vous connecter ou utiliser un autre email.');
        return;
      }

      // Vérifier si l'email de confirmation est nécessaire
      if (data?.user && !data.session) {
        setSuccess('Inscription réussie ! Veuillez vérifier votre email pour confirmer votre compte.');
        
        // Rediriger vers la page de connexion après un délai
        setTimeout(() => {
          router.push('/login');
        }, 3000);
      } else if (data?.user) {
        // Si pas besoin de confirmation email, rediriger vers la configuration du profil
        setSuccess('Inscription réussie ! Redirection vers la configuration du profil...');
        setTimeout(() => {
          router.replace('/(app)/onboarding/profile-setup');
        }, 1500);
      } else {
        // Cas où l'inscription a réussi mais sans données utilisateur claires
        setSuccess('Inscription traitée. Veuillez vérifier votre email pour les instructions suivantes.');
        setTimeout(() => {
          router.push('/login');
        }, 3000);
      }
    } catch (err: any) {
      console.error('Erreur inattendue lors de l\'inscription:', err);
      setError('Une erreur inattendue est survenue. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Créer un compte</Text>
        <Text style={styles.subtitle}>
          Rejoignez Bomoko Mobile pour accéder à nos services
        </Text>
      </View>

      <View style={styles.form}>
        {error ? (
          <View style={styles.errorContainer}>
            <Text style={styles.error}>{error}</Text>
            {error.includes('déjà utilisé') && (
              <TouchableOpacity 
                style={styles.loginRedirectButton}
                onPress={() => router.push('/login')}
              >
                <Text style={styles.loginRedirectText}>Se connecter maintenant</Text>
              </TouchableOpacity>
            )}
          </View>
        ) : null}
        {success ? <Text style={styles.success}>{success}</Text> : null}

        <TextInput
          style={styles.input}
          placeholder="Prénom"
          value={firstName}
          onChangeText={setFirstName}
          autoCapitalize="words"
          editable={!isLoading}
        />

        <TextInput
          style={styles.input}
          placeholder="Nom"
          value={lastName}
          onChangeText={setLastName}
          autoCapitalize="words"
          editable={!isLoading}
        />

        <TextInput
          style={styles.input}
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          autoCapitalize="none"
          keyboardType="email-address"
          editable={!isLoading}
        />

        <TextInput
          style={styles.input}
          placeholder="Mot de passe"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          editable={!isLoading}
        />

        <TextInput
          style={styles.input}
          placeholder="Confirmer le mot de passe"
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          secureTextEntry
          editable={!isLoading}
        />

        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={handleRegister}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Inscription...' : 'S\'inscrire'}
          </Text>
        </TouchableOpacity>

        <View style={styles.divider}>
          <View style={styles.dividerLine} />
          <Text style={styles.dividerText}>ou</Text>
          <View style={styles.dividerLine} />
        </View>

        <TouchableOpacity 
          style={[styles.googleButton, (isLoading || !request) && styles.buttonDisabled]} 
          onPress={handleGoogleSignIn}
          disabled={isLoading || !request}
        >
          <Ionicons name="logo-google" size={24} color={colors.text} />
          <Text style={styles.googleButtonText}>Continuer avec Google</Text>
        </TouchableOpacity>

        <Link href="/login">
          <TouchableOpacity style={styles.linkButton} disabled={isLoading}>
            <Text style={styles.linkText}>Déjà un compte ? Se connecter</Text>
          </TouchableOpacity>
        </Link>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    padding: 20,
  },
  header: {
    marginTop: Platform.OS === 'ios' ? 60 : 40,
    marginBottom: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: colors.text,
    textAlign: 'center',
  },
  form: {
    width: '100%',
  },
  input: {
    backgroundColor: colors.surface,
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    fontSize: 16,
  },
  button: {
    backgroundColor: colors.primary,
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 15,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: colors.gray[300],
  },
  dividerText: {
    color: colors.gray[600],
    paddingHorizontal: 10,
  },
  googleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.surface,
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  googleButtonText: {
    color: colors.text,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 10,
  },
  linkButton: {
    padding: 10,
    alignItems: 'center',
  },
  linkText: {
    color: colors.primary,
    fontSize: 16,
  },
  error: {
    color: colors.error,
    marginBottom: 10,
    textAlign: 'center',
  },
  errorContainer: {
    backgroundColor: colors.error + '15', // Légère transparence
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: colors.error + '30',
  },
  loginRedirectButton: {
    marginTop: 10,
    padding: 10,
    backgroundColor: colors.white,
    borderRadius: 8,
    alignItems: 'center',
  },
  loginRedirectText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  success: {
    color: colors.primary,
    marginBottom: 15,
    textAlign: 'center',
    backgroundColor: colors.primary + '15', // Légère transparence
    padding: 15,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.primary + '30',
  },
});