module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      'react-native-reanimated/plugin',
      // Custom plugin to transform ws imports
      [
        'module-resolver',
        {
          alias: {
            'ws': './polyfills.js',
            'ws/lib/stream': './polyfills.js',
            'ws/lib/websocket': './polyfills.js',
            'ws/lib/buffer-util': './polyfills.js',
            'ws/lib/validation': './polyfills.js',
            'ws/lib/extension': './polyfills.js',
            'ws/lib/permessage-deflate': './polyfills.js',
            'ws/lib/sender': './polyfills.js',
            'ws/lib/receiver': './polyfills.js',
            'ws/lib/event-target': './polyfills.js',
            'ws/lib/constants': './polyfills.js',
            'ws/lib/subprotocol': './polyfills.js',
            'ws/lib/websocket-server': './polyfills.js',
            'ws/lib/limiter': './polyfills.js',
          },
        },
      ],
    ],
  };
};
