module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      'react-native-reanimated/plugin',
      // Custom plugin to transform ws imports
      [
        'module-resolver',
        {
          alias: {
            'ws': './ws-polyfill/index.js',
            'ws/lib/stream': './ws-polyfill/lib/stream.js',
            'ws/lib/websocket': './ws-polyfill/lib/websocket.js',
            'ws/lib/buffer-util': './ws-polyfill/index.js',
            'ws/lib/validation': './ws-polyfill/index.js',
            'ws/lib/extension': './ws-polyfill/index.js',
            'ws/lib/permessage-deflate': './ws-polyfill/index.js',
            'ws/lib/sender': './ws-polyfill/index.js',
            'ws/lib/receiver': './ws-polyfill/index.js',
            'ws/lib/event-target': './ws-polyfill/index.js',
            'ws/lib/constants': './ws-polyfill/index.js',
            'ws/lib/subprotocol': './ws-polyfill/index.js',
            'ws/lib/websocket-server': './ws-polyfill/index.js',
            'ws/lib/limiter': './ws-polyfill/index.js',
          },
        },
      ],
    ],
  };
};
