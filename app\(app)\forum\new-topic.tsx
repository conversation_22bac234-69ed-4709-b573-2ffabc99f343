import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Platform,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { supabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';

export default function NewTopicScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const params = useLocalSearchParams();
  const categoryId = params.categoryId as string;
  const categoryName = params.categoryName as string;

  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async () => {
    if (!title.trim() || !content.trim()) {
      setError('Veuillez remplir tous les champs');
      return;
    }

    try {
      setSending(true);
      setError(null);

      const { data: topic, error: topicError } = await supabase.rpc(
        'create_forum_topic',
        {
          p_category_id: categoryId,
          p_title: title.trim(),
          p_content: content.trim(),
          p_is_anonymous: isAnonymous
        }
      );

      if (topicError) throw topicError;

      router.replace({
        pathname: '/forum/topic',
        params: { id: topic },
      });
    } catch (err) {
      console.error('Error creating topic:', err);
      setError('Erreur lors de la création de la discussion');
    } finally {
      setSending(false);
    }
  };

  const handleCancel = () => {
    if (title.trim() || content.trim()) {
      Alert.alert(
        'Abandonner',
        'Voulez-vous vraiment abandonner cette discussion ?',
        [
          {
            text: 'Continuer',
            style: 'cancel',
          },
          {
            text: 'Abandonner',
            style: 'destructive',
            onPress: () => router.back(),
          },
        ]
      );
    } else {
      router.back();
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleCancel}>
          <Ionicons
            name="close"
            size={24}
            color={theme.colors.text}
          />
        </TouchableOpacity>
        <View style={styles.headerContent}>
          <Text style={[styles.categoryName, { color: theme.colors.gray[600] }]}>
            {categoryName}
          </Text>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Nouvelle discussion
          </Text>
        </View>
      </View>

      <ScrollView style={styles.content}>
        {error && (
          <Text style={[styles.error, { color: theme.colors.error }]}>
            {error}
          </Text>
        )}

        <View style={styles.form}>
          <TextInput
            style={[
              styles.titleInput,
              {
                backgroundColor: theme.colors.surface,
                color: theme.colors.text,
              },
            ]}
            placeholder="Titre de la discussion"
            placeholderTextColor={theme.colors.gray[400]}
            value={title}
            onChangeText={setTitle}
            maxLength={100}
            editable={!sending}
          />

          <TextInput
            style={[
              styles.contentInput,
              {
                backgroundColor: theme.colors.surface,
                color: theme.colors.text,
              },
            ]}
            placeholder="Contenu de votre message..."
            placeholderTextColor={theme.colors.gray[400]}
            value={content}
            onChangeText={setContent}
            multiline
            textAlignVertical="top"
            maxLength={2000}
            editable={!sending}
          />

          <TouchableOpacity
            style={[
              styles.anonymousToggle,
              {
                backgroundColor: isAnonymous
                  ? theme.colors.primary + '20'
                  : theme.colors.surface,
              },
            ]}
            onPress={() => setIsAnonymous(!isAnonymous)}>
            <Ionicons
              name={isAnonymous ? 'eye-off' : 'eye-outline'}
              size={24}
              color={isAnonymous ? theme.colors.primary : theme.colors.gray[600]}
            />
            <Text
              style={[
                styles.anonymousText,
                {
                  color: isAnonymous
                    ? theme.colors.primary
                    : theme.colors.gray[600],
                },
              ]}>
              Poster anonymement
            </Text>
          </TouchableOpacity>

          <View style={styles.guidelines}>
            <Text style={[styles.guidelinesTitle, { color: theme.colors.text }]}>
              Conseils pour votre message
            </Text>
            {postingGuidelines.map((guideline, index) => (
              <View key={index} style={styles.guidelineItem}>
                <Ionicons
                  name={guideline.icon}
                  size={20}
                  color={theme.colors.primary}
                  style={styles.guidelineIcon}
                />
                <Text
                  style={[
                    styles.guidelineText,
                    { color: theme.colors.gray[600] },
                  ]}>
                  {guideline.text}
                </Text>
              </View>
            ))}
          </View>
        </View>
      </ScrollView>

      <View
        style={[styles.footer, { backgroundColor: theme.colors.surface }]}>
        <TouchableOpacity
          style={[
            styles.submitButton,
            {
              backgroundColor: theme.colors.primary,
              opacity: sending ? 0.5 : 1,
            },
          ]}
          onPress={handleSubmit}
          disabled={sending}>
          <Text style={styles.submitButtonText}>
            {sending ? 'Publication...' : 'Publier la discussion'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const postingGuidelines = [
  {
    icon: 'heart-outline',
    text: 'Soyez bienveillant et respectueux envers les autres',
  },
  {
    icon: 'shield-checkmark-outline',
    text: 'Protégez votre vie privée, ne partagez pas d\'informations personnelles',
  },
  {
    icon: 'chatbubbles-outline',
    text: 'Exprimez-vous clairement et évitez les propos inappropriés',
  },
  {
    icon: 'alert-circle-outline',
    text: 'Votre message sera modéré avant publication',
  },
];

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
  },
  backButton: {
    marginRight: 15,
  },
  headerContent: {
    flex: 1,
  },
  categoryName: {
    fontSize: 14,
    marginBottom: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  error: {
    marginBottom: 15,
    textAlign: 'center',
  },
  form: {
    gap: 15,
  },
  titleInput: {
    padding: 15,
    borderRadius: 12,
    fontSize: 16,
    fontWeight: '600',
  },
  contentInput: {
    padding: 15,
    borderRadius: 12,
    fontSize: 16,
    minHeight: 200,
  },
  anonymousToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 12,
    gap: 10,
  },
  anonymousText: {
    fontSize: 16,
    fontWeight: '600',
  },
  guidelines: {
    padding: 20,
    backgroundColor: '#f9fafb',
    borderRadius: 12,
  },
  guidelinesTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  guidelineItem: {
    flexDirection: 'row',
    marginBottom: 10,
    gap: 10,
  },
  guidelineIcon: {
    marginTop: 2,
  },
  guidelineText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
  },
  footer: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  submitButton: {
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});