import { Stack } from 'expo-router';
import { Platform } from 'react-native';

export default function ForumLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: true,
        headerStyle: {
          backgroundColor: '#fff',
        },
        headerTintColor: '#000',
        headerTitleStyle: {
          fontWeight: '600',
        },
        animation: Platform.OS === 'ios' ? 'default' : 'slide_from_right',
        presentation: 'card',
      }}
    >
      <Stack.Screen 
        name="index" 
        options={{
          title: "Forum",
          headerLargeTitle: Platform.OS === 'ios',
        }}
      />
      <Stack.Screen 
        name="category" 
        options={{
          title: "Catégorie",
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen 
        name="topic" 
        options={{
          title: "Discussion",
          animation: 'slide_from_right',
        }}
      />
      <Stack.Screen 
        name="new-topic" 
        options={{
          title: "Nouveau sujet",
          presentation: 'modal',
          animation: 'slide_from_bottom',
        }}
      />
    </Stack>
  );
}