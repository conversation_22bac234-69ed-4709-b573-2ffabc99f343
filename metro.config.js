// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('@expo/metro-config');

const config = getDefaultConfig(__dirname);

// Resolve JSON parsing issues
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    ...config.transformer.minifierConfig,
    keep_classnames: true,
    keep_fnames: true,
    mangle: {
      ...config.transformer.minifierConfig?.mangle,
      keep_classnames: true,
      keep_fnames: true
    },
    output: {
      ...config.transformer.minifierConfig?.output,
      ascii_only: true
    }
  }
};

// Add support for additional file extensions and platform-specific extensions
config.resolver.sourceExts = [
  ...config.resolver.sourceExts,
  'mjs',
  'cjs',
  'web.js',
  'web.ts',
  'web.tsx'
];

// Ensure platform-specific extensions are properly handled
config.resolver.platforms = ['web', 'ios', 'android'];

// Ajout de la configuration pour SDK 53
config.resolver.assetExts = [...config.resolver.assetExts, 'lottie'];

// Add Node.js polyfills for React Native
config.resolver.alias = {
  ...config.resolver.alias,
  stream: 'stream-browserify',
  crypto: 'react-native-crypto',
  buffer: '@craftzdog/react-native-buffer',
  util: 'util',
  url: 'react-native-url-polyfill',
  querystring: 'querystring-es3',
  path: 'path-browserify',
  fs: false,
  net: false,
  tls: false,
  ws: false,
};

module.exports = config;

