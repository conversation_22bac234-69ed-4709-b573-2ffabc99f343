// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('@expo/metro-config');

const config = getDefaultConfig(__dirname);

// Resolve JSON parsing issues
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    ...config.transformer.minifierConfig,
    keep_classnames: true,
    keep_fnames: true,
    mangle: {
      ...config.transformer.minifierConfig?.mangle,
      keep_classnames: true,
      keep_fnames: true
    },
    output: {
      ...config.transformer.minifierConfig?.output,
      ascii_only: true
    }
  }
};

// Add support for additional file extensions and platform-specific extensions
config.resolver.sourceExts = [
  ...config.resolver.sourceExts,
  'mjs',
  'cjs',
  'web.js',
  'web.ts',
  'web.tsx'
];

// Ensure platform-specific extensions are properly handled
config.resolver.platforms = ['web', 'ios', 'android'];

// Ajout de la configuration pour SDK 53
config.resolver.assetExts = [...config.resolver.assetExts, 'lottie'];

// Add Node.js polyfills for React Native
config.resolver.alias = {
  ...config.resolver.alias,
  stream: 'stream-browserify',
  crypto: 'react-native-crypto',
  buffer: '@craftzdog/react-native-buffer',
  util: 'util',
  url: 'react-native-url-polyfill',
  querystring: 'querystring-es3',
  path: 'path-browserify',
  fs: false,
  net: false,
  tls: false,
  ws: false,
  // Additional WebSocket and Node.js module polyfills
  'ws/lib/stream': false,
  'ws/lib/websocket': false,
  'ws/lib/buffer-util': false,
  'ws/lib/validation': false,
  'ws/lib/extension': false,
  'ws/lib/permessage-deflate': false,
  'ws/lib/sender': false,
  'ws/lib/receiver': false,
  'ws/lib/event-target': false,
  'ws/lib/constants': false,
  'ws/lib/subprotocol': false,
  'ws/lib/websocket-server': false,
  'ws/lib/limiter': false,
  http: false,
  https: false,
  zlib: false,
  os: false,
  child_process: false,
  cluster: false,
  dgram: false,
  dns: false,
  domain: false,
  events: 'events',
  readline: false,
  repl: false,
  string_decoder: false,
  sys: false,
  timers: false,
  tty: false,
  vm: false,
  worker_threads: false,
};

// Block problematic modules from being resolved
config.resolver.blockList = [
  /node_modules\/ws\/lib\/stream\.js$/,
  /node_modules\/ws\/lib\/websocket\.js$/,
  /node_modules\/ws\/lib\/.*\.js$/,
];

// Custom resolver to handle ws module imports
config.resolver.resolveRequest = (context, moduleName, platform) => {
  // Block ws module and its submodules
  if (moduleName === 'ws' || moduleName.startsWith('ws/')) {
    return {
      filePath: require.resolve('./polyfills.js'),
      type: 'sourceFile',
    };
  }

  // Block stream module imports from ws
  if (moduleName === 'stream' && context.originModulePath && context.originModulePath.includes('node_modules/ws/')) {
    return {
      filePath: require.resolve('./polyfills.js'),
      type: 'sourceFile',
    };
  }

  // Use default resolver for other modules
  return context.resolveRequest(context, moduleName, platform);
};

module.exports = config;

