import 'react-native-url-polyfill/auto'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { createClient } from '@supabase/supabase-js'
import * as SecureStore from 'expo-secure-store'
import { Platform } from 'react-native'

// URL et clé API Supabase
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || ""
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || ""

// Création d'un gestionnaire de stockage compatible avec les plateformes web et mobiles
const createStorage = () => {
  return {
    getItem: async (key: string) => {
      if (Platform.OS === 'web') {
        const item = localStorage.getItem(key)
        return item
      } else {
        try {
          return await SecureStore.getItemAsync(key)
        } catch (error) {
          console.error('Erreur lors de la récupération des données sécurisées:', error)
          return null
        }
      }
    },
    setItem: async (key: string, value: string) => {
      if (Platform.OS === 'web') {
        localStorage.setItem(key, value)
      } else {
        try {
          await SecureStore.setItemAsync(key, value)
        } catch (error) {
          console.error('Erreur lors de l\'enregistrement des données sécurisées:', error)
        }
      }
    },
    removeItem: async (key: string) => {
      if (Platform.OS === 'web') {
        localStorage.removeItem(key)
      } else {
        try {
          await SecureStore.deleteItemAsync(key)
        } catch (error) {
          console.error('Erreur lors de la suppression des données sécurisées:', error)
        }
      }
    }
  }
}

// Configuration du client Supabase avec les paramètres optimisés
export const supabase = createClient(
  supabaseUrl,
  supabaseAnonKey,
  {
    auth: {
      storage: createStorage(),
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
      // Pour le mobile, désactiver certains hooks qui pourraient causer des problèmes
      flowType: 'pkce',
    },
    global: {
      // Désactiver les fetch globaux pour éviter les conflits avec l'API Expo
      fetch: undefined,
    },
    db: {
      schema: 'public',
    },
    // Désactiver complètement les fonctionnalités real-time pour éviter les problèmes de WebSocket
    realtime: {
      params: {
        eventsPerSecond: 0
      },
      // Désactiver complètement le transport WebSocket
      transport: undefined,
      timeout: 0,
      heartbeatIntervalMs: 0,
      reconnectAfterMs: () => null,
      encode: () => '',
      decode: () => ({}),
      // Forcer la désactivation des WebSockets
      websocketConstructor: undefined
    }
  }
)

// Ajouter des écouteurs d'événements pour le débogage
supabase.auth.onAuthStateChange((event, session) => {
  console.log('Événement d\'authentification:', event)
  if (event === 'SIGNED_IN') {
    console.log('Utilisateur connecté avec ID:', session?.user?.id)
  } else if (event === 'SIGNED_OUT') {
    console.log('Utilisateur déconnecté')
  } else if (event === 'USER_UPDATED') {
    console.log('Utilisateur mis à jour')
  } else if (event === 'TOKEN_REFRESHED') {
    console.log('Token rafraîchi')
  }
})
        