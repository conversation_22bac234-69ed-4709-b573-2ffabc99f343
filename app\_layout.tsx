import '../polyfills'; // Import polyfills first to handle Node.js modules
import React, { useEffect } from 'react';
import { Slot } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { ThemeProvider } from '../context/theme';
import { AuthProvider } from '../context/auth';
import { LocationProvider } from '../context/location';
import { setupGlobalErrorHandler } from '../utils/error-handler';
import '../lib/activity-tracking'; // Initialiser le suivi des activités

export default function RootLayout() {
  // Initialize global error handler
  useEffect(() => {
    setupGlobalErrorHandler();
    console.log('Global error handler initialized');
  }, []);

  return (
    <ThemeProvider>
      <AuthProvider>
        <LocationProvider>
          <StatusBar style="auto" />
          <Slot />
        </LocationProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
