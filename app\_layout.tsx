import React from 'react';
import { Slot } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { ThemeProvider } from '../context/theme';
import { AuthProvider } from '../context/auth';
import { LocationProvider } from '../context/location';
import '../lib/activity-tracking'; // Initialiser le suivi des activités

export default function RootLayout() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <LocationProvider>
          <StatusBar style="auto" />
          <Slot />
        </LocationProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
