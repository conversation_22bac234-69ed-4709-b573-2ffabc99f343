// Network utilities for checking connectivity and handling network-related issues
import { Platform } from 'react-native';

export interface NetworkStatus {
  isConnected: boolean;
  connectionType: 'wifi' | 'cellular' | 'ethernet' | 'unknown' | 'none';
  isInternetReachable: boolean;
  timestamp: Date;
}

export class NetworkUtils {
  private static instance: NetworkUtils;
  private lastKnownStatus: NetworkStatus | null = null;

  static getInstance(): NetworkUtils {
    if (!NetworkUtils.instance) {
      NetworkUtils.instance = new NetworkUtils();
    }
    return NetworkUtils.instance;
  }

  /**
   * Check basic network connectivity
   */
  async checkConnectivity(): Promise<NetworkStatus> {
    try {
      // Simple fetch test to check internet connectivity
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('https://www.google.com/generate_204', {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);

      const status: NetworkStatus = {
        isConnected: response.ok,
        connectionType: 'unknown', // We can't easily detect this in React Native without additional packages
        isInternetReachable: response.ok,
        timestamp: new Date()
      };

      this.lastKnownStatus = status;
      return status;

    } catch (error) {
      console.warn('Network connectivity check failed:', error);
      
      const status: NetworkStatus = {
        isConnected: false,
        connectionType: 'none',
        isInternetReachable: false,
        timestamp: new Date()
      };

      this.lastKnownStatus = status;
      return status;
    }
  }

  /**
   * Get last known network status
   */
  getLastKnownStatus(): NetworkStatus | null {
    return this.lastKnownStatus;
  }

  /**
   * Check if we can reach Expo's servers
   */
  async checkExpoConnectivity(): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      // Try to reach Expo's API
      const response = await fetch('https://exp.host/--/api/v2/versions', {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);
      return response.ok;

    } catch (error) {
      console.warn('Expo connectivity check failed:', error);
      return false;
    }
  }

  /**
   * Check if we can reach Supabase
   */
  async checkSupabaseConnectivity(supabaseUrl: string): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache'
      });

      clearTimeout(timeoutId);
      return response.status < 500; // Accept any non-server error

    } catch (error) {
      console.warn('Supabase connectivity check failed:', error);
      return false;
    }
  }

  /**
   * Perform comprehensive connectivity check
   */
  async performFullConnectivityCheck(supabaseUrl?: string): Promise<{
    general: NetworkStatus;
    expo: boolean;
    supabase: boolean;
  }> {
    console.log('Performing full connectivity check...');

    const [general, expo, supabase] = await Promise.allSettled([
      this.checkConnectivity(),
      this.checkExpoConnectivity(),
      supabaseUrl ? this.checkSupabaseConnectivity(supabaseUrl) : Promise.resolve(false)
    ]);

    const result = {
      general: general.status === 'fulfilled' ? general.value : {
        isConnected: false,
        connectionType: 'none' as const,
        isInternetReachable: false,
        timestamp: new Date()
      },
      expo: expo.status === 'fulfilled' ? expo.value : false,
      supabase: supabase.status === 'fulfilled' ? supabase.value : false
    };

    console.log('Connectivity check results:', {
      general: result.general.isConnected,
      expo: result.expo,
      supabase: result.supabase
    });

    return result;
  }

  /**
   * Test webhook connectivity
   */
  async testWebhookConnectivity(webhookUrl: string): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000);

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          test: true,
          timestamp: new Date().toISOString(),
          source: 'connectivity_check'
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      
      // Accept any response that's not a network error
      return response.status < 500;

    } catch (error) {
      console.warn('Webhook connectivity test failed:', error);
      return false;
    }
  }

  /**
   * Get network diagnostics
   */
  async getDiagnostics(supabaseUrl?: string): Promise<{
    platform: string;
    timestamp: Date;
    connectivity: {
      general: NetworkStatus;
      expo: boolean;
      supabase: boolean;
    };
    userAgent?: string;
  }> {
    const connectivity = await this.performFullConnectivityCheck(supabaseUrl);

    return {
      platform: Platform.OS,
      timestamp: new Date(),
      connectivity,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined
    };
  }

  /**
   * Log network diagnostics
   */
  async logDiagnostics(supabaseUrl?: string): Promise<void> {
    const diagnostics = await this.getDiagnostics(supabaseUrl);
    
    console.log('=== Network Diagnostics ===');
    console.log('Platform:', diagnostics.platform);
    console.log('Timestamp:', diagnostics.timestamp.toISOString());
    console.log('General connectivity:', diagnostics.connectivity.general);
    console.log('Expo reachable:', diagnostics.connectivity.expo);
    console.log('Supabase reachable:', diagnostics.connectivity.supabase);
    if (diagnostics.userAgent) {
      console.log('User Agent:', diagnostics.userAgent);
    }
    console.log('=========================');
  }
}

// Export singleton instance
export const networkUtils = NetworkUtils.getInstance();

// Helper function to check if error is network-related
export const isNetworkError = (error: Error | string): boolean => {
  const message = typeof error === 'string' ? error : error.message;
  const networkKeywords = [
    'network',
    'connection',
    'timeout',
    'fetch',
    'failed to download',
    'remote update',
    'internet',
    'offline',
    'unreachable'
  ];

  return networkKeywords.some(keyword => 
    message.toLowerCase().includes(keyword)
  );
};

// Helper function to check if error is update-related
export const isUpdateError = (error: Error | string): boolean => {
  const message = typeof error === 'string' ? error : error.message;
  const updateKeywords = [
    'update',
    'expo updates',
    'remote update',
    'download',
    'manifest',
    'bundle'
  ];

  return updateKeywords.some(keyword => 
    message.toLowerCase().includes(keyword)
  );
};
