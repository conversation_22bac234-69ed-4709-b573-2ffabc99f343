// WebSocket polyfill for React Native
// This replaces the ws library to prevent Node.js module imports

console.warn('WebSocket (ws) library has been polyfilled for React Native compatibility');

// Mock WebSocket class
class MockWebSocket {
  constructor(url, protocols, options) {
    console.warn('WebSocket constructor called but WebSocket is not available in React Native');
    this.url = url;
    this.protocols = protocols;
    this.readyState = MockWebSocket.CLOSED;
    this.bufferedAmount = 0;
    this.extensions = '';
    this.protocol = '';
    this.binaryType = 'blob';
    
    // Event handlers
    this.onopen = null;
    this.onclose = null;
    this.onmessage = null;
    this.onerror = null;
    
    // Immediately close the connection
    setTimeout(() => {
      if (this.onclose) {
        this.onclose({ code: 1000, reason: 'WebSocket not supported in React Native' });
      }
    }, 0);
  }
  
  send(data) {
    console.warn('WebSocket.send() called but WebSocket is not available in React Native');
  }
  
  close(code, reason) {
    console.warn('WebSocket.close() called but WebSocket is not available in React Native');
    this.readyState = MockWebSocket.CLOSED;
  }
  
  addEventListener(type, listener) {
    console.warn('WebSocket.addEventListener() called but WebSocket is not available in React Native');
  }
  
  removeEventListener(type, listener) {
    console.warn('WebSocket.removeEventListener() called but WebSocket is not available in React Native');
  }
  
  dispatchEvent(event) {
    console.warn('WebSocket.dispatchEvent() called but WebSocket is not available in React Native');
    return false;
  }
}

// WebSocket constants
MockWebSocket.CONNECTING = 0;
MockWebSocket.OPEN = 1;
MockWebSocket.CLOSING = 2;
MockWebSocket.CLOSED = 3;

// Mock WebSocketServer class
class MockWebSocketServer {
  constructor(options, callback) {
    console.warn('WebSocketServer constructor called but WebSocket is not available in React Native');
    if (callback) callback();
  }
  
  close(callback) {
    console.warn('WebSocketServer.close() called but WebSocket is not available in React Native');
    if (callback) callback();
  }
  
  handleUpgrade() {
    console.warn('WebSocketServer.handleUpgrade() called but WebSocket is not available in React Native');
  }
  
  shouldHandle() {
    console.warn('WebSocketServer.shouldHandle() called but WebSocket is not available in React Native');
    return false;
  }
}

// Mock createWebSocketStream function
function createWebSocketStream(ws, options) {
  console.warn('createWebSocketStream() called but WebSocket is not available in React Native');
  return null;
}

// Export the mock implementations
module.exports = MockWebSocket;
module.exports.default = MockWebSocket;
module.exports.WebSocket = MockWebSocket;
module.exports.WebSocketServer = MockWebSocketServer;
module.exports.createWebSocketStream = createWebSocketStream;
module.exports.CONNECTING = MockWebSocket.CONNECTING;
module.exports.OPEN = MockWebSocket.OPEN;
module.exports.CLOSING = MockWebSocket.CLOSING;
module.exports.CLOSED = MockWebSocket.CLOSED;

// Also support ES6 exports
if (typeof exports !== 'undefined') {
  exports.default = MockWebSocket;
  exports.WebSocket = MockWebSocket;
  exports.WebSocketServer = MockWebSocketServer;
  exports.createWebSocketStream = createWebSocketStream;
  exports.CONNECTING = MockWebSocket.CONNECTING;
  exports.OPEN = MockWebSocket.OPEN;
  exports.CLOSING = MockWebSocket.CLOSING;
  exports.CLOSED = MockWebSocket.CLOSED;
}
