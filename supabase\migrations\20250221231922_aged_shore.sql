/*
  # Fix webhook status check function and configuration

  1. Changes
    - Drop existing function before recreating it
    - Update webhook configuration
    - Fix return type mismatch in check_webhook_status function
  
  2. Security
    - Maintains existing RLS policies
    - Uses security definer for functions
*/

-- Update webhook URLs with correct configuration
UPDATE webhook_urls
SET 
    url = 'https://n8n-dw1u.onrender.com/webhook/chat',
    backup_url = 'https://n8n-dw1u.onrender.com/webhook/chat',
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    consecutive_failures = 0,
    last_successful_response = CURRENT_TIMESTAMP,
    health_check_enabled = true,
    max_retries = 3,
    retry_delay_base = 2000,
    health_check_interval = 300
WHERE name = 'n8n_chatbot_agent';

-- Drop the existing function first
DROP FUNCTION IF EXISTS check_webhook_status(text);

-- Create the function with the new return type
CREATE FUNCTION check_webhook_status(webhook_name TEXT)
RETURNS TABLE (
    is_available boolean,
    current_url text,
    error_details text
) AS $$
DECLARE
    webhook_record webhook_urls%ROWTYPE;
BEGIN
    -- Get webhook record
    SELECT * INTO webhook_record
    FROM webhook_urls
    WHERE name = webhook_name;

    -- Check if webhook exists
    IF webhook_record IS NULL THEN
        RETURN QUERY SELECT false::boolean, NULL::text, 'Webhook not found'::text;
        RETURN;
    END IF;

    -- Return status based on webhook health
    RETURN QUERY
    SELECT 
        (webhook_record.is_active AND webhook_record.error_count < webhook_record.max_retries)::boolean,
        CASE 
            WHEN webhook_record.error_count >= webhook_record.max_retries AND webhook_record.backup_url IS NOT NULL 
            THEN webhook_record.backup_url::text
            ELSE webhook_record.url::text
        END,
        webhook_record.error_message::text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;