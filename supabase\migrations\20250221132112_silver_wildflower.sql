/*
  # Improve webhook error handling and tracking

  1. Changes
    - Add error tracking columns to webhook_urls table
    - Add last_check_timestamp to track webhook availability
    - Add error_count for tracking consecutive failures
    - Add max_retries column for configurable retry limits
    - Add retry_delay_base for exponential backoff configuration

  2. Security
    - Maintain existing RLS policies
    - Add new policies for error tracking
*/

-- Add new columns to webhook_urls table
ALTER TABLE webhook_urls
ADD COLUMN IF NOT EXISTS last_check_timestamp TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS last_error_timestamp TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS error_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS max_retries INTEGER DEFAULT 3,
ADD COLUMN IF NOT EXISTS retry_delay_base INTEGER DEFAULT 1000,
ADD COLUMN IF NOT EXISTS error_message TEXT;

-- Create function to update webhook status
CREATE OR REPLACE FUNCTION update_webhook_status(
  webhook_id INTEGER,
  is_success BOOLEAN,
  error_msg TEXT DEFAULT NULL
)
R<PERSON>URNS VOID AS $$
BEGIN
  IF is_success THEN
    UPDATE webhook_urls
    SET 
      last_check_timestamp = CURRENT_TIMESTAMP,
      error_count = 0,
      error_message = NULL,
      is_active = true
    WHERE id = webhook_id;
  ELSE
    UPDATE webhook_urls
    SET 
      last_check_timestamp = CURRENT_TIMESTAMP,
      last_error_timestamp = CURRENT_TIMESTAMP,
      error_count = error_count + 1,
      error_message = error_msg,
      is_active = (error_count < max_retries)
    WHERE id = webhook_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the existing webhook configuration
UPDATE webhook_urls
SET 
  max_retries = 3,
  retry_delay_base = 1000,
  last_check_timestamp = CURRENT_TIMESTAMP
WHERE name = 'n8n_chatbot_agent';

-- Create a view for webhook health monitoring
CREATE OR REPLACE VIEW webhook_health AS
SELECT 
  id,
  name,
  url,
  is_active,
  last_check_timestamp,
  last_error_timestamp,
  error_count,
  error_message,
  CASE 
    WHEN error_count >= max_retries THEN 'failed'
    WHEN error_count > 0 THEN 'degraded'
    ELSE 'healthy'
  END as status
FROM webhook_urls;

-- Add policy for webhook health monitoring
CREATE POLICY "Allow read access to webhook health for authenticated users"
  ON webhook_urls
  FOR SELECT
  TO authenticated
  USING (true);