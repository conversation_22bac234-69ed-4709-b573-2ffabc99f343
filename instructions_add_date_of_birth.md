# Instructions pour mettre à jour la table profiles dans Supabase

Vous rencontrez maintenant une erreur de syntaxe SQL. Les commentaires dans le script ont causé un problème. Voici une version corrigée du script à exécuter.

## Étapes pour mettre à jour la table profiles et ses fonctions

1. Connectez-vous à votre [dashboard Supabase](https://app.supabase.io/)
2. Sélectionnez votre projet
3. Allez dans la section **SQL Editor**
4. Créez une nouvelle requête et copiez-collez le script SQL ci-dessous
5. Exécutez le script pour mettre à jour votre table `profiles` et ses fonctions associées

```sql
-- Supprimer d'abord les fonctions existantes pour les recréer
DROP FUNCTION IF EXISTS public.create_profile CASCADE;
DROP FUNCTION IF EXISTS public.update_profile CASCADE;

-- Ajout des colonnes manquantes à la table profiles
ALTER TABLE public.profiles 
  ADD COLUMN IF NOT EXISTS role TEXT,
  ADD COLUMN IF NOT EXISTS address TEXT,
  ADD COLUMN IF NOT EXISTS phone TEXT,
  ADD COLUMN IF NOT EXISTS date_of_birth TEXT,
  ADD COLUMN IF NOT EXISTS gender_identity TEXT,
  ADD COLUMN IF NOT EXISTS emergency_contact TEXT,
  ADD COLUMN IF NOT EXISTS language_preference TEXT,
  ADD COLUMN IF NOT EXISTS notification_preferences JSONB;

-- Renommer la colonne phone_number en phone s'il existe déjà
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'profiles' 
    AND column_name = 'phone_number'
  ) THEN
    ALTER TABLE public.profiles RENAME COLUMN phone_number TO phone;
  END IF;
END $$;

-- Modifier le type de date_of_birth si c'est actuellement un DATE
DO $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'profiles' 
    AND column_name = 'date_of_birth'
    AND data_type = 'date'
  ) THEN
    ALTER TABLE public.profiles ALTER COLUMN date_of_birth TYPE TEXT USING date_of_birth::TEXT;
  END IF;
END $$;

-- Recréer la fonction pour insérer facilement un profil
CREATE OR REPLACE FUNCTION public.create_profile(
    user_id UUID,
    user_email TEXT,
    user_first_name TEXT DEFAULT NULL,
    user_last_name TEXT DEFAULT NULL,
    user_role TEXT DEFAULT NULL,
    user_avatar_url TEXT DEFAULT NULL,
    user_address TEXT DEFAULT NULL,
    user_phone TEXT DEFAULT NULL,
    user_date_of_birth TEXT DEFAULT NULL,
    user_gender_identity TEXT DEFAULT NULL,
    user_emergency_contact TEXT DEFAULT NULL,
    user_language_preference TEXT DEFAULT NULL,
    user_notification_preferences JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_profile_id UUID;
BEGIN
    INSERT INTO public.profiles (
        id, email, first_name, last_name, role, avatar_url, 
        address, phone, date_of_birth, gender_identity, 
        emergency_contact, language_preference, notification_preferences
    )
    VALUES (
        user_id, user_email, user_first_name, user_last_name, user_role, user_avatar_url,
        user_address, user_phone, user_date_of_birth, user_gender_identity,
        user_emergency_contact, user_language_preference, user_notification_preferences
    )
    RETURNING id INTO v_profile_id;
    
    RETURN v_profile_id;
EXCEPTION
    WHEN unique_violation THEN
        RAISE NOTICE 'Profile already exists for user %', user_id;
        RETURN user_id;
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error creating profile: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recréer la fonction RPC pour mettre à jour le profil utilisateur
CREATE OR REPLACE FUNCTION public.update_profile(
    user_first_name TEXT DEFAULT NULL,
    user_last_name TEXT DEFAULT NULL,
    user_role TEXT DEFAULT NULL,
    user_avatar_url TEXT DEFAULT NULL,
    user_address TEXT DEFAULT NULL,
    user_phone TEXT DEFAULT NULL,
    user_date_of_birth TEXT DEFAULT NULL,
    user_gender_identity TEXT DEFAULT NULL,
    user_emergency_contact TEXT DEFAULT NULL,
    user_language_preference TEXT DEFAULT NULL,
    user_notification_preferences JSONB DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
    v_user_id UUID;
BEGIN
    v_user_id := auth.uid();
    
    IF v_user_id IS NULL THEN
        RETURN jsonb_build_object('success', false, 'message', 'Not authenticated');
    END IF;
    
    UPDATE public.profiles
    SET 
        first_name = COALESCE(user_first_name, first_name),
        last_name = COALESCE(user_last_name, last_name),
        role = COALESCE(user_role, role),
        avatar_url = COALESCE(user_avatar_url, avatar_url),
        address = COALESCE(user_address, address),
        phone = COALESCE(user_phone, phone),
        date_of_birth = COALESCE(user_date_of_birth, date_of_birth),
        gender_identity = COALESCE(user_gender_identity, gender_identity),
        emergency_contact = COALESCE(user_emergency_contact, emergency_contact),
        language_preference = COALESCE(user_language_preference, language_preference),
        notification_preferences = COALESCE(user_notification_preferences, notification_preferences),
        updated_at = timezone('utc'::text, now())
    WHERE id = v_user_id;
    
    RETURN jsonb_build_object('success', true);
EXCEPTION
    WHEN OTHERS THEN
        RETURN jsonb_build_object('success', false, 'message', SQLERRM);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Mettre à jour les commentaires sur les colonnes
COMMENT ON COLUMN public.profiles.role IS 'Rôle de l''utilisateur dans l''application';
COMMENT ON COLUMN public.profiles.address IS 'Adresse postale de l''utilisateur';
COMMENT ON COLUMN public.profiles.phone IS 'Numéro de téléphone de l''utilisateur';
COMMENT ON COLUMN public.profiles.date_of_birth IS 'Date de naissance de l''utilisateur';
COMMENT ON COLUMN public.profiles.gender_identity IS 'Identité de genre de l''utilisateur';
COMMENT ON COLUMN public.profiles.emergency_contact IS 'Contact d''urgence de l''utilisateur';
COMMENT ON COLUMN public.profiles.language_preference IS 'Préférence de langue de l''utilisateur';
COMMENT ON COLUMN public.profiles.notification_preferences IS 'Préférences de notification au format JSON';
```

## Important: Exécutez cette commande après avoir terminé pour rafraîchir le cache

```sql
SELECT pg_cache_plan_invalidate();
```

## Vérification

Après avoir exécuté le script, vous pouvez vérifier que les colonnes ont été ajoutées en exécutant :

```sql
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'profiles' 
ORDER BY ordinal_position;
```

## Instructions pour exécuter le script correctement :

1. Assurez-vous de ne copier que le code entre les délimiteurs de code (```sql et ```)
2. Exécutez le script par parties si nécessaire :
   - D'abord les commandes DROP FUNCTION
   - Puis les commandes ALTER TABLE
   - Ensuite les blocs DO (pour renommer et modifier les colonnes)
   - Puis les CREATE OR REPLACE FUNCTION un par un
   - Enfin les COMMENT ON COLUMN

Si vous rencontrez toujours des erreurs, essayez d'exécuter les parties individuellement dans des requêtes séparées.

## Résolution des erreurs persistantes

Si vous continuez à avoir des erreurs après avoir exécuté le script :

1. Essayez de redémarrer votre service Supabase dans les paramètres du projet
2. Assurez-vous que l'application est redémarrée pour prendre en compte les changements
3. Vérifiez les journaux d'erreur dans le dashboard Supabase pour des informations plus détaillées
