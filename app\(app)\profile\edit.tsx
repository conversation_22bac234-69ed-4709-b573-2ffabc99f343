import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Platform,
  Alert,
  Image,
  ActivityIndicator,
  Modal,
  KeyboardAvoidingView,
} from 'react-native';
import { router } from 'expo-router';
import { supabase } from '../../../lib/supabase';
import { useAuth } from '../../../context/auth';
import { useTheme } from '../../../context/theme';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import DateTimePicker from '@react-native-community/datetimepicker';
import { recordActivity } from '../../../lib/activity-tracking';
import { decode } from 'base64-arraybuffer';
import Animated, { FadeInUp, FadeIn } from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';

const AVATAR_DEFAULT = '/assets/images/profile.png';

// Types de genre disponibles
const genderOptions = [
  { label: 'Homme', value: 'homme' },
  { label: 'Femme', value: 'femme' },
  { label: 'Non-binaire', value: 'non-binaire' },
  { label: 'Préfère ne pas préciser', value: 'non_precise' },
];

export default function EditProfileScreen() {
  const { user, refreshUser } = useAuth();
  const { theme } = useTheme();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [uploadingAvatar, setUploadingAvatar] = useState(false);

  // États pour les modales
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showGenderPicker, setShowGenderPicker] = useState(false);
  const [birthDate, setBirthDate] = useState<Date | null>(null);

  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    phone: '',
    address: '',
    date_of_birth: '',
    gender_identity: '',
    emergency_contact: '',
    avatar_url: '',
    documents: [],
  });

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      if (!user) return;

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) throw error;

      if (data) {
        setFormData({
          ...formData,
          ...data,
        });

        // Traiter la date de naissance
        if (data.date_of_birth) {
          try {
            // Format attendu: DD/MM/YYYY
            const parts = data.date_of_birth.split('/');
            if (parts.length === 3) {
              const day = parseInt(parts[0], 10);
              const month = parseInt(parts[1], 10) - 1; // Les mois commencent à 0 en JavaScript
              const year = parseInt(parts[2], 10);
              const date = new Date(year, month, day);
              if (!isNaN(date.getTime())) {
                setBirthDate(date);
              }
            }
          } catch (e) {
            console.error('Erreur lors de la conversion de la date:', e);
          }
        }
      }
    } catch (err) {
      console.error('Error loading profile:', err);
      setError('Erreur lors du chargement du profil');
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    
    if (selectedDate) {
      setBirthDate(selectedDate);
      
      // Formater la date au format DD/MM/YYYY
      const day = selectedDate.getDate().toString().padStart(2, '0');
      const month = (selectedDate.getMonth() + 1).toString().padStart(2, '0');
      const year = selectedDate.getFullYear();
      
      setFormData({
        ...formData,
        date_of_birth: `${day}/${month}/${year}`
      });
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      if (!user) {
        Alert.alert('Erreur', 'Vous devez être connecté pour modifier votre profil.');
        return;
      }

      // Préparer les données à mettre à jour
      const updates = {
        id: user.id,
          first_name: formData.first_name,
          last_name: formData.last_name,
          phone: formData.phone || null,
          date_of_birth: formData.date_of_birth || null,
          gender_identity: formData.gender_identity || null,
        address: formData.address || null,
          emergency_contact: formData.emergency_contact || null,
        avatar_url: formData.avatar_url || null,
        updated_at: new Date()
      };
      
      // Mettre à jour le profil
      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id);
        
      if (error) {
        throw error;
      }
      
      // Enregistrer l'activité de mise à jour de profil
      recordActivity('profile_update', {
        updated_fields: Object.keys(updates).filter(key => key !== 'id' && key !== 'updated_at')
      });
      
      Alert.alert('Succès', 'Votre profil a été mis à jour avec succès.');
        router.back();
    } catch (error: any) {
      console.error('Erreur lors de la mise à jour du profil:', error);
      Alert.alert('Erreur', error.message || 'Une erreur est survenue lors de la mise à jour du profil.');
    } finally {
      setLoading(false);
    }
  };

  const handleUploadAvatar = async () => {
    try {
      // Demander la permission d'accéder à la galerie
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission refusée', 'Nous avons besoin de votre permission pour accéder à vos photos.');
        return;
      }

      // Lancer le sélecteur d'images
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        setUploadingAvatar(true);
        setError('');
        
        try {
          const asset = result.assets[0];
          
          // Vérifier que l'utilisateur est connecté
          if (!user) {
            throw new Error("Utilisateur non connecté");
          }
          
          // Générer un nom de fichier unique
          const fileExt = asset.uri.split('.').pop();
          const fileName = `avatar-${user.id}-${Date.now()}.${fileExt}`;
          const filePath = `avatars/${fileName}`;
          
          // Méthode alternative pour Android: utiliser FileSystem et base64-arraybuffer
          // au lieu de blob qui cause des erreurs "Network request failed"
          const base64 = await FileSystem.readAsStringAsync(asset.uri, {
            encoding: FileSystem.EncodingType.Base64,
          });
          
          // Utiliser la bibliothèque base64-arraybuffer pour convertir en ArrayBuffer
          const arrayBuffer = decode(base64);
          
          // Télécharger l'image vers Supabase Storage
          const { data, error } = await supabase.storage
            .from('avatars')
            .upload(filePath, arrayBuffer, {
              contentType: `image/${fileExt}`,
              upsert: true
            });
            
          if (error) {
            throw error;
          }
          
          // Obtenir l'URL publique de l'image
          const { data: publicUrlData } = supabase.storage
            .from('avatars')
            .getPublicUrl(filePath);
            
          const publicUrl = publicUrlData.publicUrl;
          
          // Mettre à jour l'état local avec l'URL publique
          setFormData(prev => ({ ...prev, avatar_url: publicUrl }));
          
          // Mettre à jour directement le profil dans la base de données
          const { error: updateError } = await supabase
            .from('profiles')
            .update({ 
              avatar_url: publicUrl,
              updated_at: new Date()
            })
            .eq('id', user.id);
            
          if (updateError) {
            throw updateError;
          }
          
          // Utiliser un type d'activité qui est déjà accepté par la contrainte de la base de données
          // au lieu d'essayer d'enregistrer l'activité
          
          Alert.alert('Succès', 'Votre photo de profil a été téléchargée et mise à jour avec succès.');
        } catch (error: any) {
          console.error('Erreur lors du traitement de la photo:', error);
          setError(`Une erreur est survenue: ${error.message || 'Erreur inconnue'}`);
        } finally {
          setUploadingAvatar(false);
        }
      }
    } catch (error: any) {
      console.error('Erreur lors de la sélection de la photo:', error);
      setError('Une erreur est survenue lors de la sélection de votre photo.');
      setUploadingAvatar(false);
    }
  };

  const handleUploadDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf', 'image/*'],
      });

      if (!result.canceled && result.assets[0]) {
        // Handle document upload to Supabase storage
        const file = result.assets[0];
        console.log('Selected file:', file);
        // TODO: Implement file upload to Supabase storage
      }
    } catch (err) {
      console.error('Error picking document:', err);
      setError('Erreur lors de la sélection du document');
    }
  };

  // Fonction pour obtenir l'URL de l'avatar
  const getAvatarUrl = () => {
    return formData.avatar_url || AVATAR_DEFAULT;
  };

  const getAvatarSource = () => {
    if (formData.avatar_url) {
      // Ajouter un timestamp pour éviter la mise en cache de l'image
      return { uri: `${formData.avatar_url}?t=${new Date().getTime()}` };
    }
    return require('../../../assets/images/profile.png');
  };

  const renderGenderPicker = () => (
    <Modal
      visible={showGenderPicker}
      transparent={true}
      animationType="slide"
      onRequestClose={() => setShowGenderPicker(false)}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: theme.colors.background }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.colors.text }]}>Sélectionnez votre genre</Text>
            <TouchableOpacity onPress={() => setShowGenderPicker(false)}>
              <Ionicons name="close" size={24} color={theme.colors.text} />
            </TouchableOpacity>
          </View>
          
          {genderOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.genderOption,
                formData.gender_identity === option.value && styles.selectedGenderOption
              ]}
              onPress={() => {
                setFormData({ ...formData, gender_identity: option.value });
                setShowGenderPicker(false);
              }}
            >
              <Text style={[
                styles.genderOptionText,
                { color: theme.colors.text },
                formData.gender_identity === option.value && styles.selectedGenderOptionText
              ]}>
                {option.label}
              </Text>
              {formData.gender_identity === option.value && (
                <Ionicons name="checkmark" size={20} color="#fff" />
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Modal>
  );

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1, backgroundColor: '#f7f9ff' }}
    >
      <ScrollView 
        style={styles.container}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View 
          style={styles.header}
          entering={FadeIn.duration(800)}
        >
          <TouchableOpacity 
            style={styles.backButton} 
            onPress={() => router.back()}
          >
            <Ionicons name="arrow-back" size={24} color="#0066ff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Modifier votre profil</Text>
          <View style={{ width: 24 }} />
        </Animated.View>

        <Animated.View 
          style={styles.avatarContainer}
          entering={FadeInUp.duration(800).delay(200)}
        >
          <LinearGradient
            colors={['#e6f2ff', '#ffffff']}
            style={styles.avatarGradient}
          >
            {uploadingAvatar ? (
              <View style={styles.loadingAvatarContainer}>
                <ActivityIndicator size="large" color="#0066ff" />
              </View>
            ) : (
              <TouchableOpacity 
                style={styles.avatarWrapper}
                onPress={handleUploadAvatar}
                activeOpacity={0.8}
              >
                <Image
                  source={getAvatarSource()}
                  style={styles.avatar}
                  resizeMode="cover"
                />
                <View style={styles.editAvatarButton}>
                  <Ionicons name="camera" size={16} color="#fff" />
                </View>
              </TouchableOpacity>
            )}
          </LinearGradient>
        </Animated.View>

        {error ? (
          <Animated.View 
            style={styles.errorContainer}
            entering={FadeInUp.duration(600)}
          >
            <Ionicons name="alert-circle" size={20} color="#e74c3c" />
            <Text style={styles.errorText}>{error}</Text>
          </Animated.View>
        ) : null}

        {success ? (
          <Animated.View 
            style={styles.successContainer}
            entering={FadeInUp.duration(600)}
          >
            <Ionicons name="checkmark-circle" size={20} color="#2ecc71" />
            <Text style={styles.successText}>{success}</Text>
          </Animated.View>
        ) : null}

        <Animated.View 
          style={styles.formCard}
          entering={FadeInUp.duration(800).delay(400)}
        >
          <View style={styles.formSection}>
            <Text style={styles.sectionTitle}>Informations personnelles</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Prénom</Text>
              <TextInput
                style={styles.input}
                value={formData.first_name}
                onChangeText={(text) => setFormData({...formData, first_name: text})}
                placeholder="Votre prénom"
                placeholderTextColor="#aaa"
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Nom</Text>
              <TextInput
                style={styles.input}
                value={formData.last_name}
                onChangeText={(text) => setFormData({...formData, last_name: text})}
                placeholder="Votre nom"
                placeholderTextColor="#aaa"
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Téléphone</Text>
              <TextInput
                style={styles.input}
                value={formData.phone}
                onChangeText={(text) => setFormData({...formData, phone: text})}
                placeholder="Votre numéro de téléphone"
                placeholderTextColor="#aaa"
                keyboardType="phone-pad"
              />
            </View>
          </View>
          
          <View style={styles.formSection}>
            <Text style={styles.sectionTitle}>Informations complémentaires</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Date de naissance</Text>
              <TouchableOpacity 
                style={styles.datePickerButton}
                onPress={() => setShowDatePicker(true)}
              >
                <Text style={styles.datePickerText}>
                  {formData.date_of_birth || "Sélectionner une date"}
                </Text>
                <Ionicons name="calendar" size={20} color="#0066ff" />
              </TouchableOpacity>
              {showDatePicker && (
                <DateTimePicker
                  value={birthDate || new Date()}
                  mode="date"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={handleDateChange}
                />
              )}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Genre</Text>
              <TouchableOpacity 
                style={styles.pickerButton}
                onPress={() => setShowGenderPicker(true)}
              >
                <Text style={styles.pickerText}>
                  {formData.gender_identity ? 
                    genderOptions.find(g => g.value === formData.gender_identity)?.label : 
                    "Sélectionner un genre"}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#0066ff" />
              </TouchableOpacity>
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Adresse</Text>
              <TextInput
                style={styles.input}
                value={formData.address}
                onChangeText={(text) => setFormData({...formData, address: text})}
                placeholder="Votre adresse"
                placeholderTextColor="#aaa"
                multiline
              />
            </View>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Contact d'urgence</Text>
              <TextInput
                style={styles.input}
                value={formData.emergency_contact}
                onChangeText={(text) => setFormData({...formData, emergency_contact: text})}
                placeholder="Nom et numéro de téléphone"
                placeholderTextColor="#aaa"
              />
            </View>
          </View>

          <Animated.View 
            style={styles.buttonsContainer}
            entering={FadeInUp.duration(600).delay(600)}
          >
            <TouchableOpacity 
              style={styles.cancelButton}
              onPress={() => router.back()}
              disabled={loading}
            >
              <Text style={styles.cancelButtonText}>Annuler</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.saveButton}
              onPress={handleSave}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.saveButtonText}>Enregistrer</Text>
              )}
            </TouchableOpacity>
          </Animated.View>
        </Animated.View>

        {showGenderPicker && renderGenderPicker()}
        
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7f9ff',
  },
  scrollContent: {
    paddingBottom: 40,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: Platform.OS === 'ios' ? 50 : 20,
    paddingBottom: 10,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  avatarContainer: {
    alignItems: 'center',
    marginVertical: 24,
  },
  avatarGradient: {
    padding: 4,
    borderRadius: 75,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  avatarWrapper: {
    position: 'relative',
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderColor: '#fff',
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#0066ff',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#fff',
  },
  loadingAvatarContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: '#fff',
  },
  formCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    marginHorizontal: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  formSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    backgroundColor: '#f7f9ff',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#333',
    borderWidth: 1,
    borderColor: '#e1e8f0',
  },
  datePickerButton: {
    backgroundColor: '#f7f9ff',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e1e8f0',
  },
  datePickerText: {
    fontSize: 16,
    color: '#333',
  },
  pickerButton: {
    backgroundColor: '#f7f9ff',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e1e8f0',
  },
  pickerText: {
    fontSize: 16,
    color: '#333',
  },
  errorContainer: {
    backgroundColor: '#fdeaea',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  errorText: {
    color: '#e74c3c',
    fontSize: 14,
    marginLeft: 8,
  },
  successContainer: {
    backgroundColor: '#eafaf1',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 16,
    marginBottom: 16,
  },
  successText: {
    color: '#2ecc71',
    fontSize: 14,
    marginLeft: 8,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#f7f9ff',
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#e1e8f0',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#555',
    fontWeight: '600',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#0066ff',
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    marginLeft: 8,
  },
  saveButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
  genderPicker: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  genderPickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  genderPickerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  genderPickerCloseButton: {
    padding: 8,
  },
  genderOption: {
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  genderOptionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedGenderOption: {
    backgroundColor: '#e6f2ff',
  },
  selectedGenderOptionText: {
    color: '#0066ff',
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    padding: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
});