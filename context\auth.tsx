import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../utils/supabase';
import { Session, User, AuthError } from '@supabase/supabase-js';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';
import { router } from 'expo-router';

export interface ErrorWithCode extends Error {
  code?: string;
}

export interface SignUpError extends Error {
  code?: string;
  message: string;
}

// Interface pour les données de profil
interface ProfileData {
  id: string;
  email: string;
  created_at: Date;
  updated_at: Date;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  phone_number?: string;
}

interface AuthContextType {
  user: User | null;
  session: Session | null;
  initialized: boolean;
  isLoggingIn: boolean;
  signUp: (email: string, password: string) => Promise<{ error: SignUpError | null }>;
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: Error | null }>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [initialized, setInitialized] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  useEffect(() => {
    // Vérifier si la session existe déjà
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setInitialized(true);
    });

    // Écouter les changements d'authentification
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, session) => {
      setSession(session);
        setUser(session?.user ?? null);
        setInitialized(true);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const refreshUser = async () => {
    const { data } = await supabase.auth.getUser();
    if (data?.user) {
      setUser(data.user);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setIsLoggingIn(true);
      console.log('Début de la connexion pour:', email);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('Erreur de connexion:', error);
        
        let errorMessage = 'Vérifiez vos identifiants et réessayez';
        
        if (error.message.includes('Invalid login credentials')) {
          errorMessage = 'Email ou mot de passe incorrect';
        } else if (error.message.includes('Email not confirmed')) {
          errorMessage = 'Veuillez confirmer votre email avant de vous connecter';
        }
        
        return { error: { message: errorMessage } as Error };
      }

      console.log('Connexion réussie pour:', email);
      return { error: null };
    } catch (error: any) {
      console.error('Exception lors de la connexion:', error);
      
      let errorMessage = 'Une erreur est survenue lors de la connexion';
      
      if (error.message) {
        errorMessage = error.message;
      }
      
      return { error: { message: errorMessage } as Error };
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Fonction d'inscription modifiée pour éviter l'erreur 500
  const signUp = async (email: string, password: string) => {
    try {
      // Vérifier d'abord si l'utilisateur existe déjà pour une meilleure gestion des erreurs
      const { data: existingUsers } = await supabase
        .from('profiles')
        .select('email')
        .eq('email', email)
        .limit(1);

      if (existingUsers && existingUsers.length > 0) {
        return {
          error: {
            name: 'UserAlreadyExistsError',
            message: 'Un utilisateur avec cet email existe déjà',
            code: 'user_already_registered'
          }
        };
      }

      // Préparer l'URL de redirection (selon la plateforme)
      const redirectUrl = Platform.OS === 'web' 
        ? window.location.origin + '/auth/callback'
        : 'bomoko://auth/callback';

      // Tentative d'inscription avec gestion explicite des erreurs
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          // Utilisez ceci uniquement si vous avez configuré la confirmation d'email
          // emailRedirectTo: redirectUrl,
          // Pour le débogage, nous pouvons désactiver temporairement la confirmation d'email
          // data: { confirm_email: false }
        }
      });

      // Gérer les erreurs spécifiques
      if (error) {
        if (error.message?.includes('already registered')) {
          return {
            error: {
              name: 'UserAlreadyExistsError',
              message: 'Un utilisateur avec cet email existe déjà',
              code: 'user_already_registered'
            }
          };
        }

        // Pour éviter l'erreur "Database error granting user", essayons une insertion manuelle
        if (error.message?.includes('Database error granting user') || 
            error.message?.includes('unexpected_failure')) {
          
          // Essayons une approche alternative
          try {
            const userData = data?.user as any;
            // Créer directement un profil si l'utilisateur a été créé mais le déclencheur a échoué
            if (userData && userData.id) {
              const profileData: ProfileData = {
                id: userData.id,
                email: email,
                created_at: new Date(),
                updated_at: new Date()
              };
              
              await supabase.from('profiles').insert(profileData);
            }
            
            // Si nous arrivons ici, l'insertion manuelle a peut-être fonctionné
            return { error: null };
          } catch (insertError) {
            console.error('Erreur lors de l\'insertion manuelle du profil:', insertError);
            // Continuer avec l'erreur originale
          }
        }

        return { error: error };
      }

      // Gérer le cas où l'utilisateur a été créé mais session est null (confirmation email requise)
      if (data?.user && !data?.session) {
        router.replace('/auth/verification' as any);
      }

      return { error: null };
    } catch (err: any) {
      console.error('Erreur non gérée lors de l\'inscription:', err);
      return {
        error: {
          name: err.name || 'SignUpError',
          message: err.message || 'Une erreur s\'est produite lors de l\'inscription',
          code: err.code
        }
      };
    }
  };

  const signOut = async () => {
    try {
      setIsLoggingIn(true);
      
      // Enregistrer l'activité de déconnexion - DÉSACTIVÉ TEMPORAIREMENT
      // try {
      //  await recordActivity('logout', {});
      // } catch (e) {
      //  console.warn('Impossible d\'enregistrer l\'activité de déconnexion', e);
      // }
      
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Erreur lors de la déconnexion:', error);
        throw error;
      }
      router.replace('/login' as any);
    } catch (error: any) {
      console.error('Erreur de déconnexion:', error.message);
    } finally {
      setIsLoggingIn(false);
    }
  };

  const resetPassword = async (email: string) => {
    const redirectUrl = Platform.OS === 'web' 
      ? window.location.origin + '/auth/reset-callback'
      : 'bomoko://auth/reset-callback';
      
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: redirectUrl,
    });
    
    return { error };
  };

  const value = {
    user,
    session,
    initialized,
    isLoggingIn,
    signIn,
    signUp,
    signOut,
    resetPassword,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}