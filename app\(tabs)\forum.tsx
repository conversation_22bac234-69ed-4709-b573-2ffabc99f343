import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { Stack } from 'expo-router';
import { useTheme } from '../../context/theme';
import ForumScreen from '../(app)/forum';

export default function ForumTab() {
  const { theme } = useTheme();

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      <ForumScreen />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});