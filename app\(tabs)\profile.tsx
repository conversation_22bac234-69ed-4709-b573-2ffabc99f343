import { View, Text, StyleSheet, TouchableOpacity, ScrollView, Platform, Image } from 'react-native';
import { useTheme } from '../../context/theme';
import { useAuth } from '../../context/auth';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import ThemeSelector from '../components/ThemeSelector';

export default function ProfileScreen() {
  const { theme } = useTheme();
  const { user, signOut } = useAuth();
  const [profileData, setProfileData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadProfileData();
    }
  }, [user]);

  const loadProfileData = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user?.id)
        .single();

      if (error) {
        console.error('Error loading profile data:', error);
      } else if (data) {
        setProfileData(data);
      }
    } catch (err) {
      console.error('Error in loadProfileData:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleEditProfile = () => {
    router.push('/(app)/profile/edit');
  };

  const handleAssessment = () => {
    router.push('/(app)/assessment');
  };

  // Fonction pour obtenir le label du genre
  const getGenderLabel = (value: string) => {
    const genderOptions = [
      { label: 'Homme', value: 'homme' },
      { label: 'Femme', value: 'femme' },
      { label: 'Non-binaire', value: 'non-binaire' },
      { label: 'Préfère ne pas préciser', value: 'non_precise' },
    ];
    
    const option = genderOptions.find(opt => opt.value === value);
    return option ? option.label : value;
  };

  // Ajouter une fonction pour obtenir l'URL de l'avatar avec un timestamp
  const getAvatarUrl = (url: string) => {
    return `${url}?t=${new Date().getTime()}`;
  };

  if (!user) {
    return (
      <View style={[styles.container, { justifyContent: 'center', backgroundColor: theme.colors.background }]}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Connectez-vous pour accéder à votre profil
        </Text>
        <TouchableOpacity
          style={[styles.button, { backgroundColor: theme.colors.primary }]}
          onPress={() => router.push('/(auth)/login')}>
          <Text style={styles.buttonText}>Se connecter</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        {profileData?.avatar_url ? (
          <Image
            style={styles.avatar}
            source={{ uri: getAvatarUrl(profileData.avatar_url) }}
          />
        ) : (
        <View
          style={[
            styles.avatarContainer,
            { backgroundColor: theme.colors.primary },
          ]}>
          <Text style={styles.avatarText}>
            {profileData?.first_name 
              ? profileData.first_name[0].toUpperCase() 
              : user.email && user.email[0] 
                ? user.email[0].toUpperCase() 
                : '?'}
          </Text>
        </View>
        )}
        <Text style={[styles.name, { color: theme.colors.text }]}>
          {profileData?.first_name && profileData?.last_name 
            ? `${profileData.first_name} ${profileData.last_name}`
            : 'Profil incomplet'}
        </Text>
        <Text style={[styles.email, { color: theme.colors.gray[600] }]}>
          {user.email}
        </Text>
      
        <TouchableOpacity
          style={[styles.editProfileButton, { backgroundColor: theme.colors.primary }]}
          onPress={handleEditProfile}>
          <Ionicons name="create-outline" size={18} color="white" />
          <Text style={styles.editProfileButtonText}>
            Modifier le profil
          </Text>
        </TouchableOpacity>
      </View>

      <View style={[styles.infoCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.gray[300] }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Informations personnelles
        </Text>
        
        <View style={styles.infoRow}>
          <View style={styles.infoItem}>
            <Ionicons name="call-outline" size={20} color={theme.colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: theme.colors.gray[600] }]}>Téléphone</Text>
              <Text style={[styles.infoValue, { color: theme.colors.text }]}>
                {profileData?.phone || 'Non renseigné'}
              </Text>
            </View>
          </View>
          
          <View style={styles.infoItem}>
            <Ionicons name="calendar-outline" size={20} color={theme.colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: theme.colors.gray[600] }]}>Date de naissance</Text>
              <Text style={[styles.infoValue, { color: theme.colors.text }]}>
                {profileData?.date_of_birth || 'Non renseignée'}
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.infoRow}>
          <View style={styles.infoItem}>
            <Ionicons name="person-outline" size={20} color={theme.colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: theme.colors.gray[600] }]}>Genre</Text>
              <Text style={[styles.infoValue, { color: theme.colors.text }]}>
                {profileData?.gender_identity ? getGenderLabel(profileData.gender_identity) : 'Non renseigné'}
              </Text>
            </View>
          </View>
          
          <View style={styles.infoItem}>
            <Ionicons name="home-outline" size={20} color={theme.colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: theme.colors.gray[600] }]}>Adresse</Text>
              <Text style={[styles.infoValue, { color: theme.colors.text }]}>
                {profileData?.address || 'Non renseignée'}
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.infoRow}>
          <View style={styles.infoItem}>
            <Ionicons name="alert-circle-outline" size={20} color={theme.colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: theme.colors.gray[600] }]}>Contact d'urgence</Text>
              <Text style={[styles.infoValue, { color: theme.colors.text }]}>
                {profileData?.emergency_contact || 'Non renseigné'}
              </Text>
            </View>
          </View>
        </View>
      </View>

      <View style={[styles.infoCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.gray[300] }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Préférences
        </Text>
        
        <View style={styles.infoRow}>
          <View style={styles.infoItem}>
            <Ionicons name="language-outline" size={20} color={theme.colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: theme.colors.gray[600] }]}>Langue</Text>
              <Text style={[styles.infoValue, { color: theme.colors.text }]}>
                {profileData?.language_preference === 'fr' ? 'Français' : 
                 profileData?.language_preference === 'en' ? 'English' : 
                 'Non renseignée'}
              </Text>
            </View>
          </View>
          
          <View style={styles.infoItem}>
            <Ionicons name="notifications-outline" size={20} color={theme.colors.primary} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: theme.colors.gray[600] }]}>Notifications</Text>
              <View style={styles.notificationBadges}>
                {profileData?.notification_preferences?.email && (
                  <View style={[styles.badge, { backgroundColor: theme.colors.primary }]}>
                    <Text style={styles.badgeText}>Email</Text>
                  </View>
                )}
                {profileData?.notification_preferences?.push && (
                  <View style={[styles.badge, { backgroundColor: theme.colors.primary }]}>
                    <Text style={styles.badgeText}>Push</Text>
                  </View>
                )}
                {profileData?.notification_preferences?.sms && (
                  <View style={[styles.badge, { backgroundColor: theme.colors.primary }]}>
                    <Text style={styles.badgeText}>SMS</Text>
                  </View>
                )}
                {!profileData?.notification_preferences?.email && 
                 !profileData?.notification_preferences?.push && 
                 !profileData?.notification_preferences?.sms && (
                  <Text style={[styles.infoValue, { color: theme.colors.text }]}>
                    Aucune
                  </Text>
                )}
              </View>
            </View>
          </View>
        </View>
      </View>

      <View style={[styles.infoCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.gray[300] }]}>
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Paramètres d'affichage
        </Text>
        
        <ThemeSelector />
      </View>

      {profileData?.role === 'admin' && (
        <View style={[styles.infoCard, { backgroundColor: theme.colors.surface, borderColor: theme.colors.gray[300] }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Administration
          </Text>
          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => router.push('/admin/user-activities')}
          >
            <View style={styles.settingIconContainer}>
              <Ionicons name="analytics-outline" size={24} color={theme.colors.primary} />
            </View>
            <View style={styles.settingTextContainer}>
              <Text style={[styles.settingTitle, { color: theme.colors.text }]}>
                Suivi des activités utilisateurs
              </Text>
              <Text style={[styles.settingDescription, { color: theme.colors.gray[500] }]}>
                Analyser le comportement des utilisateurs
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={theme.colors.gray[400]} />
          </TouchableOpacity>
        </View>
      )}

      <TouchableOpacity
        style={[styles.signOutButton, { borderColor: theme.colors.error }]}
        onPress={handleSignOut}>
        <Ionicons name="log-out-outline" size={24} color={theme.colors.error} />
        <Text style={[styles.signOutText, { color: theme.colors.error }]}>
          Se déconnecter
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
  },
  avatarText: {
    color: 'white',
    fontSize: 36,
    fontWeight: 'bold',
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  email: {
    fontSize: 16,
    marginBottom: 16,
  },
  editProfileButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    gap: 8,
  },
  editProfileButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  infoCard: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  section: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    width: '50%',
    marginBottom: 16,
    paddingRight: 8,
  },
  infoContent: {
    marginLeft: 8,
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  notificationBadges: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginRight: 4,
    marginBottom: 4,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 15,
    gap: 8,
  },
  editButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderRadius: 12,
    marginBottom: 10,
  },
  settingIcon: {
    marginRight: 15,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    margin: 20,
    borderRadius: 12,
    borderWidth: 1,
  },
  signOutText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  button: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 20,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  settingIconContainer: {
    marginRight: 15,
  },
  settingTextContainer: {
    flex: 1,
  },
});