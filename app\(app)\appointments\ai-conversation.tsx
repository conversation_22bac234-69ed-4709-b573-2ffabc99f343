import React, { useState, useEffect, useRef } from 'react';
import { processFallbackRequest } from '../../../app/utils/fallback-ai-agent';
import { getWebhookUrl, sendWebhookRequest } from '../../../app/utils/webhook-utils';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { supabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { showAnimatedSuccessAlert, showAnimatedErrorAlert } from '../../components/SweetAlert';

// Types pour les messages de conversation
type MessageType = 'user' | 'agent' | 'system';

interface Message {
  id: string;
  type: MessageType;
  content: string;
  timestamp: Date;
  isLoading?: boolean;
}

// Type pour les données de la demande de rendez-vous
interface AppointmentRequest {
  requestId: string;
  userId: string;
  dateTime: string;
  reason: string;
  appointmentType: string;
  notes?: string;
  conversationId?: string;
}

export default function AIConversationScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const params = useLocalSearchParams();
  const scrollViewRef = useRef<ScrollView>(null);

  // Récupérer les paramètres de la demande de rendez-vous
  const requestId = params.requestId as string;
  const appointmentDateTime = params.dateTime as string;
  const appointmentReason = params.reason as string;
  const appointmentType = params.type as string;
  const appointmentNotes = params.notes as string;

  // États pour la conversation
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [appointmentCreated, setAppointmentCreated] = useState(false);

  // Initialiser la conversation
  useEffect(() => {
    if (user) {
      initializeConversation();
    }
  }, [user]);

  // Faire défiler automatiquement vers le bas lorsque de nouveaux messages arrivent
  useEffect(() => {
    if (scrollViewRef.current) {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages]);

  // Initialiser la conversation avec l'agent AI
  const initializeConversation = async () => {
    try {
      setIsLoading(true);

      // Ajouter un message de bienvenue
      const welcomeMessage: Message = {
        id: 'welcome',
        type: 'system',
        content: 'Bienvenue dans le système de prise de rendez-vous assisté par IA. Notre agent va vous aider à planifier un rendez-vous avec un professionnel adapté à vos besoins.',
        timestamp: new Date(),
      };

      setMessages([welcomeMessage]);

      // Récupérer l'URL du webhook
      const webhookUrl = await getWebhookUrl('RDV AI WEBHOOK URL');

      if (!webhookUrl) {
        console.warn('URL du webhook non disponible ou service désactivé. Utilisation de l\'agent de secours.');
        // Nous allons continuer et utiliser l'agent de secours plus tard
      }

      // Récupérer le profil utilisateur
      const { data: profileData } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user?.id)
        .single();

      // Créer un ID de conversation unique
      const newConversationId = `conv_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
      setConversationId(newConversationId);

      // Préparer les données pour l'initialisation de la conversation
      const initData = {
        action: 'initialize_conversation',
        conversation_id: newConversationId,
        request_id: requestId,
        user_id: user?.id,
        user_email: user?.email,
        user_profile: profileData,
        appointment_request: {
          date_time: appointmentDateTime,
          reason: appointmentReason,
          type: appointmentType,
          notes: appointmentNotes
        },
        // Données spécifiques pour l'agent autonome N8N
        input: {
          message: `Bonjour, je souhaite prendre rendez-vous pour la raison suivante : ${appointmentReason}. ${appointmentNotes ? `Notes additionnelles : ${appointmentNotes}` : ''}`,
          preferredDateTime: appointmentDateTime,
          appointmentType: appointmentType
        }
      };

      // Ajouter un message de chargement
      const loadingMessage: Message = {
        id: 'loading',
        type: 'agent',
        content: 'Analyse de votre demande...',
        timestamp: new Date(),
        isLoading: true
      };

      setMessages(prev => [...prev, loadingMessage]);

      // Ajouter des logs pour déboguer
      console.log('Initialisation de la conversation avec les données:', JSON.stringify(initData, null, 2));
      console.log('URL du webhook:', webhookUrl);

      let responseData;
      let useFallback = false;

      // Essayer d'envoyer la demande au webhook N8N si l'URL est disponible
      if (webhookUrl) {
        try {
          const result = await sendWebhookRequest(webhookUrl, initData);

          if (result.ok) {
            responseData = result.data;
            console.log('Réponse du webhook:', responseData);
          } else {
            console.warn(`Erreur du webhook. Utilisation de l'agent de secours.`);
            useFallback = true;
          }
        } catch (error) {
          console.error('Erreur lors de la requête au webhook:', error);
          useFallback = true;
        }
      } else {
        // Pas d'URL de webhook disponible, utiliser l'agent de secours
        console.warn(`Pas d'URL de webhook disponible. Utilisation de l'agent de secours.`);
        useFallback = true;
      }

      // Si le webhook a échoué, utiliser l'agent de secours
      if (useFallback) {
        try {
          console.log('Utilisation de l\'agent de secours pour l\'initialisation');
          responseData = await processFallbackRequest(initData);
          console.log('Réponse de l\'agent de secours:', responseData);
        } catch (error) {
          console.error('Erreur lors de l\'utilisation de l\'agent de secours:', error);
          throw new Error('Impossible d\'initialiser la conversation. Veuillez réessayer.');
        }
      }

      // Remplacer le message de chargement par la réponse de l'agent
      const agentResponse = responseData || { message: 'Conversation initialisée' };

      setMessages(prev => prev.filter(msg => msg.id !== 'loading').concat({
        id: `agent_${Date.now()}`,
        type: 'agent',
        content: agentResponse.message || 'Bonjour, je suis votre assistant pour la prise de rendez-vous. Comment puis-je vous aider aujourd\'hui?',
        timestamp: new Date()
      }));

      return agentResponse;

    } catch (error) {
      console.error('Erreur lors de l\'initialisation de la conversation:', error);

      // Ajouter un message d'erreur
      setMessages(prev => [...prev, {
        id: `error_${Date.now()}`,
        type: 'system',
        content: 'Une erreur est survenue lors de l\'initialisation de la conversation. Veuillez réessayer plus tard.',
        timestamp: new Date()
      }]);

      showAnimatedErrorAlert(
        'Impossible de démarrer la conversation avec l\'agent AI. Veuillez réessayer plus tard.',
        () => router.back()
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Envoyer un message à l'agent AI
  const sendMessage = async () => {
    if (!inputMessage.trim() || !conversationId || isLoading) return;

    const messageToSend = inputMessage.trim();
    setInputMessage('');

    try {
      setIsLoading(true);

      // Ajouter le message de l'utilisateur à la conversation
      const userMessage: Message = {
        id: `user_${Date.now()}`,
        type: 'user',
        content: messageToSend,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, userMessage]);

      // Ajouter un message de chargement
      const loadingMessage: Message = {
        id: 'loading',
        type: 'agent',
        content: '...',
        timestamp: new Date(),
        isLoading: true
      };

      setMessages(prev => [...prev, loadingMessage]);

      // Récupérer l'URL du webhook
      const webhookUrl = await getWebhookUrl('RDV AI WEBHOOK URL');

      // Préparer les données pour l'envoi du message
      const messageData = {
        action: 'send_message',
        conversation_id: conversationId,
        user_id: user?.id,
        message: messageToSend,
        // Données spécifiques pour l'agent autonome N8N
        input: {
          message: messageToSend,
          conversationId: conversationId
        }
      };

      // Ajouter des logs pour déboguer
      console.log('Envoi du message avec les données:', JSON.stringify(messageData, null, 2));

      let responseData;
      let useFallback = false;

      // Essayer d'envoyer la demande au webhook N8N si l'URL est disponible
      if (webhookUrl) {
        try {
          const result = await sendWebhookRequest(webhookUrl, messageData);

          if (result.ok) {
            responseData = result.data;
            console.log('Réponse du webhook:', responseData);
          } else {
            console.warn(`Erreur du webhook. Utilisation de l'agent de secours.`);
            useFallback = true;
          }
        } catch (error) {
          console.error('Erreur lors de la requête au webhook:', error);
          useFallback = true;
        }
      } else {
        // Pas d'URL de webhook disponible, utiliser l'agent de secours
        console.warn(`Pas d'URL de webhook disponible. Utilisation de l'agent de secours.`);
        useFallback = true;
      }

      // Si le webhook a échoué, utiliser l'agent de secours
      if (useFallback) {
        try {
          console.log('Utilisation de l\'agent de secours pour l\'envoi de message');
          responseData = await processFallbackRequest(messageData);
          console.log('Réponse de l\'agent de secours:', responseData);
        } catch (error) {
          console.error('Erreur lors de l\'utilisation de l\'agent de secours:', error);
          throw new Error('Impossible de traiter votre message. Veuillez réessayer.');
        }
      }

      // Remplacer le message de chargement par la réponse de l'agent
      setMessages(prev => prev.filter(msg => msg.id !== 'loading').concat({
        id: `agent_${Date.now()}`,
        type: 'agent',
        content: responseData.message || 'Je n\'ai pas compris votre message. Pouvez-vous reformuler?',
        timestamp: new Date()
      }));

      // Vérifier si un rendez-vous a été créé
      if (responseData.appointment_created) {
        setAppointmentCreated(true);

        // Récupérer les détails du rendez-vous créé
        const appointmentDetails = responseData.appointment_details || {};

        // Formater la date et l'heure du rendez-vous
        let formattedDateTime = 'Date à confirmer';
        if (appointmentDetails.date_time) {
          const appointmentDate = new Date(appointmentDetails.date_time);
          formattedDateTime = format(appointmentDate, 'EEEE d MMMM yyyy à HH:mm', { locale: fr });
        }

        // Formater les informations du professionnel
        let professionalInfo = 'Professionnel à confirmer';
        if (appointmentDetails.professional) {
          const professional = appointmentDetails.professional;
          professionalInfo = `${professional.title || ''} ${professional.first_name || ''} ${professional.last_name || ''}`;
          if (professional.speciality) {
            professionalInfo += ` (${professional.speciality})`;
          }
        }

        // Créer un message de confirmation détaillé
        const confirmationMessage = `
**Rendez-vous confirmé**

📅 **Date et heure**: ${formattedDateTime}
👨‍⚕️ **Professionnel**: ${professionalInfo}
🏥 **Type**: ${appointmentDetails.type || 'Consultation'}
${appointmentDetails.location ? `📍 **Lieu**: ${appointmentDetails.location}\n` : ''}
${appointmentDetails.notes ? `📝 **Notes**: ${appointmentDetails.notes}\n` : ''}

Vous recevrez une notification de rappel avant votre rendez-vous.`;

        // Ajouter un message de confirmation
        setMessages(prev => [...prev, {
          id: `system_${Date.now()}`,
          type: 'system',
          content: confirmationMessage,
          timestamp: new Date()
        }]);

        // Rediriger vers la liste des rendez-vous après un court délai
        setTimeout(() => {
          showAnimatedSuccessAlert(
            'Rendez-vous créé avec succès!',
            () => router.replace('/appointments')
          );
        }, 5000);
      }

    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error);

      // Remplacer le message de chargement par un message d'erreur
      setMessages(prev => prev.filter(msg => msg.id !== 'loading').concat({
        id: `error_${Date.now()}`,
        type: 'system',
        content: 'Une erreur est survenue lors de l\'envoi du message. Veuillez réessayer.',
        timestamp: new Date()
      }));
    } finally {
      setIsLoading(false);
    }
  };

  // Formater l'heure du message
  const formatMessageTime = (date: Date) => {
    return format(date, 'HH:mm', { locale: fr });
  };

  // Annuler la conversation et revenir en arrière
  const cancelConversation = async () => {
    if (conversationId) {
      try {
        // Récupérer l'URL du webhook
        const webhookUrl = await getWebhookUrl('RDV AI WEBHOOK URL');

        if (webhookUrl) {
          // Informer le webhook que la conversation a été annulée
          await sendWebhookRequest(webhookUrl, {
            action: 'cancel_conversation',
            conversation_id: conversationId,
            user_id: user?.id
          });
        } else {
          // Si le webhook n'est pas disponible, utiliser l'agent de secours
          await processFallbackRequest({
            action: 'cancel_conversation',
            conversation_id: conversationId,
            user_id: user?.id,
            request_id: ''
          });
        }
      } catch (error) {
        console.error('Erreur lors de l\'annulation de la conversation:', error);
      }
    }

    router.back();
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={cancelConversation}>
            <Ionicons
              name="arrow-back"
              size={24}
              color={theme.colors.text}
            />
          </TouchableOpacity>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            Assistant de rendez-vous
          </Text>
        </View>

        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
        >
          {messages.map((message) => (
            <View
              key={message.id}
              style={[
                styles.messageContainer,
                message.type === 'user' ? styles.userMessage :
                message.type === 'agent' ? styles.agentMessage : styles.systemMessage,
                message.type === 'user' ? { backgroundColor: theme.colors.primary } :
                message.type === 'agent' ? { backgroundColor: theme.colors.surface } : { backgroundColor: theme.colors.gray[200] }
              ]}
            >
              {message.isLoading ? (
                <ActivityIndicator size="small" color={theme.colors.primary} />
              ) : (
                <>
                  <Text
                    style={[
                      styles.messageText,
                      message.type === 'user' ? { color: 'white' } : { color: theme.colors.text }
                    ]}
                  >
                    {message.content}
                  </Text>
                  <Text
                    style={[
                      styles.messageTime,
                      message.type === 'user' ? { color: 'rgba(255, 255, 255, 0.7)' } : { color: theme.colors.gray[500] }
                    ]}
                  >
                    {formatMessageTime(message.timestamp)}
                  </Text>
                </>
              )}
            </View>
          ))}
        </ScrollView>

        <View style={[styles.inputContainer, { borderTopColor: theme.colors.gray[300] }]}>
          <TextInput
            style={[styles.input, { backgroundColor: theme.colors.surface, color: theme.colors.text }]}
            placeholder="Tapez votre message..."
            placeholderTextColor={theme.colors.gray[500]}
            value={inputMessage}
            onChangeText={setInputMessage}
            multiline
            maxLength={500}
            editable={!isLoading && !appointmentCreated}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              { backgroundColor: theme.colors.primary },
              (isLoading || !inputMessage.trim() || appointmentCreated) && { opacity: 0.5 }
            ]}
            onPress={sendMessage}
            disabled={isLoading || !inputMessage.trim() || appointmentCreated}
          >
            <Ionicons name="send" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    paddingTop: Platform.OS === 'ios' ? 60 : 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
  },
  messageContainer: {
    borderRadius: 16,
    padding: 12,
    marginBottom: 8,
    maxWidth: '80%',
    minWidth: 120,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  userMessage: {
    alignSelf: 'flex-end',
    borderBottomRightRadius: 4,
  },
  agentMessage: {
    alignSelf: 'flex-start',
    borderBottomLeftRadius: 4,
  },
  systemMessage: {
    alignSelf: 'center',
    borderRadius: 8,
    maxWidth: '90%',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  messageTime: {
    fontSize: 12,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  inputContainer: {
    flexDirection: 'row',
    padding: 12,
    borderTopWidth: 1,
    alignItems: 'center',
  },
  input: {
    flex: 1,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 8,
    maxHeight: 100,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default AIConversationScreen;
