import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
  TextInput,
  ActivityIndicator,
  Image,
  Animated,
  RefreshControl,
  StatusBar,
  Pressable,
  FlatList,
} from 'react-native';
import { router, Stack, useRouter } from 'expo-router';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { supabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ForumCategory, ForumTopic, useForumData } from './hooks/useForumData';

// Ajoutons un type pour les notifications
type ForumNotification = {
  id: string;
  type: 'new_topic' | 'new_reply' | 'reaction' | 'mention';
  title: string;
  message: string;
  read: boolean;
  created_at: string;
  topic_id?: string;
  user_id?: string;
  user_name?: string;
  user_avatar?: string;
};

export default function ForumScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [refreshing, setRefreshing] = useState(false);
  const [hasMoreTopics, setHasMoreTopics] = useState(true);
  const [sortBy, setSortBy] = useState<'newest' | 'popular' | 'unanswered'>('newest');
  const [showFilters, setShowFilters] = useState(false);
  const [hasNotifications, setHasNotifications] = useState(false);
  const [notifications, setNotifications] = useState<ForumNotification[]>([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [showNotificationsPanel, setShowNotificationsPanel] = useState(false);
  
  const [categories, setCategories] = useState<ForumCategory[]>([]);
  const [topics, setTopics] = useState<ForumTopic[]>([]);
  const [loading, setLoading] = useState(true);
  const [initialLoad, setInitialLoad] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const notificationsPanelAnim = useRef(new Animated.Value(-400)).current;
  const notificationBackdropAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    StatusBar.setBarStyle('light-content');
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true
    }).start();
    
    loadCategories();
    fetchNotifications();
    
    // Chargez les notifications toutes les 60 secondes
    const notificationsInterval = setInterval(() => {
      fetchNotifications();
    }, 60000);
    
    return () => {
      StatusBar.setBarStyle('default');
      clearInterval(notificationsInterval);
    };
  }, []);

  useEffect(() => {
    if (selectedCategory) {
      fetchTopics(selectedCategory);
    } else {
      fetchTopics();
    }
    setCurrentPage(1);
    setHasMoreTopics(true);
  }, [selectedCategory, sortBy]);
  
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadCategories();
      await fetchTopics(selectedCategory || undefined);
      setCurrentPage(1);
      setHasMoreTopics(true);
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = () => {
    if (!loading && hasMoreTopics) {
      const nextPage = currentPage + 1;
      fetchTopics(selectedCategory || undefined, nextPage, false).then(newTopics => {
        if (newTopics.length === 0) {
          setHasMoreTopics(false);
        } else {
          setCurrentPage(nextPage);
        }
      });
    }
  };

  const handleSelectCategory = (categoryId: string | null) => {
    setSearchQuery('');
    setSelectedCategory(categoryId);
  };

  const handleReaction = async (topicId: string, hasReacted: boolean) => {
    if (!user) {
      router.push('/');
      return;
    }

    if (hasReacted) {
      await removeReaction(topicId, user.id);
    } else {
      await addReaction(topicId, 'like', user.id);
    }
  };
  
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };
  
  const navigateToNotifications = () => {
    // Ouvrir/fermer le panneau de notifications
    if (showNotificationsPanel) {
      hideNotificationsPanel();
    } else {
      openNotificationsPanel();
    }
  };
  
  const openNotificationsPanel = () => {
    setShowNotificationsPanel(true);
    Animated.parallel([
      Animated.timing(notificationsPanelAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true
      }),
      Animated.timing(notificationBackdropAnim, {
        toValue: 0.5,
        duration: 300,
        useNativeDriver: true
      })
    ]).start();
  };
  
  const hideNotificationsPanel = () => {
    Animated.parallel([
      Animated.timing(notificationsPanelAnim, {
        toValue: -400,
        duration: 300,
        useNativeDriver: true
      }),
      Animated.timing(notificationBackdropAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true
      })
    ]).start(() => {
      setShowNotificationsPanel(false);
    });
  };
  
  const markNotificationAsRead = (notificationId: string) => {
    // Marquer une notification comme lue
    setNotifications(prev => 
      prev.map(n => 
        n.id === notificationId ? { ...n, read: true } : n
      )
    );
    const updatedUnreadCount = notifications.filter(n => n.id !== notificationId && !n.read).length;
    setNotificationCount(updatedUnreadCount);
    setHasNotifications(updatedUnreadCount > 0);
  };
  
  const markAllNotificationsAsRead = () => {
    // Marquer toutes les notifications comme lues
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
    setNotificationCount(0);
    setHasNotifications(false);
  };

  const handleNotificationPress = (notification: ForumNotification) => {
    // Naviguer vers la discussion associée à la notification
    markNotificationAsRead(notification.id);
    hideNotificationsPanel();
    
    if (notification.topic_id) {
      router.push({ 
        pathname: '/forum/topic',
        params: { id: notification.topic_id } 
      });
    }
  };
  
  const formatNotificationTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    
    if (diffInMinutes < 1) return 'À l\'instant';
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes} min`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `Il y a ${diffInHours}h`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `Il y a ${diffInDays}j`;
    
    return format(date, 'dd/MM/yyyy');
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new_topic':
        return 'create-outline';
      case 'new_reply':
        return 'chatbubble-outline';
      case 'reaction':
        return 'heart-outline';
      case 'mention':
        return 'at-outline';
      default:
        return 'notifications-outline';
    }
  };

  const loadCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: categoriesError } = await supabase
        .from('forum_categories')
        .select(`
          *,
          topic_count: forum_topics(count)
        `)
        .order('order_index');

      if (categoriesError) throw categoriesError;

      const formattedData = data?.map(category => ({
        ...category,
        topic_count: typeof category.topic_count === 'object' && category.topic_count.count 
          ? Number(category.topic_count.count) 
          : 0
      })) || [];

      setCategories(formattedData);
    } catch (err) {
      console.error('Error loading categories:', err);
      setError('Erreur lors du chargement des catégories');
    } finally {
      setLoading(false);
    }
  };

  const fetchTopics = async (categoryId?: string, page = 1, isRefresh = true) => {
    try {
      setLoading(true);
      setError(null);

      // Requête modifiée pour éviter les jointures problématiques
      let query = supabase
        .from('forum_topics')
        .select(`
          id, title, content, created_at, updated_at, 
          user_id, category_id, is_anonymous, is_pinned, is_locked, moderation_status,
          category:forum_categories(id, name, icon, color)
        `)
        .order('created_at', { ascending: false })
        .range((page - 1) * 10, page * 10 - 1);
        
      if (categoryId && categoryId !== 'undefined') {
        query = query.eq('category_id', categoryId);
      }
      
      const { data, error: topicsError } = await query;

      if (topicsError) throw topicsError;

      // Ne pas montrer le message "Aucune discussion" pendant le chargement des données
      if (data.length === 0 && isRefresh) {
        // Si aucune donnée n'est trouvée mais que nous sommes toujours en train de charger,
        // attendons la fin du chargement complet avant de montrer le message "Aucune discussion"
        setTopics([]);
      } else {
        // Traiter les données brutes pour les adapter au type ForumTopic
        const formattedData = (data || []).map(topic => {
          // Extraire la catégorie du format tableau retourné par Supabase
          const categoryData = topic.category && topic.category.length > 0 
            ? topic.category[0] 
            : null;
          
          // Construire l'objet topic correctement typé
          return {
            id: topic.id,
            title: topic.title,
            content: topic.content,
            created_at: topic.created_at,
            updated_at: topic.updated_at,
            user_id: topic.user_id,
            category_id: topic.category_id,
            is_anonymous: topic.is_anonymous,
            is_pinned: topic.is_pinned,
            is_locked: topic.is_locked,
            moderation_status: topic.moderation_status || null,
            has_reacted: false,
            _count: {
              reactions: 0,
              replies: 0
            },
            user: null,
            category: categoryData ? {
              id: categoryData.id,
              name: categoryData.name,
              icon: categoryData.icon,
              color: categoryData.color
            } : null
          } as ForumTopic;
        });

        // Récupérer les counts pour chaque topic
        for (const topic of formattedData) {
          // Compter les réactions
          const { count: reactionsCount } = await supabase
            .from('forum_reactions')
            .select('*', { count: 'exact' })
            .eq('target_id', topic.id)
            .eq('target_type', 'topic');
          
          // Compter les réponses
          const { count: repliesCount } = await supabase
            .from('forum_replies')
            .select('*', { count: 'exact' })
            .eq('topic_id', topic.id);
          
          // Mettre à jour les compteurs
          topic._count.reactions = reactionsCount || 0;
          topic._count.replies = repliesCount || 0;
          
          // Vérifier si l'utilisateur a réagi
          if (user) {
            const { data: reactionData } = await supabase
              .from('forum_reactions')
              .select('id')
              .eq('target_id', topic.id)
              .eq('user_id', user.id)
              .eq('target_type', 'topic')
              .maybeSingle();
            
            topic.has_reacted = !!reactionData;
          }
        }

        // Collecter les IDs des utilisateurs (non anonymes) pour récupérer leurs profils
        const userIds = formattedData
          .filter(topic => !topic.is_anonymous)
          .map(topic => topic.user_id);

        // Récupérer les profils des utilisateurs si nécessaire
        if (userIds.length > 0) {
          const { data: userData, error: userError } = await supabase
            .from('profiles')
            .select('id, first_name, last_name, avatar_url')
            .in('id', userIds);

          if (!userError && userData) {
            // Créer un dictionnaire pour accès rapide
            const userMap: Record<string, {
              first_name: string | null;
              last_name: string | null;
              avatar_url: string | null;
            }> = {};
            userData.forEach(user => {
              userMap[user.id] = {
                first_name: user.first_name,
                last_name: user.last_name,
                avatar_url: user.avatar_url
              };
            });

            // Associer les données utilisateur aux topics
            formattedData.forEach(topic => {
              if (!topic.is_anonymous && userMap[topic.user_id]) {
                topic.user = userMap[topic.user_id];
              }
            });
          }
        }

        if (isRefresh) {
          setTopics(formattedData);
        } else {
          setTopics(prevTopics => [...prevTopics, ...formattedData]);
        }
      }
      
      // Marquer le chargement initial comme terminé après une courte période
      // pour éviter le flash de "Aucune discussion"
      setTimeout(() => {
        setInitialLoad(false);
      }, 300);
      
      return data || [];
    } catch (err) {
      console.error('Error loading topics:', err);
      setError('Erreur lors du chargement des discussions');
      setInitialLoad(false);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const removeReaction = async (topicId: string, userId: string) => {
    // Implementation of removeReaction function
  };

  const addReaction = async (topicId: string, reaction: string, userId: string) => {
    // Implementation of addReaction function
  };

  // Récupérer les notifications
  const fetchNotifications = async () => {
    if (!user) return;

    try {
      // Ici, on simule des données de notification pour l'exemple
      // Dans une application réelle, vous les récupéreriez de votre base de données
      const mockNotifications: ForumNotification[] = [
        {
          id: '1',
          type: 'new_reply',
          title: 'Nouvelle réponse',
          message: 'Sophie a répondu à votre discussion "Comment gérer le stress"',
          read: false,
          created_at: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
          topic_id: 'topic-123',
          user_id: 'user-456',
          user_name: 'Sophie Martin',
          user_avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
        },
        {
          id: '2',
          type: 'reaction',
          title: 'Nouvelle réaction',
          message: 'Thomas a aimé votre message dans "Techniques de méditation"',
          read: true,
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
          topic_id: 'topic-456',
          user_id: 'user-789',
          user_name: 'Thomas Dupont',
          user_avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
        },
        {
          id: '3',
          type: 'new_topic',
          title: 'Nouvelle discussion',
          message: 'Une nouvelle discussion a été créée dans la catégorie "Santé mentale"',
          read: false,
          created_at: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(), // 5 hours ago
        }
      ];

      setNotifications(mockNotifications);
      const unreadCount = mockNotifications.filter(n => !n.read).length;
      setNotificationCount(unreadCount);
      setHasNotifications(unreadCount > 0);
    } catch (error) {
      console.error('Erreur lors du chargement des notifications:', error);
    }
  };

  const renderTopicItem = ({ item }: { item: ForumTopic }) => {
    // Filtrer par recherche
    if (searchQuery && !item.title.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !item.content.toLowerCase().includes(searchQuery.toLowerCase())) {
      return null;
    }
    
    // Filtrer par "sans réponse" si ce tri est activé
    if (sortBy === 'unanswered' && item._count?.replies > 0) {
      return null;
    }

  return (
      <Animated.View
        style={[styles.topicCard, { backgroundColor: theme.colors.surface }]}
      >
        <TouchableOpacity
          style={styles.topicContainer}
          onPress={() => router.push({ 
            pathname: '/forum/topic',
            params: { id: item.id } 
          })}
        >
          <View style={styles.topicHeader}>
            <Image 
              source={{ 
                uri: item.is_anonymous 
                  ? 'https://via.placeholder.com/40' 
                  : (item.user?.avatar_url || 'https://via.placeholder.com/40')
              }} 
              style={styles.avatar} 
            />
            <View style={styles.topicInfo}>
              <Text 
                style={[styles.topicTitle, { color: theme.colors.text }]}
                numberOfLines={2}
              >
                {item.title}
              </Text>
              <View style={styles.topicMeta}>
                <Text style={[styles.authorName, { color: theme.colors.gray[500] }]}>
                  {item.is_anonymous ? 'Anonyme' : `${item.user?.first_name || ''} ${item.user?.last_name || ''}`}
                </Text>
                <Text style={[styles.dot, { color: theme.colors.gray[400] }]}>•</Text>
                <Text style={[styles.date, { color: theme.colors.gray[500] }]}>
                  {format(new Date(item.created_at), 'dd/MM/yyyy')}
                </Text>
              </View>
            </View>
            
            {item.is_pinned && (
              <View style={styles.pinnedBadge}>
                <Ionicons name="pin" size={14} color={theme.colors.primary} />
              </View>
            )}
          </View>
            
          <View style={styles.topicPreview}>
            <Text 
              style={[styles.topicContent, { color: theme.colors.text + 'CC' }]}
              numberOfLines={2}
            >
              {item.content}
            </Text>
          </View>
            
          <View style={styles.topicFooter}>
            <View style={styles.categoryBadge}>
              <Ionicons 
                name={(item.category?.icon as any) || 'folder-outline'} 
                size={14} 
                color={item.category?.color || theme.colors.primary} 
              />
              <Text 
                style={[
                  styles.categoryText, 
                  { color: item.category?.color || theme.colors.primary }
                ]}
              >
                {item.category?.name || 'Général'}
              </Text>
            </View>
              
            <View style={styles.actionButtons}>
              <View style={styles.actionButton}>
                <Ionicons name="chatbubble-outline" size={16} color={theme.colors.gray[500]} />
                <Text style={[styles.actionText, { color: theme.colors.gray[500] }]}>
                  {item._count?.replies || 0}
                </Text>
              </View>
                
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleReaction(item.id, item.has_reacted === true)}
              >
          <Ionicons
                  name={item.has_reacted ? "heart" : "heart-outline"} 
                  size={16} 
                  color={item.has_reacted ? "#FF3B30" : theme.colors.gray[500]} 
                />
                <Text 
                  style={[
                    styles.actionText, 
                    { color: item.has_reacted ? "#FF3B30" : theme.colors.gray[500] }
                  ]}
                >
                  {item._count?.reactions || 0}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  const applyFilter = (filter: 'newest' | 'popular' | 'unanswered') => {
    setSortBy(filter);
    setShowFilters(false);
  };

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Forum',
          headerTitleStyle: { 
            color: 'white',
            fontWeight: 'bold',
            fontSize: 20,
          },
          headerStyle: {
            backgroundColor: theme.colors.primary,
          },
          headerRight: () => (
            <View style={styles.headerRight}>
              <TouchableOpacity 
                style={styles.headerButton}
                onPress={navigateToNotifications}
              >
                <Ionicons name="notifications" size={24} color="white" />
                {hasNotifications && (
                  <View style={styles.notificationBadge}>
                    {notificationCount > 0 && (
                      <Text style={styles.notificationCount}>
                        {notificationCount <= 99 ? notificationCount : '99+'}
                      </Text>
                    )}
                  </View>
                )}
              </TouchableOpacity>
            </View>
          ),
          headerShadowVisible: false,
        }}
      />
      
      {/* Ajoutez le panneau de notifications */}
      {showNotificationsPanel && (
        <TouchableOpacity 
          style={StyleSheet.absoluteFill} 
          activeOpacity={1}
          onPress={hideNotificationsPanel}
        >
          <Animated.View 
            style={[
              StyleSheet.absoluteFill,
              styles.notificationsBackdrop,
              { opacity: notificationBackdropAnim }
            ]} 
          />
        </TouchableOpacity>
      )}
      
      <Animated.View 
        style={[
          styles.notificationsPanel,
          { 
            backgroundColor: theme.colors.surface,
            transform: [{ translateX: notificationsPanelAnim }]
          }
        ]}
      >
        <View style={styles.notificationsHeader}>
          <Text style={[styles.notificationsTitle, { color: theme.colors.text }]}>
            Notifications
          </Text>
          {notifications.some(n => !n.read) && (
            <TouchableOpacity onPress={markAllNotificationsAsRead}>
              <Text style={[styles.markReadText, { color: theme.colors.primary }]}>
                Tout marquer comme lu
              </Text>
            </TouchableOpacity>
          )}
        </View>
        
        {notifications.length === 0 ? (
          <View style={styles.emptyNotifications}>
            <Ionicons name="notifications-off-outline" size={50} color={theme.colors.gray[400]} />
            <Text style={[styles.emptyNotificationsText, { color: theme.colors.gray[500] }]}>
              Pas de notifications
            </Text>
          </View>
        ) : (
          <FlatList
            data={notifications}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <TouchableOpacity 
                style={[
                  styles.notificationItem,
                  !item.read && { backgroundColor: theme.colors.primaryLight + '30' }
                ]}
                onPress={() => handleNotificationPress(item)}
              >
                <View style={[styles.notificationIconContainer, { backgroundColor: theme.colors.primaryLight }]}>
                  <Ionicons 
                    name={getNotificationIcon(item.type)} 
                    size={20} 
                    color={theme.colors.primary} 
                  />
                </View>
                <View style={styles.notificationContent}>
                  <Text style={[styles.notificationTitle, { color: theme.colors.text }]}>
                    {item.title}
                  </Text>
                  <Text 
                    style={[styles.notificationMessage, { color: theme.colors.gray[600] }]}
                    numberOfLines={2}
                  >
                    {item.message}
                  </Text>
                  <Text style={[styles.notificationTime, { color: theme.colors.gray[500] }]}>
                    {formatNotificationTime(item.created_at)}
                  </Text>
                </View>
                {!item.read && (
                  <View style={[styles.unreadIndicator, { backgroundColor: theme.colors.primary }]} />
                )}
              </TouchableOpacity>
            )}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.notificationsList}
          />
        )}
      </Animated.View>
      
      <Animated.View 
        style={[
          styles.container, 
          { 
            backgroundColor: theme.colors.background,
            paddingBottom: insets.bottom,
            opacity: fadeAnim 
          }
        ]}
      >
        <View style={styles.searchBar}>
          <View style={[styles.searchInputContainer, { backgroundColor: theme.colors.surface }]}>
            <Ionicons name="search" size={20} color={theme.colors.gray[400]} />
            <TextInput
              style={[styles.searchInput, { color: theme.colors.text }]}
              placeholder="Rechercher dans le forum..."
              placeholderTextColor={theme.colors.gray[400]}
              value={searchQuery}
              onChangeText={handleSearch}
            />
            {searchQuery ? (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color={theme.colors.gray[400]} />
              </TouchableOpacity>
            ) : null}
      </View>

          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => setShowFilters(!showFilters)}
          >
            <Ionicons name="options" size={22} color="white" />
          </TouchableOpacity>
        </View>
        
        {showFilters && (
          <Animated.View 
            style={[styles.filtersContainer, { backgroundColor: theme.colors.surface }]}
          >
            <Text style={[styles.filtersTitle, { color: theme.colors.text }]}>
              Trier par
            </Text>
            <View style={styles.filterOptions}>
              <TouchableOpacity 
                style={[
                  styles.filterOption,
                  sortBy === 'newest' && { backgroundColor: theme.colors.primaryLight }
                ]}
                onPress={() => applyFilter('newest')}
              >
                <Ionicons 
                  name="time-outline" 
                  size={18} 
                  color={sortBy === 'newest' ? theme.colors.primary : theme.colors.text} 
                />
                <Text style={[
                  styles.filterOptionText,
                  { color: sortBy === 'newest' ? theme.colors.primary : theme.colors.text }
                ]}>
                  Plus récents
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[
                  styles.filterOption,
                  sortBy === 'popular' && { backgroundColor: theme.colors.primaryLight }
                ]}
                onPress={() => applyFilter('popular')}
              >
                <Ionicons 
                  name="flame-outline" 
                  size={18} 
                  color={sortBy === 'popular' ? theme.colors.primary : theme.colors.text} 
                />
                <Text style={[
                  styles.filterOptionText,
                  { color: sortBy === 'popular' ? theme.colors.primary : theme.colors.text }
                ]}>
                  Populaires
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[
                  styles.filterOption,
                  sortBy === 'unanswered' && { backgroundColor: theme.colors.primaryLight }
                ]}
                onPress={() => applyFilter('unanswered')}
              >
                <Ionicons 
                  name="help-circle-outline" 
                  size={18} 
                  color={sortBy === 'unanswered' ? theme.colors.primary : theme.colors.text} 
                />
                <Text style={[
                  styles.filterOptionText,
                  { color: sortBy === 'unanswered' ? theme.colors.primary : theme.colors.text }
                ]}>
                  Sans réponse
          </Text>
              </TouchableOpacity>
        </View>
          </Animated.View>
        )}

        <View style={styles.categoriesContainer}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesContent}
          >
            <TouchableOpacity
              style={[
                styles.categoryChip,
                selectedCategory === null && { backgroundColor: theme.colors.primary }
              ]}
              onPress={() => handleSelectCategory(null)}
            >
              <Text 
                style={[
                  styles.categoryChipText,
                  { color: selectedCategory === null ? 'white' : theme.colors.text }
                ]}
              >
                Tous
          </Text>
            </TouchableOpacity>

            {categories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={[
                  styles.categoryChip,
                  selectedCategory === category.id && { backgroundColor: category.color || theme.colors.primary }
                ]}
                onPress={() => handleSelectCategory(category.id)}
              >
                <Ionicons 
                  name={(category.icon as any) || 'folder-outline'} 
                  size={16} 
                  color={selectedCategory === category.id ? 'white' : category.color || theme.colors.primary} 
                />
                <Text 
                  style={[
                    styles.categoryChipText,
                    { color: selectedCategory === category.id ? 'white' : category.color || theme.colors.primary }
                  ]}
                >
                    {category.name}
                  </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
          </View>

        {error ? (
          <View style={styles.centerContent}>
            <Ionicons name="alert-circle" size={50} color={theme.colors.error} />
            <Text style={[styles.errorText, { color: theme.colors.error }]}>
              {error}
            </Text>
            <TouchableOpacity
              style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleRefresh}
            >
              <Text style={styles.retryButtonText}>
                Réessayer
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={topics}
            renderItem={renderTopicItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.topicList}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={[theme.colors.primary]}
                tintColor={theme.colors.primary}
              />
            }
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.5}
            ListEmptyComponent={
              loading || initialLoad ? (
                <View style={styles.centerContent}>
                  <ActivityIndicator size="large" color={theme.colors.primary} />
                  <Text style={[styles.loadingText, { color: theme.colors.gray[500] }]}>
                    Chargement des discussions...
                  </Text>
                </View>
              ) : topics.length === 0 ? (
                <View style={styles.centerContent}>
                  <Ionicons name="chatbubbles-outline" size={50} color={theme.colors.gray[400]} />
                  <Text style={[styles.emptyText, { color: theme.colors.gray[500] }]}>
                    Aucune discussion {selectedCategory ? 'dans cette catégorie' : ''}
                  </Text>
                  <TouchableOpacity
                    style={[styles.newTopicButtonSmall, { backgroundColor: theme.colors.primary }]}
                    onPress={() => {
                      if (user) {
                        router.push({
                          pathname: '/forum/new-topic',
                          params: { 
                            categoryId: selectedCategory || categories[0]?.id,
                            categoryName: selectedCategory 
                              ? categories.find(c => c.id === selectedCategory)?.name 
                              : categories[0]?.name
                          }
                        });
                      } else {
                        router.push('/login');
                      }
                    }}
                  >
                    <Ionicons name="add" size={18} color="white" style={styles.buttonIcon} />
                    <Text style={styles.buttonText}>Nouvelle discussion</Text>
                  </TouchableOpacity>
                </View>
              ) : null
            }
            ListFooterComponent={
              loading && !refreshing && !initialLoad ? (
                <View style={styles.footer}>
                  <ActivityIndicator size="small" color={theme.colors.primary} />
                </View>
              ) : null
            }
          />
        )}

        <TouchableOpacity
          style={[styles.newTopicButton, { backgroundColor: theme.colors.primary }]}
          onPress={() => {
            if (!user) {
              router.push('/');
              return;
            }
            
            router.push({
              pathname: '/forum/new-topic',
              params: { 
                categoryId: selectedCategory || categories[0]?.id,
                categoryName: selectedCategory 
                  ? categories.find(c => c.id === selectedCategory)?.name 
                  : categories[0]?.name
              }
            });
          }}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </Animated.View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 10,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 5,
    right: 5,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#FF3B30',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: 'white',
  },
  notificationCount: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 2,
  },
  notificationsBackdrop: {
    backgroundColor: '#000',
    zIndex: 1000,
  },
  notificationsPanel: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: '80%',
    maxWidth: 350,
    height: '100%',
    zIndex: 1001,
    borderTopLeftRadius: 20,
    borderBottomLeftRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: -2, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
    elevation: 25,
  },
  notificationsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  notificationsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  markReadText: {
    fontSize: 12,
    fontWeight: '500',
  },
  notificationsList: {
    paddingVertical: 8,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
    position: 'relative',
  },
  notificationIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  notificationContent: {
    flex: 1,
    marginRight: 10,
  },
  notificationTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  notificationMessage: {
    fontSize: 13,
    lineHeight: 18,
    marginBottom: 4,
  },
  notificationTime: {
    fontSize: 11,
  },
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    position: 'absolute',
    top: 12,
    right: 12,
  },
  emptyNotifications: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyNotificationsText: {
    marginTop: 10,
    fontSize: 16,
    textAlign: 'center',
  },
  searchBar: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
    gap: 10,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    padding: 10,
    borderRadius: 20,
    alignItems: 'center',
    gap: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  filtersContainer: {
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 16,
    marginBottom: 10,
  },
  filtersTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  filterOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    gap: 6,
  },
  filterOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  categoriesContainer: {
    paddingHorizontal: 16,
    marginBottom: 10,
  },
  categoriesContent: {
    paddingVertical: 4,
    gap: 8,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: '#F2F2F7',
    marginRight: 8,
    gap: 6,
  },
  categoryChipText: {
    fontSize: 14,
    fontWeight: '500',
  },
  topicList: {
    padding: 16,
    gap: 12,
  },
  topicCard: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 12,
  },
  topicContainer: {
    padding: 16,
  },
  topicHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  topicInfo: {
    flex: 1,
  },
  topicTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  topicMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  authorName: {
    fontSize: 14,
  },
  dot: {
    marginHorizontal: 6,
  },
  date: {
    fontSize: 14,
  },
  pinnedBadge: {
    padding: 4,
    borderRadius: 4,
  },
  topicPreview: {
    marginBottom: 12,
  },
  topicContent: {
    fontSize: 14,
    lineHeight: 20,
  },
  topicFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  actionText: {
    fontSize: 14,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    gap: 16,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
  retryButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
  footer: {
    padding: 16,
    alignItems: 'center',
  },
  newTopicButton: {
    position: 'absolute',
    bottom: 30,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    textAlign: 'center',
  },
  newTopicButtonSmall: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  buttonIcon: {
    marginRight: 6,
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 14,
  },
});