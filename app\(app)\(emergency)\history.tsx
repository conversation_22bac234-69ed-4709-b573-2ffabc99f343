import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Platform,
  RefreshControl,
  Linking
} from 'react-native';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { supabase } from '../../../lib/supabase';
import { Audio } from 'expo-av';
import { format, parseISO, isAfter, subDays } from 'date-fns';
import { fr } from 'date-fns/locale';

// Types pour les alertes
type AlertStatus = 'active' | 'ended' | 'cancelled';

type AlertEvent = {
  id: string;
  user_id: string;
  started_at: string;
  ended_at?: string;
  initial_location: {
    latitude: number;
    longitude: number;
    accuracy: number;
    timestamp: string;
  };
  status: AlertStatus;
  metadata: any;
  audio_recordings?: AudioRecording[];
  location_updates?: LocationUpdate[];
};

type AudioRecording = {
  id: string;
  sos_event_id: string;
  file_url?: string;
  file_path?: string;
  duration?: number;
  recorded_at?: string; // Utilisons recorded_at au lieu de created_at
  uploaded_at?: string;
  metadata?: any;
  timestamp?: string; // Colonne alternative pour la date
};

type LocationUpdate = {
  id: string;
  sos_event_id: string;
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: string;
  metadata: any;
};

export default function AlertHistoryScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const router = useRouter();

  const [alerts, setAlerts] = useState<AlertEvent[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [expandedAlertId, setExpandedAlertId] = useState<string | null>(null);
  const [playingAudioId, setPlayingAudioId] = useState<string | null>(null);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Charger les alertes au démarrage
  useEffect(() => {
    if (user) {
      loadAlerts();
    }

    // Nettoyer le son lors du démontage
    return () => {
      if (sound) {
        sound.unloadAsync();
      }
    };
  }, [user]);

  // Fonction pour charger les alertes
  const loadAlerts = async () => {
    try {
      setLoading(true);
      setError(null);

      // Calculer la date limite (30 jours en arrière)
      const thirtyDaysAgo = subDays(new Date(), 30).toISOString();

      // Récupérer les alertes de l'utilisateur des 30 derniers jours
      const { data: alertsData, error: alertsError } = await supabase
        .from('sos_events')
        .select('*')
        .eq('user_id', user?.id)
        .gte('started_at', thirtyDaysAgo)
        .order('started_at', { ascending: false });

      if (alertsError) {
        throw alertsError;
      }

      // Récupérer les enregistrements audio pour chaque alerte
      const alertsWithDetails = await Promise.all(alertsData.map(async (alert) => {
        // Récupérer les enregistrements audio
        const { data: audioData, error: audioError } = await supabase
          .from('sos_audio_recordings')
          .select('*')
          .eq('sos_event_id', alert.id);

        if (audioError) {
          console.error('Erreur lors de la récupération des enregistrements audio:', audioError);
        }

        // Récupérer les mises à jour de localisation
        const { data: locationData, error: locationError } = await supabase
          .from('sos_location_updates')
          .select('*')
          .eq('sos_event_id', alert.id)
          .order('timestamp', { ascending: true });

        if (locationError) {
          console.error('Erreur lors de la récupération des mises à jour de localisation:', locationError);
        }

        // Ajouter des logs pour comprendre la structure des enregistrements audio
        if (audioData && audioData.length > 0) {
          console.log('Structure d\'un enregistrement audio:',
            Object.keys(audioData[0]).join(', '));
        }

        return {
          ...alert,
          audio_recordings: audioData || [],
          location_updates: locationData || []
        };
      }));

      setAlerts(alertsWithDetails);
    } catch (err) {
      console.error('Erreur lors du chargement des alertes:', err);
      setError('Impossible de charger l\'historique des alertes');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fonction pour rafraîchir les alertes
  const handleRefresh = () => {
    setRefreshing(true);
    loadAlerts();
  };

  // Fonction pour formater la date
  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'dd MMMM yyyy à HH:mm', { locale: fr });
    } catch (err) {
      return 'Date inconnue';
    }
  };

  // Fonction pour calculer la durée d'une alerte
  const calculateDuration = (startDate: string, endDate?: string) => {
    try {
      const start = parseISO(startDate);
      const end = endDate ? parseISO(endDate) : new Date();

      const diffInMs = end.getTime() - start.getTime();
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

      if (diffInMinutes < 60) {
        return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''}`;
      } else {
        const hours = Math.floor(diffInMinutes / 60);
        const minutes = diffInMinutes % 60;
        return `${hours} heure${hours > 1 ? 's' : ''} ${minutes} minute${minutes > 1 ? 's' : ''}`;
      }
    } catch (err) {
      return 'Durée inconnue';
    }
  };

  // Fonction pour jouer un enregistrement audio
  const playAudio = async (audioUrl: string, audioId: string) => {
    try {
      // Vérifier que l'URL est valide
      if (!audioUrl) {
        Alert.alert('Erreur', 'URL de l\'enregistrement audio non disponible');
        return;
      }

      // Arrêter l'audio en cours de lecture
      if (sound) {
        try {
          const status = await sound.getStatusAsync();
          if (status.isLoaded) {
            if (status.isPlaying) {
              await sound.stopAsync();
            }
            await sound.unloadAsync();
          }
        } catch (stopError) {
          console.error('Erreur lors de l\'arrêt de l\'audio précédent:', stopError);
        }
        setSound(null);
      }

      // Si on clique sur l'audio déjà en lecture, arrêter la lecture
      if (playingAudioId === audioId) {
        setPlayingAudioId(null);
        return;
      }

      // Indiquer que le chargement est en cours
      setPlayingAudioId('loading');

      // Vérifier les permissions audio
      const { granted } = await Audio.requestPermissionsAsync();
      if (!granted) {
        Alert.alert('Permission refusée', 'L\'application n\'a pas la permission d\'accéder à l\'audio');
        setPlayingAudioId(null);
        return;
      }

      // Configurer l'audio
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });

      // Charger et jouer le nouvel audio
      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: audioUrl },
        { shouldPlay: true },
        (status) => {
          if (status.didJustFinish) {
            setPlayingAudioId(null);
          } else if (status.error) {
            console.error('Erreur de lecture audio:', status.error);
            Alert.alert('Erreur', 'Une erreur est survenue pendant la lecture');
            setPlayingAudioId(null);
          }
        }
      );

      setSound(newSound);
      setPlayingAudioId(audioId);
    } catch (err) {
      console.error('Erreur lors de la lecture audio:', err);
      Alert.alert('Erreur', 'Impossible de lire l\'enregistrement audio. Vérifiez votre connexion internet.');
      setPlayingAudioId(null);
    }
  };

  // Fonction pour ouvrir Google Maps avec les coordonnées
  const openLocation = (latitude: number, longitude: number) => {
    try {
      const url = `https://www.google.com/maps?q=${latitude},${longitude}`;

      // Demander confirmation avant d'ouvrir le navigateur
      Alert.alert(
        'Ouvrir la carte',
        `Voulez-vous ouvrir Google Maps avec les coordonnées: ${latitude.toFixed(6)}, ${longitude.toFixed(6)} ?`,
        [
          { text: 'Annuler', style: 'cancel' },
          {
            text: 'Ouvrir',
            onPress: async () => {
              try {
                // Vérifier si le lien peut être ouvert
                const canOpen = await Linking.canOpenURL(url);

                if (canOpen) {
                  await Linking.openURL(url);
                } else {
                  Alert.alert('Erreur', 'Impossible d\'ouvrir Google Maps. Vérifiez que vous avez un navigateur installé.');
                }
              } catch (error) {
                console.error('Erreur lors de l\'ouverture du lien:', error);
                Alert.alert('Erreur', 'Une erreur est survenue lors de l\'ouverture de Google Maps.');
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error('Erreur lors de la préparation du lien:', error);
      Alert.alert('Erreur', 'Une erreur est survenue lors de la préparation du lien Google Maps.');
    }
  };

  // Rendu d'un enregistrement audio
  const renderAudioItem = ({ item }: { item: AudioRecording }) => {
    const isPlaying = playingAudioId === item.id;

    // Déterminer la date d'enregistrement en utilisant les champs disponibles
    const dateField = item.recorded_at || item.timestamp || item.uploaded_at ||
                     (item.metadata && item.metadata.recorded_at) ||
                     (item.metadata && item.metadata.timestamp);

    // Formater la date ou utiliser une valeur par défaut
    const recordingDate = dateField ? formatDate(dateField) : 'Date inconnue';

    // Déterminer la durée
    const durationInSeconds = item.duration ||
                             (item.metadata && item.metadata.duration) || 0;

    const durationText = `${Math.floor(durationInSeconds / 60)}:${(durationInSeconds % 60).toString().padStart(2, '0')}`;

    // Déterminer l'URL du fichier audio
    const audioUrl = item.file_url || item.file_path ||
                    (item.metadata && item.metadata.file_url) ||
                    (item.metadata && item.metadata.file_path);

    const canPlay = !!audioUrl;

    return (
      <TouchableOpacity
        style={[
          styles.audioItem,
          !canPlay && { opacity: 0.6 }
        ]}
        onPress={() => canPlay ? playAudio(audioUrl, item.id) : Alert.alert('Erreur', 'Fichier audio non disponible')}
        disabled={!canPlay}
      >
        <View style={styles.audioIconContainer}>
          <Ionicons
            name={isPlaying ? "pause-circle" : canPlay ? "play-circle" : "alert-circle"}
            size={36}
            color={canPlay ? theme.colors.primary : theme.colors.gray[400]}
          />
        </View>
        <View style={styles.audioInfo}>
          <Text style={styles.audioTitle}>
            {dateField ? `Enregistrement du ${recordingDate}` : 'Enregistrement audio'}
          </Text>
          <Text style={styles.audioDuration}>
            {canPlay ? `Durée: ${durationText}` : 'Fichier non disponible'}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  // Rendu d'une mise à jour de localisation
  const renderLocationItem = ({ item }: { item: LocationUpdate }) => {
    const locationTime = formatDate(item.timestamp);

    return (
      <TouchableOpacity
        style={styles.locationItem}
        onPress={() => openLocation(item.latitude, item.longitude)}
      >
        <Ionicons name="location" size={24} color={theme.colors.primary} />
        <View style={styles.locationInfo}>
          <Text style={styles.locationTime}>{locationTime}</Text>
          <Text style={styles.locationCoords}>
            Lat: {item.latitude.toFixed(6)}, Long: {item.longitude.toFixed(6)}
          </Text>
          {item.accuracy && (
            <Text style={styles.locationAccuracy}>
              Précision: ±{item.accuracy.toFixed(2)}m
            </Text>
          )}
        </View>
        <Ionicons name="open-outline" size={20} color={theme.colors.gray[500]} />
      </TouchableOpacity>
    );
  };

  // Rendu d'une alerte
  const renderAlertItem = ({ item }: { item: AlertEvent }) => {
    const isExpanded = expandedAlertId === item.id;
    const startDate = formatDate(item.started_at);
    const duration = calculateDuration(item.started_at, item.ended_at);
    const statusColor =
      item.status === 'active' ? '#FF3B30' :
      item.status === 'ended' ? '#34C759' :
      '#FF9500'; // cancelled

    const statusText =
      item.status === 'active' ? 'Active' :
      item.status === 'ended' ? 'Terminée' :
      'Annulée';

    const hasAudio = item.audio_recordings && item.audio_recordings.length > 0;
    const hasLocations = item.location_updates && item.location_updates.length > 0;

    return (
      <View style={[styles.alertCard, { backgroundColor: theme.colors.card }]}>
        <TouchableOpacity
          style={styles.alertHeader}
          onPress={() => setExpandedAlertId(isExpanded ? null : item.id)}
        >
          <View style={styles.alertTitleContainer}>
            <View style={[styles.statusIndicator, { backgroundColor: statusColor }]} />
            <Text style={[styles.alertTitle, { color: theme.colors.text }]}>
              Alerte du {startDate}
            </Text>
          </View>
          <View style={styles.alertHeaderRight}>
            <Text style={[styles.alertStatus, { color: statusColor }]}>
              {statusText}
            </Text>
            <Ionicons
              name={isExpanded ? "chevron-up" : "chevron-down"}
              size={20}
              color={theme.colors.gray[500]}
            />
          </View>
        </TouchableOpacity>

        {isExpanded && (
          <View style={styles.alertDetails}>
            <View style={styles.alertInfoRow}>
              <Text style={styles.alertInfoLabel}>Début:</Text>
              <Text style={styles.alertInfoValue}>{startDate}</Text>
            </View>

            {item.ended_at && (
              <View style={styles.alertInfoRow}>
                <Text style={styles.alertInfoLabel}>Fin:</Text>
                <Text style={styles.alertInfoValue}>{formatDate(item.ended_at)}</Text>
              </View>
            )}

            <View style={styles.alertInfoRow}>
              <Text style={styles.alertInfoLabel}>Durée:</Text>
              <Text style={styles.alertInfoValue}>{duration}</Text>
            </View>

            {item.initial_location && (
              <View style={styles.alertInfoRow}>
                <Text style={styles.alertInfoLabel}>Position initiale:</Text>
                <TouchableOpacity
                  onPress={() => openLocation(
                    item.initial_location.latitude,
                    item.initial_location.longitude
                  )}
                >
                  <Text style={[styles.alertInfoValue, styles.locationLink]}>
                    Voir sur la carte
                  </Text>
                </TouchableOpacity>
              </View>
            )}

            {hasAudio && (
              <View style={styles.sectionContainer}>
                <Text style={styles.sectionTitle}>Enregistrements audio ({item.audio_recordings.length})</Text>
                <FlatList
                  data={item.audio_recordings}
                  renderItem={renderAudioItem}
                  keyExtractor={(audioItem) => audioItem.id}
                  scrollEnabled={false}
                />
              </View>
            )}

            {hasLocations && (
              <View style={styles.sectionContainer}>
                <Text style={styles.sectionTitle}>Positions ({item.location_updates.length})</Text>
                <FlatList
                  data={item.location_updates.slice(0, 5)} // Limiter à 5 positions pour éviter une liste trop longue
                  renderItem={renderLocationItem}
                  keyExtractor={(locationItem) => locationItem.id}
                  scrollEnabled={false}
                />
                {item.location_updates.length > 5 && (
                  <TouchableOpacity style={styles.viewMoreButton}>
                    <Text style={styles.viewMoreText}>
                      Voir les {item.location_updates.length - 5} positions supplémentaires
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            )}
          </View>
        )}
      </View>
    );
  };

  // Rendu principal
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Historique des alertes
        </Text>
      </View>

      {loading && !refreshing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Chargement de l'historique...
          </Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle" size={48} color={theme.colors.error} />
          <Text style={[styles.errorText, { color: theme.colors.error }]}>
            {error}
          </Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
            onPress={loadAlerts}
          >
            <Text style={styles.retryButtonText}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : alerts.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="information-circle" size={48} color={theme.colors.gray[500]} />
          <Text style={[styles.emptyText, { color: theme.colors.text }]}>
            Aucune alerte dans les 30 derniers jours
          </Text>
        </View>
      ) : (
        <FlatList
          data={alerts}
          renderItem={renderAlertItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    paddingTop: Platform.OS === 'ios' ? 50 : 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  listContainer: {
    padding: 16,
  },
  alertCard: {
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  alertHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  alertTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8,
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  alertHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  alertStatus: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 8,
  },
  alertDetails: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  alertInfoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  alertInfoLabel: {
    width: 100,
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
  },
  alertInfoValue: {
    flex: 1,
    fontSize: 14,
    color: '#1f2937',
  },
  locationLink: {
    color: '#3b82f6',
    textDecorationLine: 'underline',
  },
  sectionContainer: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#1f2937',
  },
  audioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    marginBottom: 8,
  },
  audioIconContainer: {
    marginRight: 12,
  },
  audioInfo: {
    flex: 1,
  },
  audioTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
  },
  audioDuration: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 4,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    marginBottom: 8,
  },
  locationInfo: {
    flex: 1,
    marginLeft: 12,
  },
  locationTime: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
  },
  locationCoords: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
  },
  locationAccuracy: {
    fontSize: 12,
    color: '#6b7280',
  },
  viewMoreButton: {
    padding: 12,
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
  },
  viewMoreText: {
    fontSize: 14,
    color: '#3b82f6',
    fontWeight: '500',
  },
});
