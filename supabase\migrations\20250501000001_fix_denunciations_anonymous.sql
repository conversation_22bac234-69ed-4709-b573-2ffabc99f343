/*
  # Fix anonymous denunciations RLS policies

  1. Changes
    - Update RLS policies for denunciations table to properly handle anonymous submissions
    - Ensure proper access control for denunciations
    
  2. Security
    - Maintain existing RLS policies
    - Fix policy for anonymous denunciations
*/

-- Drop existing policies
DROP POLICY IF EXISTS "Users can create denunciations" ON denunciations;
DROP POLICY IF EXISTS "Users can view all their denunciations" ON denunciations;
DROP POLICY IF EXISTS "Users can view their own denunciations" ON denunciations;

-- Create new policy that allows anonymous submissions
CREATE POLICY "Users can create denunciations"
    ON denunciations
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

-- Create policy for viewing denunciations
-- This policy allows users to view their own denunciations, including anonymous ones
CREATE POLICY "Users can view their own denunciations"
    ON denunciations
    FOR SELECT
    TO authenticated
    USING (
        -- Either the user_id matches the authenticated user
        (user_id = auth.uid())
        OR 
        -- Or the denunciation is anonymous and created by the current user
        -- This requires client-side filtering since we can't track anonymous submissions server-side
        (anonymous = true AND user_id IS NULL)
    );

-- Modify the user_id column to allow NULL values (for anonymous submissions)
ALTER TABLE denunciations ALTER COLUMN user_id DROP NOT NULL;
