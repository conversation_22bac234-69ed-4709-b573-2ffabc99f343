-- Create webhook access logs table
CREATE TABLE IF NOT EXISTS webhook_access_logs (
    id SERIAL PRIMARY KEY,
    webhook_name VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    user_id UUID REFERENCES auth.users(id),
    timestamp TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    ip_address TEXT,
    user_agent TEXT
);

-- Enable RLS
ALTER TABLE webhook_access_logs ENABLE ROW LEVEL SECURITY;

-- Create policy for webhook access logs
CREATE POLICY "Allow read access for admins on webhook logs"
    ON webhook_access_logs
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Add encryption for webhook URLs
ALTER TABLE webhook_urls
ADD COLUMN IF NOT EXISTS url_encrypted BYTEA,
ADD COLUMN IF NOT EXISTS backup_url_encrypted BYTEA;

-- Create function to encrypt webhook URLs
CREATE OR REPLACE FUNCTION encrypt_webhook_url(url TEXT)
RETURNS BYTEA AS $$
BEGIN
    -- Utiliser pgcrypto pour le chiffrement
    RETURN pgp_sym_encrypt(
        url,
        current_setting('app.webhook_encryption_key')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to decrypt webhook URLs
CREATE OR REPLACE FUNCTION decrypt_webhook_url(encrypted_url BYTEA)
RETURNS TEXT AS $$
BEGIN
    RETURN pgp_sym_decrypt(
        encrypted_url,
        current_setting('app.webhook_encryption_key')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically encrypt URLs
CREATE OR REPLACE FUNCTION encrypt_webhook_urls_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR NEW.url <> OLD.url THEN
        NEW.url_encrypted = encrypt_webhook_url(NEW.url);
    END IF;
    
    IF TG_OP = 'INSERT' OR COALESCE(NEW.backup_url, '') <> COALESCE(OLD.backup_url, '') THEN
        NEW.backup_url_encrypted = 
            CASE WHEN NEW.backup_url IS NOT NULL 
                THEN encrypt_webhook_url(NEW.backup_url)
                ELSE NULL
            END;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE TRIGGER encrypt_webhook_urls
    BEFORE INSERT OR UPDATE ON webhook_urls
    FOR EACH ROW
    EXECUTE FUNCTION encrypt_webhook_urls_trigger();

-- Add validation check for URLs
ALTER TABLE webhook_urls
ADD CONSTRAINT valid_url_format
    CHECK (
        url ~ '^https://'
        AND (backup_url IS NULL OR backup_url ~ '^https://')
    );

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_webhook_access_logs_webhook_name 
    ON webhook_access_logs(webhook_name);
CREATE INDEX IF NOT EXISTS idx_webhook_access_logs_timestamp 
    ON webhook_access_logs(timestamp);

-- Create view for webhook monitoring
CREATE OR REPLACE VIEW webhook_monitoring AS
SELECT 
    w.name,
    w.is_active,
    w.last_check_timestamp,
    w.error_count,
    COUNT(l.id) as access_count_24h,
    MAX(l.timestamp) as last_access
FROM webhook_urls w
LEFT JOIN webhook_access_logs l 
    ON w.name = l.webhook_name 
    AND l.timestamp > NOW() - INTERVAL '24 hours'
GROUP BY w.id, w.name;