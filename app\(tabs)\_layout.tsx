// @ts-nocheck
import React, { useEffect } from 'react';
import { Tabs } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../context/theme';
import ProfileHeader from '../components/ProfileHeader';
import { View, Dimensions, Platform, StyleSheet, StatusBar } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Utiliser une fonction pour créer les icônes afin d'éviter les problèmes de typage
function createTabBarIcon(name: string) {
  return function TabBarIcon({ color, size }: { color: string; size: number }) {
    // @ts-ignore - Ignorer les erreurs de typage pour Ionicons
    return <ion-icon name={name} size={size} color={color} />;
  };
}

export default function TabLayout() {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const { width, height } = Dimensions.get('window');
  const isLargeScreen = width >= 768; // Détection des tablettes ou grands écrans

  // Calculer la hauteur de la barre d'onglets en fonction de l'appareil
  const tabBarHeight = Platform.OS === 'ios' ? 49 + insets.bottom : 56;

  // Effet pour ajuster le statut de la barre d'état
  useEffect(() => {
    StatusBar.setBarStyle(theme.dark ? 'light-content' : 'dark-content');
    if (Platform.OS === 'android') {
      StatusBar.setBackgroundColor('transparent');
      StatusBar.setTranslucent(true);
    }
  }, [theme.dark]);

  return (
    <View style={styles.container}>
      <ProfileHeader compact={true} />
      <Tabs
        screenOptions={{
          headerShown: false, // Cacher l'en-tête par défaut car nous utilisons ProfileHeader
          headerStyle: {
            backgroundColor: theme.colors.background,
            height: 0, // Réduire la hauteur à 0 puisque nous utilisons ProfileHeader
            elevation: 0, // Supprimer l'ombre sur Android
            shadowOpacity: 0, // Supprimer l'ombre sur iOS
          },
          headerTintColor: theme.colors.primary,
          headerTitleStyle: {
            fontWeight: 'bold',
          },
          headerBackTitleVisible: false,
          headerStatusBarHeight: 0,
          tabBarStyle: {
            backgroundColor: theme.colors.background,
            borderTopColor: theme.colors.gray[200],
            borderTopWidth: 0.5,
            height: tabBarHeight,
            paddingBottom: insets.bottom > 0 ? insets.bottom : 8,
            paddingTop: 8,
            elevation: 8, // Ombre plus prononcée sur Android
            shadowColor: theme.dark ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.3)',
            shadowOffset: { width: 0, height: -2 },
            shadowOpacity: 0.1,
            shadowRadius: 3,
          },
          tabBarActiveTintColor: theme.colors.primary,
          tabBarInactiveTintColor: theme.colors.gray[400],
          tabBarLabelStyle: {
            fontSize: 12,
            fontWeight: '500',
            paddingBottom: Platform.OS === 'ios' ? 0 : 4,
          },
          tabBarIconStyle: {
            marginTop: 4,
          },
          // Adapter la mise en page pour les grands écrans
          tabBarItemStyle: isLargeScreen ? {
            paddingVertical: 8,
          } : undefined,
        }}>
        <Tabs.Screen
          name="index"
          options={{
            title: 'Accueil',
            tabBarIcon: ({ color, size, focused }) => (
              <View style={styles.tabIconContainer}>
                <Ionicons
                  name={focused ? "home" : "home-outline"}
                  size={size}
                  color={color}
                />
              </View>
            ),
          }}
        />
        <Tabs.Screen
          name="appointments"
          options={{
            title: 'Rendez-vous',
            tabBarIcon: ({ color, size, focused }) => (
              <View style={styles.tabIconContainer}>
                <Ionicons
                  name={focused ? "calendar" : "calendar-outline"}
                  size={size}
                  color={color}
                />
                {/* Badge pour indiquer des rendez-vous à venir (exemple) */}
                <View style={[styles.badge, { backgroundColor: theme.colors.primary }]}>
                  <View style={styles.badgeDot} />
                </View>
              </View>
            ),
          }}
        />
        <Tabs.Screen
          name="ngo"
          options={{
            title: 'ONGs',
            tabBarIcon: ({ color, size, focused }) => (
              <View style={styles.tabIconContainer}>
                <Ionicons
                  name={focused ? "people" : "people-outline"}
                  size={size}
                  color={color}
                />
              </View>
            ),
          }}
        />
        <Tabs.Screen
          name="forum"
          options={{
            title: 'Forum',
            tabBarIcon: ({ color, size, focused }) => (
              <View style={styles.tabIconContainer}>
                <Ionicons
                  name={focused ? "chatbubbles" : "chatbubbles-outline"}
                  size={size}
                  color={color}
                />
                {/* Badge pour indiquer de nouveaux messages (exemple) */}
                <View style={[styles.badge, { backgroundColor: '#FF9800' }]}>
                  <View style={styles.badgeDot} />
                </View>
              </View>
            ),
          }}
        />

        <Tabs.Screen
          name="profile"
          options={{
            title: 'Profil',
            tabBarIcon: ({ color, size, focused }) => (
              <View style={styles.tabIconContainer}>
                <Ionicons
                  name={focused ? "person" : "person-outline"}
                  size={size}
                  color={color}
                />
              </View>
            ),
          }}
        />
      </Tabs>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  tabIconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    width: 30,
    height: 30,
  },
  badge: {
    position: 'absolute',
    top: -2,
    right: -6,
    width: 12,
    height: 12,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  badgeDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'white',
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
});