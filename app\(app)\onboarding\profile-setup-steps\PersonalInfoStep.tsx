import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ScrollView, Platform, Modal, KeyboardAvoidingView, SafeAreaView } from 'react-native';
import { useProfileStore, Gender } from './store';
import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';

const genderOptions = [
  { label: 'Homme', value: 'homme' as Gender },
  { label: 'Femme', value: 'femme' as Gender },
  { label: 'Préfère ne pas préciser', value: 'non_precise' as Gender },
];

const languageOptions = [
  { label: 'Français', value: 'fr' },
  { label: 'Anglais', value: 'en' },

];

export function PersonalInfoStep() {
  const { profile, updateProfile, setCurrentStep } = useProfileStore();
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [birthDate, setBirthDate] = useState<Date | null>(
    profile.dateOfBirth ? new Date(profile.dateOfBirth) : null
  );

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!profile.firstName.trim()) {
      newErrors.firstName = 'Le prénom est requis';
    }

    if (!profile.lastName.trim()) {
      newErrors.lastName = 'Le nom est requis';
    }

    if (!profile.age) {
      newErrors.age = 'L\'âge est requis';
    } else if (profile.age < 0 || profile.age > 120) {
      newErrors.age = 'Veuillez entrer un âge valide';
    }

    if (!profile.gender) {
      newErrors.gender = 'Le genre est requis';
    }

    if (profile.phoneNumber && !/^\+?[0-9]{10,15}$/.test(profile.phoneNumber)) {
      newErrors.phoneNumber = 'Numéro de téléphone invalide';
    }

    if (!profile.dateOfBirth) {
      newErrors.dateOfBirth = 'La date de naissance est requise';
    }

    if (!profile.address) {
      newErrors.address = 'L\'adresse est requise';
    }

    if (!profile.languagePreference) {
      newErrors.languagePreference = 'La préférence linguistique est requise';
    }

    if (profile.emergencyContact && !/^\+?[0-9]{10,15}$/.test(profile.emergencyContact)) {
      newErrors.emergencyContact = 'Contact d\'urgence invalide';
    }

    if (!profile.professionalTitle) {
      newErrors.professionalTitle = 'Le titre professionnel est requis';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      setCurrentStep(2);
    }
  };

  const onDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setBirthDate(selectedDate);

      // Format de date
      const day = selectedDate.getDate().toString().padStart(2, '0');
      const month = (selectedDate.getMonth() + 1).toString().padStart(2, '0');
      const year = selectedDate.getFullYear();

      updateProfile({
        dateOfBirth: `${day}/${month}/${year}`,
        age: new Date().getFullYear() - year
      });
    }
  };

  const formatDisplayDate = (date: string | undefined) => {
    if (!date) return '';
    return date;
  };

  return (
    <View style={styles.container}>
      <View style={styles.cardHeader}>
        <Text style={styles.cardTitle}>Informations personnelles</Text>
        <Text style={styles.cardDescription}>
          Veuillez fournir vos informations personnelles pour commencer
        </Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={true}
        scrollEnabled={true}
        nestedScrollEnabled={true}
        alwaysBounceVertical={true}
      >
        <View style={styles.formGroup}>
          <Text style={styles.label}>Prénom</Text>
          <View style={styles.inputContainer}>
            <Ionicons name="person-outline" size={20} color="#666" style={styles.inputIcon} />
            <TextInput
              style={[styles.input, errors.firstName ? styles.inputError : null]}
              placeholder="Entrez votre prénom"
              value={profile.firstName}
              onChangeText={(value) => updateProfile({ firstName: value })}
              placeholderTextColor="#999"
            />
          </View>
          {errors.firstName ? <Text style={styles.errorText}>{errors.firstName}</Text> : null}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Nom</Text>
          <View style={styles.inputContainer}>
            <Ionicons name="person-outline" size={20} color="#666" style={styles.inputIcon} />
            <TextInput
              style={[styles.input, errors.lastName ? styles.inputError : null]}
              placeholder="Entrez votre nom"
              value={profile.lastName}
              onChangeText={(value) => updateProfile({ lastName: value })}
              placeholderTextColor="#999"
            />
          </View>
          {errors.lastName ? <Text style={styles.errorText}>{errors.lastName}</Text> : null}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Date de naissance</Text>
          <TouchableOpacity
            style={[styles.datePickerButton, errors.dateOfBirth ? styles.inputError : null]}
            onPress={() => setShowDatePicker(true)}
          >
            <Ionicons name="calendar-outline" size={20} color="#666" style={styles.inputIcon} />
            <Text style={[styles.datePickerText, !profile.dateOfBirth && styles.placeholderText]}>
              {profile.dateOfBirth ? formatDisplayDate(profile.dateOfBirth) : "Sélectionner votre date de naissance"}
            </Text>
          </TouchableOpacity>
          {errors.dateOfBirth ? <Text style={styles.errorText}>{errors.dateOfBirth}</Text> : null}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Âge</Text>
          <View style={styles.inputContainer}>
            <Ionicons name="time-outline" size={20} color="#666" style={styles.inputIcon} />
            <TextInput
              style={[styles.input, errors.age ? styles.inputError : null]}
              placeholder="Votre âge"
              value={profile.age?.toString() || ''}
              onChangeText={(value) => updateProfile({ age: value ? parseInt(value, 10) : null })}
              keyboardType="numeric"
              placeholderTextColor="#999"
              editable={false} // Disable direct age editing since it's calculated from date of birth
            />
          </View>
          {errors.age ? <Text style={styles.errorText}>{errors.age}</Text> : null}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Genre</Text>
          <View style={[styles.genderContainer, errors.gender ? {borderColor: '#ff3b30'} : null]}>
            {genderOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.genderOption,
                  profile.gender === option.value && styles.genderOptionSelected
                ]}
                onPress={() => updateProfile({ gender: option.value })}
              >
                <Text style={[
                  styles.genderOptionText,
                  profile.gender === option.value && styles.genderOptionTextSelected
                ]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          {errors.gender ? <Text style={styles.errorText}>{errors.gender}</Text> : null}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Numéro de téléphone</Text>
          <View style={styles.inputContainer}>
            <Ionicons name="call-outline" size={20} color="#666" style={styles.inputIcon} />
            <TextInput
              style={[styles.input, errors.phoneNumber ? styles.inputError : null]}
              placeholder="Entrez votre numéro de téléphone"
              value={profile.phoneNumber}
              onChangeText={(value) => updateProfile({ phoneNumber: value })}
              keyboardType="phone-pad"
              placeholderTextColor="#999"
            />
          </View>
          {errors.phoneNumber ? <Text style={styles.errorText}>{errors.phoneNumber}</Text> : null}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Adresse</Text>
          <View style={styles.inputContainer}>
            <Ionicons name="home-outline" size={20} color="#666" style={styles.inputIcon} />
            <TextInput
              style={[styles.input, errors.address ? styles.inputError : null]}
              placeholder="Entrez votre adresse"
              value={profile.address}
              onChangeText={(value) => updateProfile({ address: value })}
              placeholderTextColor="#999"
            />
          </View>
          {errors.address ? <Text style={styles.errorText}>{errors.address}</Text> : null}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Contact d'urgence</Text>
          <View style={styles.inputContainer}>
            <Ionicons name="alert-circle-outline" size={20} color="#666" style={styles.inputIcon} />
            <TextInput
              style={[styles.input, errors.emergencyContact ? styles.inputError : null]}
              placeholder="Numéro d'une personne à contacter en cas d'urgence"
              value={profile.emergencyContact}
              onChangeText={(value) => updateProfile({ emergencyContact: value })}
              keyboardType="phone-pad"
              placeholderTextColor="#999"
            />
          </View>
          {errors.emergencyContact ? <Text style={styles.errorText}>{errors.emergencyContact}</Text> : null}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Langue préférée</Text>
          <View style={[styles.genderContainer, errors.languagePreference ? {borderColor: '#ff3b30'} : null]}>
            {languageOptions.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.genderOption,
                  profile.languagePreference === option.value && styles.genderOptionSelected
                ]}
                onPress={() => updateProfile({ languagePreference: option.value })}
              >
                <Text style={[
                  styles.genderOptionText,
                  profile.languagePreference === option.value && styles.genderOptionTextSelected
                ]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          {errors.languagePreference ? <Text style={styles.errorText}>{errors.languagePreference}</Text> : null}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Titre professionnel</Text>
          <View style={styles.inputContainer}>
            <Ionicons name="briefcase-outline" size={20} color="#666" style={styles.inputIcon} />
            <TextInput
              style={[styles.input, errors.professionalTitle ? styles.inputError : null]}
              placeholder="Ex: Médecin, Infirmier, Assistant social..."
              value={profile.professionalTitle}
              onChangeText={(value) => updateProfile({ professionalTitle: value })}
              placeholderTextColor="#999"
            />
          </View>
          {errors.professionalTitle ? <Text style={styles.errorText}>{errors.professionalTitle}</Text> : null}
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Biographie professionnelle</Text>
          <View style={[styles.inputContainer, { height: 100, alignItems: 'flex-start', paddingTop: 10 }]}>
            <Ionicons name="document-text-outline" size={20} color="#666" style={[styles.inputIcon, { marginTop: 5 }]} />
            <TextInput
              style={[styles.input, { height: 80, textAlignVertical: 'top' }]}
              placeholder="Parlez brièvement de votre expérience professionnelle"
              value={profile.professionalBio}
              onChangeText={(value) => updateProfile({ professionalBio: value })}
              placeholderTextColor="#999"
              multiline={true}
              numberOfLines={4}
            />
          </View>
        </View>

        <View style={styles.formGroup}>
          <Text style={styles.label}>Visibilité du profil</Text>
          <View style={[styles.genderContainer, errors.profileVisibility ? {borderColor: '#ff3b30'} : null]}>
            {[
              { label: 'Public', value: 'public' },
              { label: 'Privé', value: 'private' },
              { label: 'Uniquement aux professionnels', value: 'professional' }
            ].map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.genderOption,
                  profile.profileVisibility === option.value && styles.genderOptionSelected
                ]}
                onPress={() => updateProfile({ profileVisibility: option.value })}
              >
                <Text style={[
                  styles.genderOptionText,
                  profile.profileVisibility === option.value && styles.genderOptionTextSelected
                ]}>
                  {option.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} onPress={handleNext}>
            <Text style={styles.buttonText}>Continuer</Text>
            <Ionicons name="arrow-forward" size={20} color="#fff" />
          </TouchableOpacity>
        </View>
      </ScrollView>

      {showDatePicker && (
        <DateTimePicker
          value={birthDate || new Date()}
          mode="date"
          display="default"
          onChange={onDateChange}
          maximumDate={new Date()}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fafafa',
  },
  cardHeader: {
    padding: 20, // Slightly larger padding
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: '#fafafa'
  },
  cardTitle: {
    fontSize: 22, // Slightly larger font size
    fontWeight: '700',
    color: '#333',
    marginBottom: 8 // Slightly larger margin
  },
  cardDescription: {
    fontSize: 15, // Slightly larger font size
    color: '#666',
    lineHeight: 20 // Slightly larger line height
  },
  scrollView: {
    flex: 1,
    width: '100%',
  },
  scrollViewContent: {
    paddingHorizontal: 16,
    paddingBottom: 100, // Add extra padding to ensure content is scrollable
  },
  formGroup: {
    marginBottom: 20 // Slightly larger margin
  },
  label: {
    fontSize: 15, // Slightly larger font size
    fontWeight: '600',
    marginBottom: 8, // Slightly larger margin
    color: '#333'
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ccc', // Slightly darker border color
    borderRadius: 12, // More rounded corners
    backgroundColor: '#f2f2f2', // Slightly darker background color
    paddingHorizontal: 12 // Slightly larger padding
  },
  inputIcon: {
    marginRight: 10, // Slightly larger margin
    color: '#007bff' // Use a more modern color
  },
  input: {
    flex: 1,
    height: 50, // Slightly larger height
    fontSize: 16, // Slightly larger font size
    color: '#333'
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ccc', // Slightly darker border color
    borderRadius: 12, // More rounded corners
    backgroundColor: '#f2f2f2', // Slightly darker background color
    paddingHorizontal: 12, // Slightly larger padding
    height: 50 // Slightly larger height
  },
  datePickerText: {
    flex: 1,
    fontSize: 16, // Slightly larger font size
    color: '#333'
  },
  placeholderText: {
    color: '#999'
  },
  inputError: {
    borderColor: '#ff4d4d' // Use a more visually distinct error color
  },
  errorText: {
    color: '#ff4d4d', // Use a more visually distinct error color
    fontSize: 12, // Slightly larger font size
    marginTop: 4, // Slightly larger margin
    marginLeft: 6 // Slightly larger margin
  },
  genderContainer: {
    borderWidth: 1,
    borderColor: '#ccc', // Slightly darker border color
    borderRadius: 12, // More rounded corners
    overflow: 'hidden'
  },
  genderOption: {
    paddingVertical: 12, // Slightly larger padding
    paddingHorizontal: 14, // Slightly larger padding
    borderBottomWidth: 1,
    borderBottomColor: '#eee'
  },
  genderOptionSelected: {
    backgroundColor: '#007bff', // Use a more modern color
  },
  genderOptionText: {
    fontSize: 15, // Slightly larger font size
    color: '#333'
  },
  genderOptionTextSelected: {
    color: '#fff',
    fontWeight: '500'
  },
  buttonContainer: {
    padding: 20, // Slightly larger padding
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    alignItems: 'flex-end'
  },
  button: {
    backgroundColor: '#007bff', // Use a more modern color
    borderRadius: 12, // More rounded corners
    paddingVertical: 14, // Slightly larger padding
    paddingHorizontal: 28, // Slightly larger padding
    flexDirection: 'row',
    alignItems: 'center'
  },
  buttonText: {
    color: '#fff',
    fontSize: 17, // Slightly larger font size
    fontWeight: '600',
    marginRight: 10 // Slightly larger margin
  }
});

// Export par défaut pour Expo Router
export default PersonalInfoStep;