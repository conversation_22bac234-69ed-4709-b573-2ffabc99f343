/*
  # Correction de la configuration du webhook

  1. Mise à jour
    - Réinitialisation des compteurs d'erreur
    - Mise à jour des URLs
    - Activation du webhook
    
  2. Fonction
    - Amélioration de la fonction de vérification du statut
*/

-- Réinitialiser et mettre à jour la configuration du webhook
UPDATE webhook_configurations
SET 
    url = 'https://n8n-dw1u.onrender.com/webhook-test/chat',
    backup_url = 'https://backup-n8n-dw1u.onrender.com/webhook-test/chat',
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    last_error_timestamp = null,
    max_retries = 3,
    retry_delay_base = 2000,
    health_check_interval = 300,
    metadata = jsonb_build_object(
        'last_reset', CURRENT_TIMESTAMP,
        'status', 'healthy',
        'version', '1.0.1'
    )
WHERE name = 'n8n_chatbot_agent';

-- Améliorer la fonction de vérification du statut
CREATE OR REPLACE FUNCTION check_webhook_configuration(webhook_name TEXT)
RETURNS TABLE (
    is_available BOOLEAN,
    current_url VARCHAR(500),
    error_details TEXT
) AS $$
DECLARE
    config_record webhook_configurations%ROWTYPE;
BEGIN
    -- Récupérer la configuration la plus récente
    SELECT * INTO config_record
    FROM webhook_configurations
    WHERE name = webhook_name;

    -- Vérifier si la configuration existe
    IF config_record IS NULL THEN
        RETURN QUERY SELECT 
            false::BOOLEAN, 
            NULL::VARCHAR(500), 
            'Configuration non trouvée'::TEXT;
        RETURN;
    END IF;

    -- Vérifier si la configuration est active
    IF NOT config_record.is_active THEN
        RETURN QUERY SELECT 
            false::BOOLEAN, 
            NULL::VARCHAR(500), 
            'Configuration inactive'::TEXT;
        RETURN;
    END IF;

    -- Vérifier le statut des erreurs
    IF config_record.error_count >= config_record.max_retries THEN
        -- Si nous avons une URL de backup, l'utiliser
        IF config_record.backup_url IS NOT NULL THEN
            RETURN QUERY SELECT 
                true::BOOLEAN,
                config_record.backup_url::VARCHAR(500),
                ''::TEXT;
        ELSE
            RETURN QUERY SELECT 
                false::BOOLEAN,
                NULL::VARCHAR(500),
                'Service temporairement indisponible'::TEXT;
        END IF;
        RETURN;
    END IF;

    -- Configuration normale et fonctionnelle
    RETURN QUERY SELECT 
        true::BOOLEAN,
        config_record.url::VARCHAR(500),
        ''::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Créer une fonction pour réinitialiser les erreurs
CREATE OR REPLACE FUNCTION reset_webhook_configuration(webhook_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    UPDATE webhook_configurations
    SET 
        error_count = 0,
        error_message = null,
        last_error_timestamp = null,
        is_active = true,
        last_check_timestamp = CURRENT_TIMESTAMP,
        metadata = jsonb_set(
            metadata,
            '{status}',
            '"healthy"'::jsonb
        )
    WHERE name = webhook_name
    RETURNING 1 INTO affected_rows;

    RETURN affected_rows > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;