import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Animated,
  Dimensions,
  Platform,
  StatusBar,
  ImageBackground,
  Pressable,
  ActivityIndicator,
  RefreshControl,
  Alert
} from 'react-native';
import { useTheme } from '../../context/theme';
import { useAuth } from '../../context/auth';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { supabase } from '../../lib/supabase';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';

import * as Location from 'expo-location';
import MapView, { Marker, PROVIDER_GOOGLE } from '../../components/Map';
import { recordActivity } from '../../lib/activity-tracking';

const { width, height } = Dimensions.get('window');
const AVATAR_DEFAULT = { uri: '/assets/images/profile.png' };

export default function HomeScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const [profile, setProfile] = useState<{
    first_name?: string;
    last_name?: string;
    profile_completed?: boolean;
    avatar_url?: string;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [location, setLocation] = useState<Location.LocationObject | null>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [address, setAddress] = useState<Location.LocationGeocodedAddress | null>(null);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);

  // Animations
  const scrollY = useRef(new Animated.Value(0)).current;
  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [1, 0.9],
    extrapolate: 'clamp'
  });
  const headerHeight = scrollY.interpolate({
    inputRange: [0, 100],
    outputRange: [200, 120],
    extrapolate: 'clamp'
  });
  const serviceScale = useRef(new Animated.Value(0.95)).current;
  const actionButtonScale = useRef(new Animated.Value(1)).current;
  const locationWatchRef = useRef<Location.LocationSubscription | null>(null);
  const mapRef = useRef<MapView>(null);
  const isMounted = useRef(true);

  // Ajoutez un état pour gérer le type de carte
  const [mapType, setMapType] = useState<'standard' | 'satellite' | 'hybrid'>('satellite');

  // Optimisé: Animation démarrée une seule fois avec des valeurs plus rapides
  useEffect(() => {
    Animated.spring(serviceScale, {
      toValue: 1,
      friction: 6, // Réduction de la friction pour une animation plus rapide
      tension: 50, // Augmentation de la tension pour une réponse plus rapide
      useNativeDriver: true
    }).start();

    // Nettoyage de la référence de montage lors du démontage
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Optimisé: Utilisation de useCallback pour mémoriser les fonctions
  const loadProfile = useCallback(async () => {
    if (!user || !isMounted.current) return;

    try {
      if (!refreshing) setLoading(true);

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Erreur lors du chargement du profil:', error);
      } else if (data && isMounted.current) {
        setProfile(data);

        // Vérifier si le profil est complet - mais seulement la première fois
        if (data.profile_completed === false && !initialLoadComplete) {
          // Éviter les alertes bloquantes et utiliser setTimeout uniquement si nécessaire
          Alert.alert(
            'Profil incomplet',
            'Veuillez compléter votre profil pour une meilleure expérience.',
            [{ text: 'OK', onPress: () => router.replace('/onboarding/profile-setup') }]
          );
        }
      }
    } catch (error) {
      console.error('Erreur inattendue:', error);
    } finally {
      if (isMounted.current) {
        setLoading(false);
        setRefreshing(false);
        setInitialLoadComplete(true);
      }
    }
  }, [user, refreshing, initialLoadComplete]);

  // Optimisé: Géolocalisation en utilisant la précision basse pour un chargement initial plus rapide
  const getLocationAsync = useCallback(async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted' || !isMounted.current) {
        setErrorMsg('Accès à la localisation refusé');
        return;
      }

      // Vérifier si les services de localisation sont activés
      const enabled = await Location.hasServicesEnabledAsync();
      if (!enabled || !isMounted.current) {
        setErrorMsg('Services de localisation désactivés');
        return;
      }

      // Pour le chargement initial, utiliser une précision moins élevée pour une réponse plus rapide
      const currentLocation = await Location.getCurrentPositionAsync({
        accuracy: initialLoadComplete ? Location.Accuracy.High : Location.Accuracy.Balanced,
      });

      if (isMounted.current) {
        setLocation(currentLocation);

        // Centrer la carte sur la position de l'utilisateur
        if (mapRef.current && currentLocation) {
          mapRef.current.animateToRegion({
            latitude: currentLocation.coords.latitude,
            longitude: currentLocation.coords.longitude,
            latitudeDelta: 0.005,
            longitudeDelta: 0.005,
          }, 1000);
        }

        // Obtenir l'adresse en arrière-plan après avoir affiché la carte
        setTimeout(() => {
          if (isMounted.current) {
            updateAddress(currentLocation.coords);
          }
        }, 500);
      }
    } catch (error) {
      console.error('Erreur de localisation:', error);
      if (isMounted.current) setErrorMsg('Impossible d\'obtenir la localisation');
    }
  }, [initialLoadComplete]);

  // Optimisé: Mise à jour de l'adresse non bloquante
  const updateAddress = async (coords: Location.LocationObjectCoords) => {
    if (!isMounted.current) return;

    try {
      const addressResponse = await Location.reverseGeocodeAsync({
        latitude: coords.latitude,
        longitude: coords.longitude
      });

      if (addressResponse && addressResponse.length > 0 && isMounted.current) {
        setAddress(addressResponse[0]);
      }
    } catch (error) {
      console.error('Erreur de géocodage:', error);
    }
  };

  // Chargement initial avec vérification de montage
  useEffect(() => {
    if (user) {
      // Charger le profil immédiatement
      loadProfile();

      // Charger la localisation avec un léger délai pour éviter de bloquer l'interface
      setTimeout(() => {
        if (isMounted.current) {
          getLocationAsync();
        }
      }, 300);

      // Enregistrer l'activité après le chargement initial
      recordActivity('page_view', { page: 'index' });
    }

    return () => {
      if (locationWatchRef.current) {
        locationWatchRef.current.remove();
      }
    };
  }, [user, loadProfile, getLocationAsync]);

  // Optimisé: Fonctions gérées par useCallback pour éviter les re-rendus inutiles
  const handleChatSupport = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push('/(app)/assessment/conversations');
  }, []);

  const handleActionPress = useCallback((destination: any) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    if (typeof destination === 'string') {
      router.push(destination as any);
    } else if (typeof destination === 'function') {
      destination();
    }
  }, []);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadProfile();
    getLocationAsync();
  }, [loadProfile, getLocationAsync]);

  // Optimisé: Fonction mémorisée pour le nom d'affichage
  const getDisplayName = useCallback(() => {
    if (profile?.first_name && profile?.last_name) {
      return `${profile.first_name} ${profile.last_name}`;
    } else if (profile?.first_name) {
      return profile.first_name;
    } else {
      return user?.email?.split('@')[0] || '';
    }
  }, [profile, user]);

  // Optimisé: Toggle de carte mémorisé
  const toggleMapType = useCallback(() => {
    const nextType = mapType === 'standard' ? 'satellite' :
                    mapType === 'satellite' ? 'hybrid' : 'standard';
    setMapType(nextType);

    if (user) {
      recordActivity('map_interaction', {
        action: 'change_map_type',
        previous_type: mapType,
        new_type: nextType
      });
    }
  }, [mapType, user]);

  // Déplacer les styles à l'intérieur du composant pour avoir accès à theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      width: '100%',
    },
    scrollView: {
      flex: 1,
      width: '100%',
    },
    scrollContent: {
      paddingHorizontal: 16, // Marges horizontales uniformes
      paddingBottom: 30,
      width: '100%', // Utiliser toute la largeur disponible
    },
    // Styles pour la carte
    mapContainer: {
      height: 200,
      borderRadius: 20,
      overflow: 'hidden',
      marginVertical: 16,
      shadowColor: theme.dark ? '#000' : '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: theme.dark ? 0.5 : 0.1,
      shadowRadius: 4,
      elevation: 3,
      width: '100%', // Utiliser toute la largeur disponible
    },
    map: {
      flex: 1,
    },
    mapLoading: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 20,
      backgroundColor: theme.colors.cardBackground,
    },
    mapLoadingText: {
      marginTop: 10,
      fontSize: 14,
      color: theme.colors.text,
    },
    customMarker: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: 'rgba(33, 150, 243, 0.3)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    markerInner: {
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: '#2196F3',
      borderWidth: 2,
      borderColor: theme.colors.background,
    },
    // Styles existants
    statusCard: {
      flexDirection: 'row',
      borderRadius: 16,
      padding: 16,
      marginBottom: 20,
      backgroundColor: theme.colors.cardBackground,
      borderWidth: 1,
      borderColor: theme.colors.cardBorder,
      shadowColor: theme.dark ? '#000' : '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: theme.dark ? 0.3 : 0.1,
      shadowRadius: 2,
      elevation: 2,
      width: '100%', // Utiliser toute la largeur disponible
    },
    statusItem: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    statusTextContainer: {
      marginLeft: 10,
    },
    statusTitle: {
      fontSize: 14,
      opacity: 0.7,
    },
    statusValue: {
      fontSize: 16,
      fontWeight: '600',
    },
    statusSeparator: {
      width: 1,
      height: '70%',
      backgroundColor: 'rgba(0, 0, 0, 0.1)',
      marginHorizontal: 15,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      marginHorizontal: 15,
      marginTop: 20,
      marginBottom: 10,
    },
    quickActionsContainer: {
      flexDirection: 'row',
      marginBottom: 20,
      width: '100%',
      justifyContent: 'space-between',
    },
    quickActionCard: {
      flex: 1,
      height: 100,
      borderRadius: 12,
      marginHorizontal: 4,
      overflow: 'hidden',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    quickActionGradient: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 15,
    },
    quickActionText: {
      color: '#fff',
      marginTop: 8,
      fontWeight: '500',
      textAlign: 'center',
    },
    servicesContainer: {
      marginHorizontal: 15,
      marginBottom: 20,
    },
    servicesScrollContent: {
      paddingRight: 15,
    },
    serviceCard: {
      width: 200,
      height: 180,
      borderRadius: 12,
      marginRight: 10,
      overflow: 'hidden',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    serviceGradient: {
      flex: 1,
      padding: 15,
    },
    serviceIconContainer: {
      width: 60,
      height: 60,
      borderRadius: 30,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 15,
    },
    serviceTitle: {
      fontSize: 16,
      fontWeight: '600',
      marginBottom: 8,
    },
    serviceDescription: {
      fontSize: 14,
      opacity: 0.8,
    },
    mapTypeButton: {
      position: 'absolute',
      top: 10,
      right: 10,
      backgroundColor: 'rgba(0,0,0,0.5)',
      borderRadius: 20,
      width: 40,
      height: 40,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <StatusBar barStyle={theme.dark ? "light-content" : "dark-content"} />

      {/* Le ProfileHeader est déjà affiché dans le layout des onglets */}

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[styles.scrollContent, {
          paddingTop: 8, // Ajouter un peu d'espace en haut
          paddingBottom: Platform.OS === 'ios' ? 100 : 80, // Espace en bas pour éviter que le contenu soit caché par la barre d'onglets
        }]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]} // Couleurs personnalisées pour Android
            tintColor={theme.colors.primary} // Couleur personnalisée pour iOS
          />
        }
      >
        {/* Carte Mapbox interactive remplaçant le second header */}
        <View style={styles.mapContainer}>
          {location ? (
            <MapView
              ref={mapRef}
              style={styles.map}
              provider={PROVIDER_GOOGLE}
              mapType={mapType}
              initialRegion={{
                latitude: location.coords.latitude,
                longitude: location.coords.longitude,
                latitudeDelta: 0.005,
                longitudeDelta: 0.005,
              }}
              showsUserLocation
              showsMyLocationButton
              showsCompass
            >
              <Marker
                coordinate={{
                  latitude: location.coords.latitude,
                  longitude: location.coords.longitude,
                }}
                title="Votre position"
                description={address ?
                  [address.street, address.city, address.region]
                    .filter(Boolean)
                    .join(', ') :
                  "Position actuelle"
                }
              >
                <View style={styles.customMarker}>
                  <View style={styles.markerInner} />
                </View>
              </Marker>
            </MapView>
          ) : (
            <View style={[styles.mapLoading, { backgroundColor: theme.colors.surface }]}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={[styles.mapLoadingText, { color: theme.colors.text }]}>
                {errorMsg || "Chargement de la carte..."}
        </Text>
            </View>
          )}
      </View>

        {/* Ajoutez un bouton de changement de type de carte */}
        <TouchableOpacity
          style={styles.mapTypeButton}
          onPress={toggleMapType}
        >
          <Ionicons
            name={mapType === 'standard' ? 'map-outline' :
                 mapType === 'satellite' ? 'globe-outline' : 'layers-outline'}
            size={22}
            color="#fff"
          />
        </TouchableOpacity>



        {/* Actions rapides */}
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Actions rapides
        </Text>

        <View style={styles.quickActionsContainer}>
          <TouchableOpacity
            style={[styles.quickActionCard, { backgroundColor: theme.dark ? '#FF5252' : '#FF6B6B' }]}
            onPress={() => handleActionPress('/(app)/(emergency)')}
          >
            <LinearGradient
              colors={theme.dark ? ['#FF5252', '#FF8E53'] : ['#FF6B6B', '#FF8E53']}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
              style={styles.quickActionGradient}
            >
              <Ionicons name="alert-circle-outline" size={28} color="#fff" />
              <Text style={styles.quickActionText}>SOS</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickActionCard, { backgroundColor: theme.dark ? '#E91E63' : '#EC407A' }]}
            onPress={() => handleActionPress('/(app)/denunciation')}
          >
            <LinearGradient
              colors={theme.dark ? ['#E91E63', '#F06292'] : ['#EC407A', '#F48FB1']}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
              style={styles.quickActionGradient}
            >
              <Ionicons name="warning-outline" size={28} color="#fff" />
              <Text style={styles.quickActionText}>Signaler</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickActionCard, { backgroundColor: theme.dark ? '#2F7CF6' : '#2196F3' }]}
            onPress={handleChatSupport}
          >
            <LinearGradient
              colors={theme.dark ? ['#3F51B5', '#764ba2'] : ['#667eea', '#764ba2']}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
              style={styles.quickActionGradient}
            >
              <Ionicons name="chatbubbles-outline" size={28} color="#fff" />
              <Text style={styles.quickActionText}>Parler à Mr.Bomoko</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* Services disponibles */}
        <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
          Services disponibles
        </Text>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.servicesScrollContent}
          style={styles.servicesContainer}
        >
          <TouchableOpacity
            style={[styles.serviceCard, {
              backgroundColor: theme.colors.cardBackground,
              borderColor: theme.colors.cardBorder,
              borderWidth: 1,
            }]}
            onPress={() => handleActionPress('/ngo-map')}
          >
            <LinearGradient
              colors={theme.dark
                ? ['rgba(33, 150, 243, 0.3)', 'rgba(33, 150, 243, 0.05)']
                : ['rgba(33, 150, 243, 0.4)', 'rgba(33, 150, 243, 0.1)']}
              style={styles.serviceGradient}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
            >
              <View style={[styles.serviceIconContainer, {
                backgroundColor: theme.dark ? 'rgba(33, 150, 243, 0.2)' : 'rgba(33, 150, 243, 0.1)'
              }]}>
                <Ionicons name="map-outline" size={32} color="#2196F3" />
              </View>
              <Text style={[styles.serviceTitle, { color: theme.colors.text }]}>
                Carte des ONG
              </Text>
              <Text style={[styles.serviceDescription, { color: theme.colors.textSecondary }]}>
                Trouvez de l'aide près de chez vous
              </Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.serviceCard, {
              backgroundColor: theme.colors.cardBackground,
              borderColor: theme.colors.cardBorder,
              borderWidth: 1,
            }]}
            onPress={() => handleActionPress('/(app)/appointments')}
          >
            <LinearGradient
              colors={theme.dark
                ? ['rgba(76, 175, 80, 0.3)', 'rgba(76, 175, 80, 0.05)']
                : ['rgba(76, 175, 80, 0.4)', 'rgba(76, 175, 80, 0.1)']}
              style={styles.serviceGradient}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
            >
              <View style={[styles.serviceIconContainer, {
                backgroundColor: theme.dark ? 'rgba(76, 175, 80, 0.2)' : 'rgba(76, 175, 80, 0.1)'
              }]}>
                <Ionicons name="calendar-outline" size={32} color="#4CAF50" />
              </View>
              <Text style={[styles.serviceTitle, { color: theme.colors.text }]}>
                Rendez-vous
              </Text>
              <Text style={[styles.serviceDescription, { color: theme.colors.textSecondary }]}>
                Consultations avec des professionnels
              </Text>
            </LinearGradient>
          </TouchableOpacity>

            <TouchableOpacity
            style={[styles.serviceCard, {
              backgroundColor: theme.colors.cardBackground,
              borderColor: theme.colors.cardBorder,
              borderWidth: 1,
            }]}
            onPress={() => handleActionPress('/(app)/forum')}
          >
            <LinearGradient
              colors={theme.dark
                ? ['rgba(255, 152, 0, 0.3)', 'rgba(255, 152, 0, 0.05)']
                : ['rgba(255, 152, 0, 0.4)', 'rgba(255, 152, 0, 0.1)']}
              style={styles.serviceGradient}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
            >
              <View style={[styles.serviceIconContainer, {
                backgroundColor: theme.dark ? 'rgba(255, 152, 0, 0.2)' : 'rgba(255, 152, 0, 0.1)'
              }]}>
                <Ionicons name="people-outline" size={32} color="#FF9800" />
              </View>
              <Text style={[styles.serviceTitle, { color: theme.colors.text }]}>
                Forum
              </Text>
              <Text style={[styles.serviceDescription, { color: theme.colors.textSecondary }]}>
                Échangez avec la communauté
              </Text>
            </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity
            style={[styles.serviceCard, {
              backgroundColor: theme.colors.cardBackground,
              borderColor: theme.colors.cardBorder,
              borderWidth: 1,
            }]}
            onPress={() => handleActionPress('/(app)/denunciation')}
          >
            <LinearGradient
              colors={theme.dark
                ? ['rgba(233, 30, 99, 0.3)', 'rgba(233, 30, 99, 0.05)']
                : ['rgba(233, 30, 99, 0.4)', 'rgba(233, 30, 99, 0.1)']}
              style={styles.serviceGradient}
              start={{x: 0, y: 0}}
              end={{x: 1, y: 0}}
            >
              <View style={[styles.serviceIconContainer, {
                backgroundColor: theme.dark ? 'rgba(233, 30, 99, 0.2)' : 'rgba(233, 30, 99, 0.1)'
              }]}>
                <Ionicons name="warning-outline" size={32} color="#E91E63" />
              </View>
              <Text style={[styles.serviceTitle, { color: theme.colors.text }]}>
                Signaler
              </Text>
              <Text style={[styles.serviceDescription, { color: theme.colors.textSecondary }]}>
                Dénoncer une violence
              </Text>
            </LinearGradient>
            </TouchableOpacity>
        </ScrollView>
        </ScrollView>
      </View>
  );
}