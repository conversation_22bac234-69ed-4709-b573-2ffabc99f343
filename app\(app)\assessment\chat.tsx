import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Platform,
  ActivityIndicator,
  StatusBar,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useTheme } from '../../../context/theme';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../../../lib/supabase';
import { useAuth } from '../../../context/auth';
import { format } from 'date-fns';
type Message = {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  created_at: string;
};

type WebhookConfig = {
  is_available: boolean;
  current_url: string;
  error_details: string;
};

const WEBHOOK_TIMEOUT = 30000; // 30 seconds
const MAX_RETRIES = 3;
const RETRY_DELAY = 2000;

// Définir la fonction séparément
function ChatScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const params = useLocalSearchParams();
  const conversationId = params.id as string;
  
  // Couleurs du thème pour les styles
  const backgroundColor = theme.colors.background;
  const borderColor = theme.colors.gray[200];
  const surfaceColor = theme.colors.surface;
  
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    // Appliquer le thème à l'écran entier
    if (Platform.OS === 'android') {
      StatusBar.setBackgroundColor(theme.colors.primary);
    }
    StatusBar.setBarStyle(theme.dark ? 'light-content' : 'dark-content');
    
    if (!user) {
      router.replace('/(auth)/login');
      return;
    }

    if (conversationId) {
      loadMessages();
    } else {
      initializeConversation();
    }
  }, [user, conversationId, theme]);

  const loadMessages = async () => {
    try {
      const { data: messages, error: messagesError } = await supabase
        .from('chat_messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true });

      if (messagesError) throw messagesError;
      setMessages(messages || []);
    } catch (err) {
      console.error('Error loading messages:', err);
      setError('Erreur lors du chargement des messages');
    }
  };

  const initializeConversation = async () => {
    try {
      const { data: conversation, error: conversationError } = await supabase
        .from('chat_conversations')
        .insert([{ user_id: user?.id }])
        .select()
        .single();

      if (conversationError) throw conversationError;

      const welcomeMessage = {
        content: 'Bonjour! Je suis là pour vous aider à évaluer votre situation. Comment puis-je vous aider aujourd\'hui?',
        role: 'assistant' as const,
      };

      await supabase
        .from('chat_messages')
        .insert([{
          conversation_id: conversation.id,
          content: welcomeMessage.content,
          role: welcomeMessage.role,
        }]);

      router.replace({
        pathname: '/assessment/chat',
        params: { id: conversation.id }
      });
    } catch (err) {
      console.error('Error initializing conversation:', err);
      setError('Erreur lors de l\'initialisation du chat');
    }
  };

  const getWebhookConfig = async (): Promise<WebhookConfig> => {
    try {
      console.log('Tentative de récupération de la configuration du webhook...');
      
      // Essayer d'abord de récupérer la configuration depuis une table dédiée
      const { data: configData, error: configError } = await supabase
        .from('webhook_configurations')  // Utiliser une table directe au lieu d'une fonction RPC
        .select('*')
        .eq('name', 'n8n_chatbot_agent')
        .single();
      
      if (configError) {
        console.error('Erreur lors de la récupération de la configuration:', configError);
        
        // Si la table n'existe pas, essayer avec la fonction RPC comme avant
        try {
          const { data, error } = await supabase
            .rpc('check_webhook_configuration', {
              webhook_name: 'n8n_chatbot_agent'
            });

          if (error) {
            console.error('Webhook config error (RPC):', error);
            throw new Error('Erreur de configuration du service');
          }

          if (!data || !Array.isArray(data) || data.length === 0) {
            console.error('Format de données de configuration invalide (RPC):', data);
            throw new Error('Configuration du service non disponible');
          }

          return data[0];
        } catch (rpcErr) {
          console.error('Échec de la récupération via RPC:', rpcErr);
          
          // Utiliser une configuration par défaut en dernier recours
          console.log('Utilisation de la configuration par défaut');
          return {
            is_available: true,
            current_url: 'https://n8n-dw1u.onrender.com/webhook/chat',
            error_details: ''
          };
        }
      }
      
      // Si nous avons récupéré la configuration depuis la table
      if (!configData) {
        console.error('Aucune configuration trouvée dans la table');
        throw new Error('Configuration du service non disponible');
      }
      
      // Convertir le format de la table au format attendu
      const config: WebhookConfig = {
        is_available: configData.is_active || false,
        current_url: configData.url || '',
        error_details: configData.error_message || ''
      };
      
      if (!config.is_available || !config.current_url) {
        console.error('Webhook non disponible selon la configuration:', config);
        throw new Error(config.error_details || 'Service temporairement indisponible');
      }
      
      console.log('Configuration du webhook récupérée avec succès:', config);
      return config;
      
    } catch (err) {
      console.error('Error getting webhook config:', err);
      
      // En dernier recours, utiliser une URL de secours
      console.log('Utilisation de la configuration de secours');
      return {
        is_available: true,
        current_url: 'https://n8n-dw1u.onrender.com/webhook/chat',
        error_details: ''
      };
    }
  };

  const sendMessageToWebhook = async (messageContent: string, webhookUrl: string, attempt = 0): Promise<string> => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), WEBHOOK_TIMEOUT);

    try {
      console.log(`Tentative d'envoi au webhook (${attempt+1}/${MAX_RETRIES}): ${webhookUrl}`);
      
      const payload = {
        message: messageContent,
        userId: user?.id,
        userName: user?.email ? user.email.split('@')[0] : 'Utilisateur',
        userEmail: user?.email,
        conversationId,
        timestamp: new Date().toISOString(),
        attempt,
        metadata: {
          platform: Platform.OS,
          appVersion: '1.0.0',
        }
      };

      // Vérifier si l'URL est valide
      if (!webhookUrl || !webhookUrl.startsWith('http')) {
        console.error('URL de webhook invalide:', webhookUrl);
        throw new Error('URL de webhook invalide');
      }

      console.log('Envoi de la requête au webhook...');
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(payload),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      console.log('Réponse reçue du webhook, statut:', response.status);

      if (!response.ok) {
        console.error(`Erreur HTTP: ${response.status}`);
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data = await response.json();
      console.log('Données reçues du webhook:', data);
      
      if (!data?.output || typeof data.output !== 'string') {
        console.error('Format de réponse invalide:', data);
        throw new Error('Format de réponse invalide');
      }

      // Mettre à jour le statut du webhook en cas de succès
      try {
        await supabase.rpc('update_webhook_status', {
          webhook_name: 'n8n_chatbot_agent',
          success: true
        });
        console.log('Statut du webhook mis à jour avec succès');
      } catch (statusErr) {
        console.error('Erreur lors de la mise à jour du statut:', statusErr);
      }

      return data.output;
    } catch (err) {
      clearTimeout(timeoutId);
      console.error('Erreur lors de l\'envoi au webhook:', err);
      throw err;
    }
  };

  const handleSend = async () => {
    if (!inputMessage.trim() || !conversationId || isLoading) return;

    const messageToSend = inputMessage.trim();
    setInputMessage('');
    setIsLoading(true);
    setError(null);

    try {
      console.log('Envoi du message:', messageToSend);
      
      // Enregistrer le message de l'utilisateur
      const { data: userMessage, error: userMessageError } = await supabase
        .from('chat_messages')
        .insert([{
          conversation_id: conversationId,
          content: messageToSend,
          role: 'user',
        }])
        .select()
        .single();

      if (userMessageError) {
        console.error('Erreur lors de l\'enregistrement du message utilisateur:', userMessageError);
        throw userMessageError;
      }
      
      console.log('Message utilisateur enregistré avec succès');
      setMessages(prev => [...prev, userMessage]);

      // Variable pour stocker la réponse finale
      let response: string;
      let isBackupResponse = false;
      
      try {
        // Essayer d'obtenir la configuration du webhook
        const config = await getWebhookConfig();
        console.log('Configuration du webhook obtenue:', config);
        
        // Essayer d'envoyer le message au webhook avec plusieurs tentatives
        let attempt = 0;
        let webhookResponse = null;

        while (attempt < MAX_RETRIES && webhookResponse === null) {
          try {
            if (attempt > 0) {
              console.log(`Attente de ${RETRY_DELAY}ms avant la tentative ${attempt+1}`);
              await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
            }
            webhookResponse = await sendMessageToWebhook(messageToSend, config.current_url, attempt);
            console.log('Réponse du webhook reçue avec succès');
            break;
          } catch (err) {
            attempt++;
            console.error(`Échec de la tentative ${attempt}:`, err);
            if (attempt >= MAX_RETRIES) throw err;
          }
        }
        
        if (webhookResponse) {
          response = webhookResponse;
        } else {
          throw new Error('Toutes les tentatives ont échoué');
        }
      } catch (webhookErr) {
        console.error('Erreur avec le webhook, utilisation du mode de secours:', webhookErr);
        
        // Mode de secours: générer une réponse locale
        response = "Je suis désolé, le service est actuellement indisponible. Votre message a été enregistré et sera traité dès que possible. Veuillez réessayer plus tard.";
        isBackupResponse = true;
        
        // Mettre à jour le statut du webhook pour indiquer l'échec
        try {
          await supabase.rpc('update_webhook_status', {
            webhook_name: 'n8n_chatbot_agent',
            success: false,
            error_msg: 'Service indisponible - réponse de secours utilisée'
          });
        } catch (statusErr) {
          console.error('Erreur lors de la mise à jour du statut du webhook:', statusErr);
        }
      }

      // Enregistrer la réponse (qu'elle vienne du webhook ou du mode de secours)
      console.log('Enregistrement de la réponse de l\'assistant:', response.substring(0, 50) + '...');
      const { data: assistantMessage, error: assistantMessageError } = await supabase
        .from('chat_messages')
        .insert([{
          conversation_id: conversationId,
          content: response,
          role: 'assistant',
        }])
        .select()
        .single();

      if (assistantMessageError) {
        console.error('Erreur lors de l\'enregistrement de la réponse:', assistantMessageError);
        throw assistantMessageError;
      }
      
      console.log('Réponse de l\'assistant enregistrée avec succès');
      setMessages(prev => [...prev, assistantMessage]);
      
      // Afficher un message d'information si nous avons utilisé le mode de secours
      if (isBackupResponse) {
        console.log('Affichage du message de secours à l\'utilisateur');
        setError('Le service est temporairement indisponible. Une réponse automatique a été générée.');
      }
      
    } catch (err) {
      console.error('Erreur globale:', err);
      let errorMessage = 'Une erreur est survenue. Veuillez réessayer.';
      
      if (err instanceof Error) {
        if (err.message.includes('Service indisponible')) {
          errorMessage = 'Le service est temporairement indisponible. Veuillez réessayer plus tard.';
        } else if (err.message.includes('configuration')) {
          errorMessage = 'Erreur de configuration du service. Veuillez réessayer plus tard.';
        } else if (err.message.includes('abort')) {
          errorMessage = 'Le service met trop de temps à répondre. Veuillez réessayer.';
        }
      }
      
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const formatMessageTime = (date: string) => {
    return format(new Date(date), 'HH:mm');
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={[styles.headerContainer, { borderBottomColor: borderColor }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.push('/assessment/conversations')}>
          <Ionicons
            name="arrow-back"
            size={24}
            color={theme.colors.text}
          />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.colors.text }]}>
          Conversation
        </Text>
      </View>

      <ScrollView
        ref={scrollViewRef}
        style={styles.contentContainer}
        contentContainerStyle={styles.messagesContent}
        onContentSizeChange={() => scrollViewRef.current?.scrollToEnd({ animated: true })}
      >
        {messages.map((message) => (
          <View
            key={message.id}
            style={[
              styles.messageWrapper,
              message.role === 'user' ? styles.userMessage : styles.assistantMessage,
            ]}>
            <View
              style={[
                styles.messageBubble,
                message.role === 'user'
                  ? [styles.userBubble, { backgroundColor: theme.colors.primary }]
                  : [styles.assistantBubble, { backgroundColor: surfaceColor }],
              ]}>
              <Text
                style={[
                  styles.messageText,
                  {
                    color:
                      message.role === 'user'
                        ? theme.colors.white
                        : theme.colors.text,
                  },
                ]}>
                {message.content}
              </Text>
              <Text
                style={[
                  styles.messageTime,
                  {
                    color:
                      message.role === 'user'
                        ? theme.colors.white
                        : theme.colors.gray[500],
                  },
                ]}>
                {formatMessageTime(message.created_at)}
              </Text>
            </View>
          </View>
        ))}

        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator color={theme.colors.primary} />
          </View>
        )}

        {error && (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorMessage, { color: theme.colors.error }]}>
              {error}
            </Text>
            <TouchableOpacity
              style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
              onPress={() => handleSend()}>
              <Text style={styles.retryButtonText}>Réessayer</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      <View style={[styles.inputContainer, { 
        backgroundColor: surfaceColor,
        borderTopColor: borderColor
      }]}>
        <TextInput
          style={[styles.inputField, { color: theme.colors.text }]}
          placeholder="Écrivez votre message..."
          placeholderTextColor={theme.colors.gray[400]}
          value={inputMessage}
          onChangeText={setInputMessage}
          multiline
          maxLength={500}
          editable={!isLoading}
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            {
              backgroundColor: theme.colors.primary,
              opacity: isLoading || !inputMessage.trim() ? 0.5 : 1,
            },
          ]}
          onPress={handleSend}
          disabled={isLoading || !inputMessage.trim()}>
          <Ionicons name="send" size={24} color="white" />
        </TouchableOpacity>
      </View>
    </View>
  );
}

// Export explicite comme default
export default ChatScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 15,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 15,
    paddingTop: 10,
  },
  messagesContent: {
    paddingVertical: 10,
  },
  messageDate: {
    textAlign: 'center',
    fontSize: 12,
    marginVertical: 10,
  },
  messageWrapper: {
    marginBottom: 10,
    flexDirection: 'row',
  },
  userMessage: {
    justifyContent: 'flex-end',
  },
  assistantMessage: {
    justifyContent: 'flex-start',
  },
  messageBubble: {
    padding: 12,
    borderRadius: 18,
    marginBottom: 10,
    maxWidth: '75%',
  },
  userBubble: {
    alignSelf: 'flex-end',
    borderTopRightRadius: 0,
  },
  assistantBubble: {
    alignSelf: 'flex-start',
    borderTopLeftRadius: 0,
  },
  messageText: {
    fontSize: 16,
  },
  messageTime: {
    fontSize: 12,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    alignItems: 'center',
  },
  errorMessage: {
    fontSize: 16,
    marginBottom: 15,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderTopWidth: 1,
  },
  inputField: {
    flex: 1,
    borderRadius: 20,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginRight: 10,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 15,
  },
});