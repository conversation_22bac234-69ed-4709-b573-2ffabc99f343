-- Create or replace the check webhook configuration function
CREATE OR REPLACE FUNCTION check_webhook_configuration(webhook_name TEXT)
RETURNS TABLE (
    is_available BOOLEAN,
    current_url VARCHAR(500),
    error_details TEXT
) AS $$
DECLARE
    config_record webhook_configurations%ROWTYPE;
BEGIN
    -- Get webhook record
    SELECT * INTO config_record
    FROM webhook_configurations
    WHERE name = webhook_name;

    -- Check if webhook exists
    IF config_record IS NULL THEN
        RETURN QUERY SELECT 
            false::BOOLEAN, 
            NULL::VARCHAR(500), 
            'Configuration non trouvée'::TEXT;
        RETURN;
    END IF;

    -- Check if webhook is active
    IF NOT config_record.is_active THEN
        RETURN QUERY SELECT 
            false::BOOLEAN, 
            NULL::VARCHAR(500), 
            'Configuration inactive'::TEXT;
        RETURN;
    END IF;

    -- Check error status
    IF config_record.error_count >= config_record.max_retries THEN
        -- If we have a backup URL, use it
        IF config_record.backup_url IS NOT NULL THEN
            RETURN QUERY SELECT 
                true::BOOLEAN,
                config_record.backup_url::VARCHAR(500),
                ''::TEXT;
        ELSE
            RETURN QUERY SELECT 
                false::BOOLEAN,
                NULL::VARCHAR(500),
                'Service temporairement indisponible'::TEXT;
        END IF;
        RETURN;
    END IF;

    -- Return normal configuration
    RETURN QUERY SELECT 
        true::BOOLEAN,
        config_record.url::VARCHAR(500),
        ''::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;