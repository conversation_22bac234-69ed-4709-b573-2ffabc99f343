import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Stack } from 'expo-router';
import { useTheme } from '../../context/theme';
import NGOMapScreen from '../(app)/ngo-map';

export default function NGOTab() {
  const { theme } = useTheme();

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ headerShown: false }} />
      <NGOMapScreen />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});