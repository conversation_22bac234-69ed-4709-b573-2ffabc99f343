/*
  # Update webhook configuration and add status check function

  1. Changes
    - Update webhook URLs and configuration parameters
    - Add function to check webhook status and availability
  
  2. Security
    - Function is marked as SECURITY DEFINER
    - Only authenticated users can access webhook data
*/

-- Update webhook URLs with correct configuration
UPDATE webhook_urls
SET 
    url = 'https://n8n-dw1u.onrender.com/webhook-test/chat',
    backup_url = 'https://backup-n8n-dw1u.onrender.com/webhook-test/chat',
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    health_check_enabled = true,
    max_retries = 3,
    retry_delay_base = 2000,
    health_check_interval = 300
WHERE name = 'n8n_chatbot_agent';

-- Add webhook status check function
CREATE OR REPLACE FUNCTION check_webhook_status(webhook_name TEXT)
RETURNS TABLE (
    is_available boolean,
    current_url text,
    error_details text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        w.is_active,
        CASE 
            WHEN w.error_count >= w.max_retries THEN w.backup_url
            ELSE w.url
        END,
        w.error_message
    FROM webhook_urls w
    WHERE w.name = webhook_name
    AND w.is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;