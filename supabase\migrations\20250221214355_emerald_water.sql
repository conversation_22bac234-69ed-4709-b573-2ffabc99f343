-- Update webhook URLs to use the correct endpoints and ensure they are active
UPDATE webhook_urls
SET url = 'https://n8n-dw1u.onrender.com/webhook-test/chat',
    backup_url = 'https://n8n-backup-dw1u.onrender.com/webhook-test/chat',
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    health_check_enabled = true
WHERE name = 'n8n_chatbot_agent';

-- Create a function to check webhook availability
CREATE OR REPLACE FUNCTION check_webhook_availability()
RETURNS void AS $$
BEGIN
  UPDATE webhook_urls
  SET is_active = true,
      error_count = 0,
      error_message = null,
      last_check_timestamp = CURRENT_TIMESTAMP
  WHERE name = 'n8n_chatbot_agent'
    AND (last_check_timestamp IS NULL OR last_check_timestamp < CURRENT_TIMESTAMP - INTERVAL '5 minutes');
END;
$$ LANGUAGE plpgsql;