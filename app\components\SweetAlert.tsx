import React, { useEffect, useState } from 'react';
import { Alert, Platform, View, Text, Modal, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import Toast from 'react-native-toast-message';
import Animated, { FadeIn, FadeOut, SlideInUp, SlideOutDown, ZoomIn, ZoomOut } from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import AnimatedSuccessMessage from './AnimatedSuccessMessage';
import AnimatedErrorMessage from './AnimatedErrorMessage';

// Types pour les alertes
interface AlertOptions {
  title?: string;
  message: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  confirmText?: string;
  cancelText?: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  useCustomAlert?: boolean;
  useAnimatedAlert?: boolean;
}

// États globaux pour les alertes animées
let successMessageQueue: Array<{id: number, message: string, onClose?: () => void}> = [];
let errorMessageQueue: Array<{id: number, message: string, buttonText?: string, onClose?: () => void}> = [];
let currentSuccessId = 0;
let currentErrorId = 0;
let setSuccessVisible: ((visible: boolean) => void) | null = null;
let setSuccessMessage: ((message: string) => void) | null = null;
let setSuccessOnClose: ((callback: () => void) => void) | null = null;
let setErrorVisible: ((visible: boolean) => void) | null = null;
let setErrorMessage: ((message: string) => void) | null = null;
let setErrorButtonText: ((text: string) => void) | null = null;
let setErrorOnClose: ((callback: () => void) => void) | null = null;

// Composant pour une alerte personnalisée animée
const AnimatedAlert = ({ 
  visible, 
  title = '', 
  message, 
  onConfirm, 
  onCancel, 
  confirmText = 'OK', 
  cancelText = 'Annuler',
  type = 'info',
  onClose 
}: {
  visible: boolean;
  title?: string;
  message: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  confirmText?: string;
  cancelText?: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  onClose: () => void;
}) => {
  const getIconForType = () => {
    switch (type) {
      case 'success':
        return { name: 'checkmark-circle' as const, color: '#28A745' };
      case 'error':
        return { name: 'close-circle' as const, color: '#DC3545' };
      case 'warning':
        return { name: 'warning' as const, color: '#FFC107' };
      case 'info':
      default:
        return { name: 'information-circle' as const, color: '#17A2B8' };
    }
  };

  const icon = getIconForType();

  if (!visible) return null;

  return (
    <Modal
      transparent={true}
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Animated.View 
          style={styles.alertContainer}
          entering={ZoomIn.springify().damping(12)}
          exiting={FadeOut.duration(200)}
        >
          <View style={styles.iconContainer}>
            <Ionicons name={icon.name} size={50} color={icon.color} />
          </View>
          
          {title ? (
            <Text style={styles.title}>{title}</Text>
          ) : null}
          
          <Text style={styles.message}>{message}</Text>
          
          <View style={styles.buttonContainer}>
            {onCancel && (
              <TouchableOpacity 
                style={[styles.button, styles.cancelButton]} 
                onPress={() => {
                  onCancel();
                  onClose();
                }}
              >
                <Text style={styles.cancelButtonText}>{cancelText}</Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity 
              style={[
                styles.button, 
                styles.confirmButton,
                { backgroundColor: icon.color }
              ]} 
              onPress={() => {
                if (onConfirm) onConfirm();
                onClose();
              }}
            >
              <Text style={styles.confirmButtonText}>{confirmText}</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

// État global pour l'alerte personnalisée
let alertQueue: Array<AlertOptions & { id: number }> = [];
let currentId = 0;
let setAlertVisible: ((visible: boolean) => void) | null = null;
let setAlertConfig: ((config: Omit<AlertOptions, 'useCustomAlert' | 'useAnimatedAlert'>) => void) | null = null;

// Composant AlertProvider à placer à la racine de l'application
export const AlertProvider = ({ children }: { children: React.ReactNode }) => {
  const [alertVisible, _setAlertVisible] = useState(false);
  const [alertConfig, _setAlertConfig] = useState<Omit<AlertOptions, 'useCustomAlert' | 'useAnimatedAlert'>>({
    message: '',
  });
  
  // États pour les alertes animées spécialisées
  const [successVisible, _setSuccessVisible] = useState(false);
  const [successMsg, _setSuccessMessage] = useState('');
  const [successCallback, _setSuccessCallback] = useState<() => void>(() => {});
  
  const [errorVisible, _setErrorVisible] = useState(false);
  const [errorMsg, _setErrorMessage] = useState('');
  const [errorBtnText, _setErrorButtonText] = useState('OK');
  const [errorCallback, _setErrorCallback] = useState<() => void>(() => {});

  useEffect(() => {
    setAlertVisible = _setAlertVisible;
    setAlertConfig = _setAlertConfig;
    
    // Initialiser les références pour les alertes animées spécialisées
    setSuccessVisible = _setSuccessVisible;
    setSuccessMessage = _setSuccessMessage;
    setSuccessOnClose = _setSuccessCallback;
    
    setErrorVisible = _setErrorVisible;
    setErrorMessage = _setErrorMessage;
    setErrorButtonText = _setErrorButtonText;
    setErrorOnClose = _setErrorCallback;

    return () => {
      setAlertVisible = null;
      setAlertConfig = null;
      setSuccessVisible = null;
      setSuccessMessage = null;
      setSuccessOnClose = null;
      setErrorVisible = null;
      setErrorMessage = null;
      setErrorButtonText = null;
      setErrorOnClose = null;
    };
  }, []);

  const processNextAlert = () => {
    if (alertQueue.length > 0) {
      const nextAlert = alertQueue.shift()!;
      _setAlertConfig(nextAlert);
      _setAlertVisible(true);
    }
  };

  const handleClose = () => {
    _setAlertVisible(false);
    setTimeout(processNextAlert, 300); // Attendre que l'animation de fermeture soit terminée
  };
  
  // Handlers pour les alertes animées spécialisées
  const handleSuccessClose = () => {
    _setSuccessVisible(false);
    if (successCallback) successCallback();
    
    // Traiter la prochaine alerte de succès dans la file
    setTimeout(() => {
      if (successMessageQueue.length > 0) {
        const next = successMessageQueue.shift()!;
        _setSuccessMessage(next.message);
        _setSuccessCallback(() => next.onClose || (() => {}));
        _setSuccessVisible(true);
      }
    }, 300);
  };
  
  const handleErrorClose = () => {
    _setErrorVisible(false);
    if (errorCallback) errorCallback();
    
    // Traiter la prochaine alerte d'erreur dans la file
    setTimeout(() => {
      if (errorMessageQueue.length > 0) {
        const next = errorMessageQueue.shift()!;
        _setErrorMessage(next.message);
        _setErrorButtonText(next.buttonText || 'OK');
        _setErrorCallback(() => next.onClose || (() => {}));
        _setErrorVisible(true);
      }
    }, 300);
  };

  return (
    <>
      {children}
      <AnimatedAlert
        visible={alertVisible}
        title={alertConfig.title}
        message={alertConfig.message}
        onConfirm={alertConfig.onConfirm}
        onCancel={alertConfig.onCancel}
        confirmText={alertConfig.confirmText}
        cancelText={alertConfig.cancelText}
        type={alertConfig.type}
        onClose={handleClose}
      />
      
      <AnimatedSuccessMessage 
        visible={successVisible}
        message={successMsg}
        onClose={handleSuccessClose}
      />
      
      <AnimatedErrorMessage
        visible={errorVisible}
        message={errorMsg}
        buttonText={errorBtnText}
        onClose={handleErrorClose}
      />
    </>
  );
};

// Fonction pour afficher une alerte de succès animée
export const showAnimatedSuccess = (message: string, onClose?: () => void) => {
  if (setSuccessVisible && setSuccessMessage && setSuccessOnClose) {
    const alertId = currentSuccessId++;
    successMessageQueue.push({
      id: alertId,
      message,
      onClose
    });
    
    if (successMessageQueue.length === 1) {
      setSuccessMessage(message);
      setSuccessOnClose(() => onClose || (() => {}));
      setSuccessVisible(true);
    }
  } else {
    // Fallback si le provider n'est pas initialisé
    Toast.show({
      type: 'success',
      text1: 'Succès',
      text2: message,
    });
    if (onClose) setTimeout(onClose, 2000);
  }
};

// Fonction pour afficher une alerte d'erreur animée
export const showAnimatedError = (message: string, buttonText: string = 'OK', onClose?: () => void) => {
  if (setErrorVisible && setErrorMessage && setErrorButtonText && setErrorOnClose) {
    const alertId = currentErrorId++;
    errorMessageQueue.push({
      id: alertId,
      message,
      buttonText,
      onClose
    });
    
    if (errorMessageQueue.length === 1) {
      setErrorMessage(message);
      setErrorButtonText(buttonText);
      setErrorOnClose(() => onClose || (() => {}));
      setErrorVisible(true);
    }
  } else {
    // Fallback si le provider n'est pas initialisé
    Toast.show({
      type: 'error',
      text1: 'Erreur',
      text2: message,
    });
    if (onClose) setTimeout(onClose, 2000);
  }
};

// Fonction pour afficher une alerte de base
export const showAlert = (options: AlertOptions) => {
  const { 
    title = '', 
    message, 
    onConfirm, 
    onCancel, 
    confirmText = 'OK',
    cancelText = 'Annuler',
    type = 'info',
    useCustomAlert = true,
    useAnimatedAlert = false
  } = options;

  // Utiliser les alertes animées spécialisées si demandé
  if (useAnimatedAlert) {
    if (type === 'success') {
      showAnimatedSuccess(message, onConfirm);
      return;
    } else if (type === 'error') {
      showAnimatedError(message, confirmText, onConfirm);
      return;
    }
  }

  // Utiliser l'alerte personnalisée si demandé et disponible
  if (useCustomAlert && setAlertVisible && setAlertConfig) {
    const alertId = currentId++;
    alertQueue.push({
      id: alertId,
      title,
      message,
      onConfirm,
      onCancel,
      confirmText,
      cancelText,
      type
    });

    if (alertQueue.length === 1) { // Si c'est la première alerte dans la file
      setAlertConfig({
        title,
        message,
        onConfirm,
        onCancel,
        confirmText,
        cancelText,
        type
      });
      setAlertVisible(true);
    }
  } else {
    // Version standard pour les alertes de base
    if (onCancel) {
      // Alerte avec confirmation et annulation
      Alert.alert(
        title,
        message,
        [
          {
            text: cancelText,
            onPress: onCancel,
            style: 'cancel',
          },
          {
            text: confirmText,
            onPress: onConfirm,
            style: 'default',
          },
        ],
        { cancelable: false }
      );
    } else {
      // Alerte simple avec un seul bouton
      Alert.alert(
        title,
        message,
        [
          {
            text: confirmText,
            onPress: onConfirm,
          },
        ],
        { cancelable: false }
      );
    }
  }

  // Afficher également un toast pour plus de visibilité
  Toast.show({
    type: type,
    text1: title || getDefaultTitleByType(type),
    text2: message,
    position: 'top',
    visibilityTime: 4000,
    autoHide: true,
    topOffset: Platform.OS === 'ios' ? 60 : 40,
  });
};

// Fonction pour obtenir un titre par défaut selon le type d'alerte
const getDefaultTitleByType = (type: AlertOptions['type']) => {
  switch (type) {
    case 'success':
      return 'Succès';
    case 'error':
      return 'Erreur';
    case 'warning':
      return 'Attention';
    case 'info':
    default:
      return 'Information';
  }
};

// Fonctions d'aide pour différents types d'alertes
export const showSuccessAlert = (options: Omit<AlertOptions, 'type'>) => {
  showAlert({ ...options, type: 'success' });
};

export const showErrorAlert = (options: Omit<AlertOptions, 'type'>) => {
  showAlert({ ...options, type: 'error' });
};

export const showWarningAlert = (options: Omit<AlertOptions, 'type'>) => {
  showAlert({ ...options, type: 'warning' });
};

export const showInfoAlert = (options: Omit<AlertOptions, 'type'>) => {
  showAlert({ ...options, type: 'info' });
};

// Alerte de confirmation avec deux boutons
export const showConfirmAlert = (
  title: string,
  message: string,
  onConfirm: () => void,
  onCancel?: () => void,
  confirmText = 'Confirmer',
  cancelText = 'Annuler'
) => {
  showAlert({
    title,
    message,
    onConfirm,
    onCancel: onCancel || (() => {}),
    confirmText,
    cancelText,
    type: 'warning',
  });
};

// Versions animées simplifiées
export const showAnimatedSuccessAlert = (
  message: string,
  onClose?: () => void
) => {
  Alert.alert("Succès", message, [{ text: "OK", onPress: onClose }]);
};

export const showAnimatedErrorAlert = (
  message: string,
  buttonText: string = "OK",
  onClose?: () => void
) => {
  Alert.alert("Erreur", message, [{ text: buttonText, onPress: onClose }]);
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  alertContainer: {
    width: width * 0.85,
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  iconContainer: {
    marginBottom: 15,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  confirmButton: {
    backgroundColor: '#28A745',
  },
  cancelButton: {
    backgroundColor: '#F8F9FA',
    marginRight: 10,
  },
  confirmButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  cancelButtonText: {
    color: '#6C757D',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

// Exportation par défaut pour faciliter l'utilisation
export default {
  showAlert,
  showSuccessAlert,
  showErrorAlert,
  showWarningAlert,
  showInfoAlert,
  showConfirmAlert,
  showAnimatedSuccessAlert,
  showAnimatedErrorAlert,
  AlertProvider,
}; 