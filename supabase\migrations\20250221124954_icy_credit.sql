/*
  # Add webhook_urls table and assistants integration

  1. New Tables
    - `webhook_urls`
      - `id` (serial, primary key)
      - `name` (varchar, unique)
      - `url` (varchar)
      - `description` (text)
      - `is_active` (boolean)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    
    - `assistants`
      - `id` (serial, primary key)
      - `assistant_type` (varchar)
      - `webhook_url_id` (integer, foreign key)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on both tables
    - Add policies for admin access
*/

-- Create webhook_urls table
CREATE TABLE IF NOT EXISTS webhook_urls (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    url VARCHAR(500) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create assistants table
CREATE TABLE IF NOT EXISTS assistants (
    id SERIAL PRIMARY KEY,
    assistant_type VARCHAR(50) NOT NULL,
    webhook_url_id INTEGER REFERENCES webhook_urls(id),
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Enable RLS
ALTER TABLE webhook_urls ENABLE ROW LEVEL SECURITY;
ALTER TABLE assistants ENABLE ROW LEVEL SECURITY;

-- Create policies for webhook_urls
CREATE POLICY "Allow read access for authenticated users"
    ON webhook_urls
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Allow all access for admins"
    ON webhook_urls
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Create policies for assistants
CREATE POLICY "Allow read access for authenticated users on assistants"
    ON assistants
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Allow all access for admins on assistants"
    ON assistants
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE profiles.id = auth.uid()
            AND profiles.role = 'admin'
        )
    );

-- Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for both tables
CREATE TRIGGER update_webhook_urls_updated_at
    BEFORE UPDATE ON webhook_urls
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assistants_updated_at
    BEFORE UPDATE ON assistants
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert initial data
INSERT INTO webhook_urls (name, url, description)
VALUES (
    'n8n_chatbot_agent',
    'https://n8n-dw1u.onrender.com/webhook-test/chat',
    'Webhook URL for N8N chatbot agent integration'
);

-- Create initial chatbot assistant
INSERT INTO assistants (assistant_type, webhook_url_id)
VALUES (
    'chatbot',
    (SELECT id FROM webhook_urls WHERE name = 'n8n_chatbot_agent')
);