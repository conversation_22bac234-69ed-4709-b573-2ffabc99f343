import React from 'react';
import Toast, { BaseToast, ErrorToast } from 'react-native-toast-message';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, { SlideInUp, SlideOutDown } from 'react-native-reanimated';
import { AlertProvider } from './SweetAlert';

export const ToastProvider = ({ children }: { children: React.ReactNode }) => {
  // Personnalisation des toasts avec animations
  const toastConfig = {
    success: (props: any) => (
      <Animated.View
        entering={SlideInUp.duration(300)}
        exiting={SlideOutDown.duration(300)}
      >
        <BaseToast
          {...props}
          style={{ 
            borderLeftColor: '#28A745',
            backgroundColor: '#f0fff0',
            height: 'auto',
            minHeight: 60,
            paddingVertical: 10,
            borderRadius: 8,
            marginHorizontal: 16
          }}
          contentContainerStyle={{ paddingHorizontal: 15 }}
          text1Style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#28A745',
          }}
          text2Style={{
            fontSize: 14,
            color: '#444',
          }}
          text1NumberOfLines={1}
          text2NumberOfLines={2}
          renderLeadingIcon={() => (
            <View style={styles.iconContainer}>
              <Ionicons name="checkmark-circle" size={24} color="#28A745" />
            </View>
          )}
        />
      </Animated.View>
    ),
    error: (props: any) => (
      <Animated.View
        entering={SlideInUp.duration(300)}
        exiting={SlideOutDown.duration(300)}
      >
        <ErrorToast
          {...props}
          style={{ 
            borderLeftColor: '#FF3B30',
            backgroundColor: '#fff0f0',
            height: 'auto',
            minHeight: 60,
            paddingVertical: 10,
            borderRadius: 8,
            marginHorizontal: 16
          }}
          contentContainerStyle={{ paddingHorizontal: 15 }}
          text1Style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#FF3B30',
          }}
          text2Style={{
            fontSize: 14,
            color: '#444',
          }}
          text1NumberOfLines={1}
          text2NumberOfLines={2}
          renderLeadingIcon={() => (
            <View style={styles.iconContainer}>
              <Ionicons name="alert-circle" size={24} color="#FF3B30" />
            </View>
          )}
        />
      </Animated.View>
    ),
    warning: (props: any) => (
      <Animated.View
        entering={SlideInUp.duration(300)}
        exiting={SlideOutDown.duration(300)}
      >
        <BaseToast
          {...props}
          style={{ 
            borderLeftColor: '#FF9500',
            backgroundColor: '#fffaf0',
            height: 'auto',
            minHeight: 60,
            paddingVertical: 10,
            borderRadius: 8,
            marginHorizontal: 16
          }}
          contentContainerStyle={{ paddingHorizontal: 15 }}
          text1Style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#FF9500',
          }}
          text2Style={{
            fontSize: 14,
            color: '#444',
          }}
          text1NumberOfLines={1}
          text2NumberOfLines={2}
          renderLeadingIcon={() => (
            <View style={styles.iconContainer}>
              <Ionicons name="warning" size={24} color="#FF9500" />
            </View>
          )}
        />
      </Animated.View>
    ),
    info: (props: any) => (
      <Animated.View
        entering={SlideInUp.duration(300)}
        exiting={SlideOutDown.duration(300)}
      >
        <BaseToast
          {...props}
          style={{ 
            borderLeftColor: '#007AFF',
            backgroundColor: '#f0f8ff',
            height: 'auto',
            minHeight: 60,
            paddingVertical: 10,
            borderRadius: 8,
            marginHorizontal: 16
          }}
          contentContainerStyle={{ paddingHorizontal: 15 }}
          text1Style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#007AFF',
          }}
          text2Style={{
            fontSize: 14,
            color: '#444',
          }}
          text1NumberOfLines={1}
          text2NumberOfLines={2}
          renderLeadingIcon={() => (
            <View style={styles.iconContainer}>
              <Ionicons name="information-circle" size={24} color="#007AFF" />
            </View>
          )}
        />
      </Animated.View>
    ),
  };

  return (
    <AlertProvider>
      {children}
      <Toast config={toastConfig} />
    </AlertProvider>
  );
};

const styles = StyleSheet.create({
  iconContainer: {
    paddingHorizontal: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ToastProvider;