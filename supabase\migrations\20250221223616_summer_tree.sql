/*
  # Update webhook security policies

  1. Changes
    - Add new policy to allow authenticated users to update webhook URLs
    - Add policy for webhook health status updates
  
  2. Security
    - Maintains existing RLS
    - Adds controlled access for URL updates
*/

-- Drop existing policies for webhook_urls
DROP POLICY IF EXISTS "Allow read access for authenticated users" ON webhook_urls;
DROP POLICY IF EXISTS "Allow all access for admins" ON webhook_urls;

-- Create new policies with more granular control
CREATE POLICY "Allow read access for authenticated users"
    ON webhook_urls
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Allow URL updates for authenticated users"
    ON webhook_urls
    FOR UPDATE
    TO authenticated
    USING (
        url ~ '^https://n8n-dw1u\.onrender\.com/webhook.*' OR
        url ~ '^https://backup-n8n-dw1u\.onrender\.com/webhook.*'
    );

CREATE POLICY "Allow health status updates"
    ON webhook_urls
    FOR UPDATE
    TO authenticated
    USING (true);

-- <PERSON><PERSON> function to validate webhook URLs
CREATE OR REPLACE FUNCTION validate_webhook_url(url TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN url ~ '^https://n8n-dw1u\.onrender\.com/webhook.*' OR
           url ~ '^https://backup-n8n-dw1u\.onrender\.com/webhook.*';
END;
$$ LANGUAGE plpgsql;