-- Insert or update emergency webhook configuration
INSERT INTO webhook_configurations (
    name,
    url,
    backup_url,
    description,
    is_active,
    max_retries,
    retry_delay_base,
    health_check_interval,
    metadata
) VALUES (
    'n8n_emergency_webhook',
    'https://n8n-dw1u.onrender.com/webhook-test/emerigency',
    'https://n8n-dw1u.onrender.com/webhook-test/emerigency',
    'Configuration du webhook pour les alertes SOS d''urgence',
    true,
    3,
    2000,
    300,
    jsonb_build_object(
        'version', '1.0.0',
        'status', 'healthy',
        'last_update', CURRENT_TIMESTAMP,
        'webhook_type', 'emergency',
        'content_type', 'application/json',
        'expected_response_format', jsonb_build_object(
            'status', 'string',
            'message', 'string',
            'event_id', 'string'
        )
    )
)
ON CONFLICT (name) 
DO UPDATE SET
    url = EXCLUDED.url,
    backup_url = EXCLUDED.backup_url,
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    metadata = EXCLUDED.metadata;