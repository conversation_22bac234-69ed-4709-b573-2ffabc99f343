/*
  # Configuration des webhooks

  1. Tables
    - `webhook_configurations`
      - `id` (serial, primary key)
      - `name` (varchar, unique)
      - `url` (varchar)
      - `backup_url` (varchar, nullable)
      - `description` (text)
      - `is_active` (boolean)
      - `max_retries` (integer)
      - `retry_delay_base` (integer)
      - `health_check_interval` (integer)
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)
      - `last_check_timestamp` (timestamptz)
      - `last_error_timestamp` (timestamptz)
      - `error_count` (integer)
      - `error_message` (text)
      - `metadata` (jsonb)

  2. Sécurité
    - RLS activé
    - Politiques pour lecture et mise à jour
    - Validation des URLs

  3. Fonctions
    - Vérification du statut
    - Réinitialisation des erreurs
*/

-- Créer la table de configuration des webhooks
CREATE TABLE IF NOT EXISTS webhook_configurations (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    url VARCHAR(500) NOT NULL,
    backup_url VARCHAR(500),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    max_retries INTEGER DEFAULT 3,
    retry_delay_base INTEGER DEFAULT 2000,
    health_check_interval INTEGER DEFAULT 300,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    last_check_timestamp TIMESTAMPTZ,
    last_error_timestamp TIMESTAMPTZ,
    error_count INTEGER DEFAULT 0,
    error_message TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    CONSTRAINT valid_urls CHECK (
        url ~ '^https://' AND 
        (backup_url IS NULL OR backup_url ~ '^https://')
    )
);

-- Activer RLS
ALTER TABLE webhook_configurations ENABLE ROW LEVEL SECURITY;

-- Créer les politiques de sécurité
CREATE POLICY "Lecture autorisée pour les utilisateurs authentifiés"
    ON webhook_configurations
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Mise à jour autorisée pour les utilisateurs authentifiés"
    ON webhook_configurations
    FOR UPDATE
    TO authenticated
    USING (is_active = true);

-- Fonction pour vérifier le statut du webhook
CREATE OR REPLACE FUNCTION check_webhook_configuration(webhook_name TEXT)
RETURNS TABLE (
    is_available BOOLEAN,
    current_url VARCHAR(500),
    error_details TEXT
) AS $$
DECLARE
    config_record webhook_configurations%ROWTYPE;
BEGIN
    -- Récupérer la configuration
    SELECT * INTO config_record
    FROM webhook_configurations
    WHERE name = webhook_name;

    -- Vérifier si la configuration existe
    IF config_record IS NULL THEN
        RETURN QUERY SELECT 
            false::BOOLEAN, 
            NULL::VARCHAR(500), 
            'Configuration non trouvée'::TEXT;
        RETURN;
    END IF;

    -- Vérifier si la configuration est active
    IF NOT config_record.is_active THEN
        RETURN QUERY SELECT 
            false::BOOLEAN, 
            NULL::VARCHAR(500), 
            'Configuration inactive'::TEXT;
        RETURN;
    END IF;

    -- Vérifier le statut des erreurs
    IF config_record.error_count >= config_record.max_retries THEN
        -- Si nous avons une URL de backup, l'utiliser
        IF config_record.backup_url IS NOT NULL THEN
            RETURN QUERY SELECT 
                true::BOOLEAN,
                config_record.backup_url::VARCHAR(500),
                ''::TEXT;
        ELSE
            RETURN QUERY SELECT 
                false::BOOLEAN,
                NULL::VARCHAR(500),
                'Service temporairement indisponible'::TEXT;
        END IF;
        RETURN;
    END IF;

    -- Configuration normale et fonctionnelle
    RETURN QUERY SELECT 
        true::BOOLEAN,
        config_record.url::VARCHAR(500),
        ''::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour réinitialiser les erreurs
CREATE OR REPLACE FUNCTION reset_webhook_configuration(webhook_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    UPDATE webhook_configurations
    SET 
        error_count = 0,
        error_message = null,
        last_error_timestamp = null,
        is_active = true,
        last_check_timestamp = CURRENT_TIMESTAMP,
        metadata = jsonb_set(
            metadata,
            '{status}',
            '"healthy"'::jsonb
        )
    WHERE name = webhook_name
    RETURNING 1 INTO affected_rows;

    RETURN affected_rows > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insérer la configuration initiale pour le chatbot
INSERT INTO webhook_configurations (
    name,
    url,
    backup_url,
    description,
    is_active,
    max_retries,
    retry_delay_base,
    health_check_interval,
    metadata
) VALUES (
    'n8n_chatbot_agent',
    'https://n8n-dw1u.onrender.com/webhook-test/chat',
    'https://backup-n8n-dw1u.onrender.com/webhook-test/chat',
    'Configuration du webhook pour l''agent chatbot N8N',
    true,
    3,
    2000,
    300,
    jsonb_build_object(
        'version', '1.0.0',
        'status', 'healthy',
        'created_by', 'system',
        'last_reset', CURRENT_TIMESTAMP
    )
)
ON CONFLICT (name) DO UPDATE
SET
    url = EXCLUDED.url,
    backup_url = EXCLUDED.backup_url,
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    metadata = EXCLUDED.metadata;