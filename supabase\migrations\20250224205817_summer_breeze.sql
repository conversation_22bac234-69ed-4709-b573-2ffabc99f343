-- Drop existing function if it exists
DROP FUNCTION IF EXISTS check_webhook_configuration(text);

-- Create improved webhook configuration check function
CREATE OR REPLACE FUNCTION check_webhook_configuration(webhook_name TEXT)
RETURNS TABLE (
    is_available boolean,
    current_url varchar(500),
    error_details text
) AS $$
DECLARE
    config_record webhook_configurations%ROWTYPE;
BEGIN
    -- Get webhook record
    SELECT * INTO config_record
    FROM webhook_configurations
    WHERE name = webhook_name
    AND is_active = true;

    -- Check if webhook exists and is properly configured
    IF config_record IS NULL THEN
        RETURN QUERY SELECT 
            false::boolean, 
            NULL::varchar(500), 
            'Configuration non disponible'::text;
        RETURN;
    END IF;

    -- Return status based on webhook health
    RETURN QUERY
    SELECT 
        true::boolean,
        CASE 
            WHEN config_record.error_count >= config_record.max_retries AND config_record.backup_url IS NOT NULL 
            THEN config_record.backup_url::varchar(500)
            ELSE config_record.url::varchar(500)
        END,
        ''::text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;