import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { supabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

type Appointment = {
  id: string;
  professional: {
    id: string;
    type: 'doctor' | 'lawyer';
    speciality: string;
    user: {
      first_name: string;
      last_name: string;
    };
  };
  date_time: string;
  status: string;
  type: string;
  notes: string | null;
  duration?: number;
  metadata?: any; // Pour stocker des informations supplémentaires comme la localisation
};

export default function AppointmentDetailsScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const params = useLocalSearchParams();
  const appointmentId = params.id as string;

  const [appointment, setAppointment] = useState<Appointment | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadAppointmentDetails();
  }, [appointmentId]);

  // Vérifier si une chaîne est un UUID valide
  const isValidUUID = (uuid: string) => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  };

  const loadAppointmentDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      // Vérifier si l'ID est un UUID valide
      if (!isValidUUID(appointmentId)) {
        // Si ce n'est pas un UUID valide, c'est probablement un ID de test
        // Créer un rendez-vous factice pour les démonstrations
        const dummyAppointment = {
          id: appointmentId,
          date_time: new Date().toISOString(),
          status: 'confirmed',
          type: 'video',
          notes: 'Ceci est un rendez-vous de démonstration.',
          duration: 30,
          metadata: { location: 'Centre médical Bomoko, Kinshasa' },
          professional: {
            id: 'demo-professional-id',
            type: 'doctor' as 'doctor' | 'lawyer',
            speciality: 'Psychologie',
            user: {
              first_name: 'Jean',
              last_name: 'Mbaka'
            }
          }
        };

        setAppointment(dummyAppointment as Appointment);
        setLoading(false);
        return;
      }

      // Utiliser une approche en deux étapes pour récupérer les détails du rendez-vous
      const { data: appointmentData, error: appointmentError } = await supabase
        .from('appointments')
        .select(`
          id,
          date_time,
          status,
          type,
          notes,
          duration,
          metadata,
          professional_id
        `)
        .eq('id', appointmentId)
        .single();

      if (appointmentError) throw appointmentError;

      // Récupérer les détails du professionnel
      const { data: professionalData, error: professionalError } = await supabase
        .from('professionals')
        .select(`
          id,
          type,
          speciality,
          user_id
        `)
        .eq('id', appointmentData.professional_id)
        .single();

      if (professionalError) throw professionalError;

      // Récupérer les détails de l'utilisateur (professionnel)
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select(`
          first_name,
          last_name
        `)
        .eq('id', professionalData.user_id)
        .single();

      if (userError) throw userError;

      // Combiner les données
      const data = {
        ...appointmentData,
        professional: {
          id: professionalData.id,
          type: professionalData.type,
          speciality: professionalData.speciality,
          user: userData
        }
      };

      if (appointmentError) throw appointmentError;

      // Les données sont déjà formatées correctement, pas besoin de transformation supplémentaire
      const formattedData = data as Appointment;

      setAppointment(formattedData);
    } catch (err) {
      console.error('Error loading appointment details:', err);
      setError('Erreur lors du chargement des détails du rendez-vous');
    } finally {
      setLoading(false);
    }
  };

  const formatAppointmentDate = (date: string) => {
    try {
      return format(new Date(date), 'PPP à HH:mm', { locale: fr });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date non disponible';
    }
  };

  const handleCancelAppointment = async () => {
    Alert.alert(
      'Annuler le rendez-vous',
      'Êtes-vous sûr de vouloir annuler ce rendez-vous?',
      [
        {
          text: 'Non',
          style: 'cancel',
        },
        {
          text: 'Oui, annuler',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);

              const { error } = await supabase
                .from('appointments')
                .update({ status: 'cancelled' })
                .eq('id', appointmentId);

              if (error) throw error;

              Alert.alert('Succès', 'Le rendez-vous a été annulé avec succès.');
              loadAppointmentDetails();
            } catch (err) {
              console.error('Error cancelling appointment:', err);
              Alert.alert('Erreur', 'Une erreur est survenue lors de l\'annulation du rendez-vous.');
            } finally {
              setLoading(false);
            }
          },
        },
      ]
    );
  };

  const handleStartVideoCall = () => {
    if (appointment?.type === 'video') {
      router.push({
        pathname: '/(app)/appointments/video-call',
        params: { id: appointmentId }
      });
    } else {
      Alert.alert('Information', 'Ce rendez-vous n\'est pas prévu en format vidéo.');
    }
  };

  if (error) {
    return (
      <View style={[styles.container, styles.centerContent, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.error, { color: theme.colors.error }]}>
          {error}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
          onPress={loadAppointmentDetails}>
          <Text style={styles.retryButtonText}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (loading || !appointment) {
    return (
      <View style={[styles.container, styles.centerContent, { backgroundColor: theme.colors.background }]}>
        <Text style={{ color: theme.colors.text }}>
          Chargement des détails...
        </Text>
      </View>
    );
  }

  const canCancel = appointment.status === 'pending' || appointment.status === 'confirmed';
  const canStartVideoCall = appointment.type === 'video' && appointment.status === 'confirmed';
  const isPast = new Date(appointment.date_time) < new Date();

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      contentContainerStyle={styles.contentContainer}
    >
      <View style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <View style={styles.cardHeader}>
          <View>
            <Text style={[styles.cardTitle, { color: theme.colors.text }]}>
              Rendez-vous avec {appointment.professional.user.first_name} {appointment.professional.user.last_name}
            </Text>
            <Text style={[styles.cardSubtitle, { color: theme.colors.gray[600] }]}>
              {appointment.professional.type === 'doctor' ? 'Médecin' : 'Avocat'}{appointment.professional.speciality ? ` - ${appointment.professional.speciality}` : ''}
            </Text>
          </View>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(appointment.status) }]}>
            <Text style={styles.statusText}>{getStatusText(appointment.status)}</Text>
          </View>
        </View>

        <View style={styles.detailsSection}>
          <View style={styles.detailRow}>
            <Ionicons name="calendar-outline" size={20} color={theme.colors.primary} />
            <Text style={[styles.detailText, { color: theme.colors.text }]}>
              {formatAppointmentDate(appointment.date_time)}
            </Text>
          </View>

          {appointment.duration && (
            <View style={styles.detailRow}>
              <Ionicons name="time-outline" size={20} color={theme.colors.primary} />
              <Text style={[styles.detailText, { color: theme.colors.text }]}>
                Durée: {appointment.duration} minutes
              </Text>
            </View>
          )}

          <View style={styles.detailRow}>
            <Ionicons
              name={appointment.type === 'video' ? 'videocam-outline' : 'person-outline'}
              size={20}
              color={theme.colors.primary}
            />
            <Text style={[styles.detailText, { color: theme.colors.text }]}>
              {appointment.type === 'video' ? 'Consultation vidéo' : 'Consultation en personne'}
            </Text>
          </View>

          {appointment.metadata?.location && (
            <View style={styles.detailRow}>
              <Ionicons name="location-outline" size={20} color={theme.colors.primary} />
              <Text style={[styles.detailText, { color: theme.colors.text }]}>
                {appointment.metadata.location}
              </Text>
            </View>
          )}
        </View>

        {appointment.notes && (
          <View style={[styles.notesSection, { backgroundColor: theme.colors.gray[100] }]}>
            <Text style={[styles.notesTitle, { color: theme.colors.text }]}>Notes</Text>
            <Text style={[styles.notesText, { color: theme.colors.gray[700] }]}>
              {appointment.notes}
            </Text>
          </View>
        )}

        <View style={styles.actionButtons}>
          {canCancel && !isPast && (
            <TouchableOpacity
              style={[styles.cancelButton, { backgroundColor: theme.colors.error }]}
              onPress={handleCancelAppointment}
            >
              <Ionicons name="close-circle-outline" size={20} color="#fff" />
              <Text style={styles.buttonText}>Annuler</Text>
            </TouchableOpacity>
          )}

          {canStartVideoCall && !isPast && (
            <TouchableOpacity
              style={[styles.videoButton, { backgroundColor: theme.colors.primary }]}
              onPress={handleStartVideoCall}
            >
              <Ionicons name="videocam-outline" size={20} color="#fff" />
              <Text style={styles.buttonText}>Rejoindre l'appel vidéo</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </ScrollView>
  );
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'confirmed':
      return '#4CAF50'; // green
    case 'pending':
      return '#FFC107'; // amber
    case 'cancelled':
      return '#F44336'; // red
    case 'completed':
      return '#2196F3'; // blue
    default:
      return '#9E9E9E'; // grey
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'confirmed':
      return 'Confirmé';
    case 'pending':
      return 'En attente';
    case 'cancelled':
      return 'Annulé';
    case 'completed':
      return 'Terminé';
    default:
      return status;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  card: {
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  cardSubtitle: {
    fontSize: 14,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  detailsSection: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  detailText: {
    marginLeft: 10,
    fontSize: 15,
  },
  notesSection: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  notesTitle: {
    fontSize: 15,
    fontWeight: '600',
    marginBottom: 6,
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 8,
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4,
  },
  videoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4,
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
    marginLeft: 8,
  },
  error: {
    marginBottom: 15,
    textAlign: 'center',
  },
  retryButton: {
    padding: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});