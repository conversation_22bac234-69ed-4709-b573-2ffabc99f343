import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Platform,
  Animated,
  Pressable,
} from 'react-native';
import { useTheme } from '../../context/theme';
import { useAuth } from '../../context/auth';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { supabase } from '../../lib/supabase';
import * as Location from 'expo-location';

interface ProfileHeaderProps {
  compact?: boolean;
}

export default function ProfileHeader({ compact = false }: ProfileHeaderProps) {
  const { theme } = useTheme();
  const { user } = useAuth();
  const [profile, setProfile] = useState<any>(null);
  const [address, setAddress] = useState<string | null>(null);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(!compact);
  const fadeAnim = React.useRef(new Animated.Value(1)).current;
  const locationWatchRef = useRef<Location.LocationSubscription | null>(null);

  useEffect(() => {
    if (user) {
      loadProfile();
      startLocationTracking();
    }

    // Nettoyage lors du démontage du composant
    return () => {
      if (locationWatchRef.current) {
        locationWatchRef.current.remove();
      }
    };
  }, [user]);

  async function loadProfile() {
    try {
      if (!user) return;
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Erreur lors du chargement du profil:', error);
      } else if (data) {
        setProfile(data);
      }
    } catch (error) {
      console.error('Erreur inattendue:', error);
    } finally {
      setLoading(false);
    }
  }

  const startLocationTracking = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setErrorMsg('Accès à la localisation refusé');
        return;
      }

      // Arrêter l'abonnement précédent s'il existe
      if (locationWatchRef.current) {
        locationWatchRef.current.remove();
      }

      // Démarrer un nouvel abonnement pour suivre la position en temps réel
      locationWatchRef.current = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.Balanced,
          distanceInterval: 10, // Mise à jour tous les 10 mètres
          timeInterval: 5000,   // Ou toutes les 5 secondes
        },
        (location) => {
          updateAddress(location.coords);
        }
      );
    } catch (error) {
      console.error('Erreur de localisation:', error);
      setErrorMsg('Impossible d\'obtenir la localisation');
    }
  };

  const updateAddress = async (coords: Location.LocationObjectCoords) => {
    try {
      const addressResponse = await Location.reverseGeocodeAsync({
        latitude: coords.latitude,
        longitude: coords.longitude
      });

      if (addressResponse && addressResponse.length > 0) {
        const loc = addressResponse[0];

        // Construction d'une adresse plus complète
        const addressParts = [
          loc.name,                   // Nom du lieu (si disponible)
          loc.streetNumber,           // Numéro de rue
          loc.street,                 // Nom de la rue/avenue
          loc.district || loc.subregion, // Quartier ou sous-région
          loc.city,                   // Ville
          loc.region,                 // Région/Province
          loc.country                 // Pays
        ];

        // Filtrer les parties vides et les joindre
        const formattedAddress = addressParts
          .filter(Boolean)
          .join(', ');

        setAddress(formattedAddress || 'Adresse inconnue');
        setErrorMsg(null);
      } else {
        setAddress('Position approximative');
      }
    } catch (error) {
      console.error('Erreur de géocodage:', error);
      setAddress('Position approximative');
    }
  };

  const getDisplayName = () => {
    if (!profile) return 'Utilisateur';

    if (profile.first_name && profile.last_name) {
      return `${profile.first_name} ${profile.last_name}`;
    } else if (profile.first_name) {
      return profile.first_name;
    } else if (user?.email) {
      return user.email.split('@')[0];
    }

    return 'Utilisateur';
  };

  const getAvatarUrl = () => {
    if (profile?.avatar_url) {
      return { uri: `${profile.avatar_url}?t=${new Date().getTime()}` };
    }
    return require('../../assets/images/profile.png');
  };

  const toggleExpanded = () => {
    if (compact) {
      Animated.timing(fadeAnim, {
        toValue: expanded ? 0 : 1,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setExpanded(!expanded);
      });
    }
  };

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: theme.colors.cardBackground,
        shadowColor: theme.dark ? 'rgba(0,0,0,0.5)' : 'rgba(0,0,0,0.15)',
        borderBottomColor: theme.colors.cardBorder,
        borderBottomWidth: 1
      }
    ]}>
      <View style={styles.appTitleContainer}>
        <Text style={[
          styles.appTitle,
          {
            color: theme.colors.primary,
            textShadowColor: 'rgba(0, 0, 0, 0.1)',
            textShadowOffset: { width: 1, height: 1 },
            textShadowRadius: 1
          }
        ]}>
          BOMOKO MOBILE
        </Text>
      </View>
      <View
        style={[
          styles.headerContainer,
          { backgroundColor: theme.colors.cardBackground },
          compact && styles.compactHeader
        ]}
      >
        <View style={styles.profileContainer}>
          <TouchableOpacity
            style={styles.avatarContainer}
            onPress={() => router.push('/profile/edit')}
          >
            <Image
              source={getAvatarUrl()}
              style={[styles.avatar, { borderColor: theme.colors.cardBorder }]}
            />
            <View style={[styles.statusIndicator, { borderColor: theme.colors.cardBackground }]} />
          </TouchableOpacity>

          <View style={styles.userInfoContainer}>
            <Text style={[
              styles.userName,
              { color: theme.colors.text }
            ]} numberOfLines={1}>
              {getDisplayName()}
            </Text>
          </View>

          {compact && (
            <Pressable
              onPress={toggleExpanded}
              style={[
                styles.expandButton,
                {
                  backgroundColor: theme.colors.primary + '20',
                  borderColor: theme.colors.primary + '30'
                }
              ]}
            >
              <Ionicons
                name={expanded ? "chevron-up" : "chevron-down"}
                size={20}
                color={theme.colors.primary}
              />
            </Pressable>
          )}
        </View>

        {(!compact || expanded) && (
          <Animated.View style={[
            styles.locationWrapper,
            { opacity: fadeAnim }
          ]}>
            <View style={styles.locationOuterContainer}>
              {address ? (
                <View style={[
                  styles.locationContainer,
                  {
                    backgroundColor: theme.colors.primary + '15',
                    borderColor: theme.colors.primary + '20'
                  }
                ]}>
                  <Ionicons name="location-outline" size={16} color={theme.colors.primary} />
                  <Text
                    style={[
                      styles.locationText,
                      { color: theme.colors.text }
                    ]}
                    numberOfLines={2}
                    ellipsizeMode="tail"
                  >
                    {address}
                  </Text>
                </View>
              ) : errorMsg ? (
                <View style={[
                  styles.locationContainer,
                  {
                    backgroundColor: theme.colors.error + '15',
                    borderColor: theme.colors.error + '20'
                  }
                ]}>
                  <Ionicons name="warning" size={16} color={theme.colors.error} />
                  <Text
                    style={[
                      styles.locationText,
                      { color: theme.colors.error }
                    ]}
                    numberOfLines={1}
                  >
                    {errorMsg}
                  </Text>
                </View>
              ) : (
                <View style={[
                  styles.locationContainer,
                  {
                    backgroundColor: theme.colors.primary + '15',
                    borderColor: theme.colors.primary + '20'
                  }
                ]}>
                  <Ionicons name="locate" size={16} color={theme.colors.primary} />
                  <Text
                    style={[
                      styles.locationText,
                      { color: theme.colors.text }
                    ]}
                    numberOfLines={1}
                  >
                    Localisation en cours...
                  </Text>
                </View>
              )}

              <TouchableOpacity
                style={[
                  styles.refreshButton,
                  {
                    backgroundColor: theme.colors.primary + '15',
                    borderColor: theme.colors.primary + '20'
                  }
                ]}
                onPress={startLocationTracking}
              >
                <Ionicons name="refresh" size={16} color={theme.colors.primary} />
              </TouchableOpacity>
            </View>
          </Animated.View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 10,
    marginBottom: 8,
    width: '100%',
    zIndex: 10, // S'assurer que le header est au-dessus des autres éléments
  },
  appTitleContainer: {
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 5 : 2,
    paddingBottom: 0,
  },
  appTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    letterSpacing: 1,
    textTransform: 'uppercase',
    textAlign: 'center',
  },
  headerContainer: {
    paddingTop: Platform.OS === 'ios' ? 5 : 3,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  compactHeader: {
    paddingTop: Platform.OS === 'ios' ? 3 : 2,
    paddingBottom: 15,
    paddingHorizontal: 16, // Réduire légèrement les marges horizontales en mode compact
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  avatarContainer: {
    marginRight: 15,
    position: 'relative',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 3,
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
  },
  userInfoContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  userName: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: 0.2,
  },
  locationWrapper: {
    marginTop: 5,
  },
  locationOuterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 16,
    paddingVertical: 8, // Réduire légèrement la hauteur
    paddingHorizontal: 12, // Réduire légèrement les marges horizontales
    borderWidth: 1,
    minHeight: 36, // Hauteur minimale pour assurer la cohérence
    maxWidth: '100%', // S'assurer que le conteneur ne déborde pas
  },
  locationText: {
    fontSize: 14,
    lineHeight: 20,
    marginLeft: 10,
    flex: 1,
    fontWeight: '500',
  },
  refreshButton: {
    marginLeft: 10,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  expandButton: {
    padding: 8,
    borderRadius: 20,
    marginLeft: 10,
    borderWidth: 1,
  },
});



