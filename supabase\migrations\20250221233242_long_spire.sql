/*
  # Update Supabase permissions

  1. Changes
    - Add more granular RLS policies for webhook_urls table
    - Add policies for webhook health monitoring
    - Ensure proper access control for webhook status checks
    
  2. Security
    - Enable RLS on all tables
    - Add specific policies for authenticated users
    - Add specific policies for webhook status checks
*/

-- Drop existing policies
DROP POLICY IF EXISTS "webhook_urls_read_policy" ON webhook_urls;
DROP POLICY IF EXISTS "webhook_urls_update_policy" ON webhook_urls;

-- Create new policies for webhook_urls
CREATE POLICY "Allow webhook status check for authenticated users"
    ON webhook_urls
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "Allow webhook health updates for authenticated users"
    ON webhook_urls
    FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Create policy for webhook health logs
CREATE POLICY "Allow health log access for authenticated users"
    ON webhook_health_logs
    FOR SELECT
    TO authenticated
    USING (true);

-- Create policy for webhook access logs
CREATE POLICY "Allow access log creation for authenticated users"
    ON webhook_access_logs
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

-- Create policy for chat messages
CREATE POLICY "Users can access their own chat messages"
    ON chat_messages
    FOR ALL
    TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Ensure RLS is enabled on all tables
ALTER TABLE webhook_urls ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhook_health_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhook_access_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;