-- <PERSON>reate function to handle SOS events with proper error handling
CREATE OR REPLACE FUNCTION handle_sos_event(
    p_user_id uuid,
    p_latitude double precision,
    p_longitude double precision,
    p_accuracy double precision DEFAULT NULL,
    p_metadata jsonb DEFAULT '{}'::jsonb
)
RETURNS TABLE (
    event_id uuid,
    status text,
    message text
) AS $$
DECLARE
    v_sos_event_id uuid;
    v_webhook_config record;
BEGIN
    -- Start transaction
    BEGIN
        -- Create SOS event
        SELECT * INTO v_sos_event_id FROM start_sos_event(
            p_latitude,
            p_longitude,
            p_accuracy,
            p_metadata
        );

        -- Get webhook configuration
        SELECT * INTO v_webhook_config FROM check_webhook_configuration('n8n_emergency_webhook');

        IF NOT v_webhook_config.is_available THEN
            RAISE EXCEPTION 'Service webhook non disponible: %', v_webhook_config.error_details;
        END IF;

        -- Return success
        RETURN QUERY
        SELECT 
            v_sos_event_id,
            'success'::text,
            'Alerte SOS activée avec succès'::text;

    EXCEPTION WHEN OTHERS THEN
        -- Log error and return failure
        RETURN QUERY
        SELECT 
            v_sos_event_id,
            'error'::text,
            'Erreur lors de l''activation du SOS: ' || SQLERRM;
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update SOS event status
CREATE OR REPLACE FUNCTION update_sos_event_status(
    p_event_id uuid,
    p_status text,
    p_metadata jsonb DEFAULT NULL
)
RETURNS boolean AS $$
BEGIN
    UPDATE sos_events
    SET 
        status = p_status,
        metadata = CASE 
            WHEN p_metadata IS NOT NULL 
            THEN metadata || p_metadata 
            ELSE metadata 
        END,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = p_event_id
    AND user_id = auth.uid();

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;