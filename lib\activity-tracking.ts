import { supabase } from '../utils/supabase';
import { Platform } from 'react-native';
import * as Device from 'expo-device';
import NetInfo from '@react-native-community/netinfo';

// Types pour les activités
export type ActivityType =
  | 'login'
  | 'logout'
  | 'signup'
  | 'profile_update'
  | 'forum_view'
  | 'forum_post'
  | 'forum_reply'
  | 'chatbot_interaction'
  | 'appointment_created'
  | 'appointment_updated'
  | 'appointment_cancelled'
  | 'alert_created'
  | 'alert_responded'
  | 'document_uploaded'
  | 'document_viewed'
  | 'resource_accessed'
  | 'assessment_completed'
  | 'location_updated'
  | 'notification_received'
  | 'notification_clicked'
  | 'page_view'
  | 'map_interaction'
  | 'sos_triggered'
  | 'sos_cancelled'
  | 'ngo_viewed'
  | 'ngo_contacted'
  | 'search_performed'
  | 'settings_changed'
  | 'media_played'
  | 'link_clicked'
  | 'feature_used';

// Interface pour les données d'activité
export interface ActivityData {
  [key: string]: any;
}

// Interface pour les métadonnées
export interface ActivityMetadata {
  device?: string;
  os?: string;
  osVersion?: string;
  appVersion?: string;
  networkType?: string;
  screenName?: string;
  [key: string]: any;
}

// File d'attente pour les activités en cas de déconnexion
let activityQueue: Array<{
  type: ActivityType;
  data: ActivityData;
  metadata: ActivityMetadata;
  timestamp: number;
}> = [];

// Indicateur de connexion réseau
let isConnected = true;

// Indicateur pour savoir si la table user_activities existe
let userActivitiesTableExists = true;

// Initialiser l'écouteur de connexion réseau
NetInfo.addEventListener(state => {
  const wasConnected = isConnected;
  isConnected = state.isConnected ?? false;

  // Si la connexion est rétablie, essayer d'envoyer les activités en file d'attente
  if (!wasConnected && isConnected) {
    processActivityQueue();
  }
});

/**
 * Traite la file d'attente des activités
 */
async function processActivityQueue() {
  if (!isConnected || activityQueue.length === 0 || !userActivitiesTableExists) return;

  // Copier la file d'attente et la vider
  const queueToProcess = [...activityQueue];
  activityQueue = [];

  // Traiter chaque activité
  for (const activity of queueToProcess) {
    try {
      await recordActivity(
        activity.type,
        activity.data,
        activity.metadata,
        false // Ne pas mettre en file d'attente en cas d'échec
      );
    } catch (error) {
      console.error('Erreur lors du traitement de l\'activité en file d\'attente:', error);
      // Remettre l'activité dans la file d'attente si elle date de moins de 24h
      const isRecent = Date.now() - activity.timestamp < 24 * 60 * 60 * 1000;
      if (isRecent) {
        activityQueue.push(activity);
      }
    }
  }
}

/**
 * Enregistre une activité utilisateur
 * @param type Type d'activité
 * @param data Données spécifiques à l'activité
 * @param customMetadata Métadonnées personnalisées
 * @param queueIfOffline Mettre en file d'attente si hors ligne
 * @returns ID de l'activité ou null en cas d'erreur
 */
export async function recordActivity(
  type: ActivityType,
  data: ActivityData = {},
  customMetadata: ActivityMetadata = {},
  queueIfOffline: boolean = true
): Promise<string | null> {
  // Si nous savons déjà que la table n'existe pas, ne pas essayer d'enregistrer
  if (!userActivitiesTableExists) {
    console.warn('Table user_activities inexistante, activité non enregistrée');
    return null;
  }

  try {
    // Vérifier si l'utilisateur est connecté
    const { data: session } = await supabase.auth.getSession();
    if (!session.session) {
      console.warn('Tentative d\'enregistrement d\'activité sans session utilisateur');
      return null;
    }

    // Collecter les métadonnées de base
    const baseMetadata: ActivityMetadata = {
      device: Device.deviceName || 'Unknown',
      os: Platform.OS,
      osVersion: Platform.Version.toString(),
      appVersion: '1.0.0', // À remplacer par la version réelle de l'app
      timestamp: new Date().toISOString(),
      ...customMetadata
    };

    // Si hors ligne et queueIfOffline est activé, mettre en file d'attente
    if (!isConnected && queueIfOffline) {
      activityQueue.push({
        type,
        data,
        metadata: baseMetadata,
        timestamp: Date.now()
      });
      console.log(`Activité "${type}" mise en file d'attente (hors ligne)`);
      return null;
    }
    
    // Enregistrer l'activité via la fonction RPC
    const { data: result, error } = await supabase.rpc(
      'record_user_activity',
      {
        activity_type_param: type,
        activity_data_param: data,
        metadata_param: baseMetadata
      }
    );
    
    if (error) {
      // Vérifier si l'erreur est due à l'absence de la table
      if (error.message && error.message.includes('relation "user_activities" does not exist')) {
        console.warn('La table user_activities n\'existe pas dans la base de données');
        userActivitiesTableExists = false;
        return null;
      }
      
      console.error('Erreur lors de l\'enregistrement de l\'activité:', error);
      
      // Mettre en file d'attente si demandé
      if (queueIfOffline) {
        activityQueue.push({
          type,
          data,
          metadata: baseMetadata,
          timestamp: Date.now()
        });
        console.log(`Activité "${type}" mise en file d'attente (erreur)`);
      }
      
      return null;
    }
    
    return result;
  } catch (error: any) {
    // Vérifier si l'erreur est due à l'absence de la table
    if (error.message && error.message.includes('relation "user_activities" does not exist')) {
      console.warn('La table user_activities n\'existe pas dans la base de données');
      userActivitiesTableExists = false;
      return null;
    }
    
    console.error('Exception lors de l\'enregistrement de l\'activité:', error);
    
    // Mettre en file d'attente si demandé
    if (queueIfOffline) {
      activityQueue.push({
        type,
        data,
        metadata: customMetadata,
        timestamp: Date.now()
      });
      console.log(`Activité "${type}" mise en file d'attente (exception)`);
    }
    
    return null;
  }
}