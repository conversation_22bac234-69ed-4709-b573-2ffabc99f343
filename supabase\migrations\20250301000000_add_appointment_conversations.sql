-- Add appointment_conversations table to track AI conversations for appointments
CREATE TABLE IF NOT EXISTS appointment_conversations (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id text NOT NULL UNIQUE,
    request_id text NOT NULL,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    appointment_id uuid REFERENCES appointments(id) ON DELETE SET NULL,
    status text NOT NULL CHECK (status IN ('active', 'completed', 'cancelled')) DEFAULT 'active',
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Add appointment_conversation_messages table to store messages
CREATE TABLE IF NOT EXISTS appointment_conversation_messages (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id uuid REFERENCES appointment_conversations(id) ON DELETE CASCADE,
    message_type text NOT NULL CHECK (message_type IN ('user', 'agent', 'system')),
    content text NOT NULL,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamptz DEFAULT now()
);

-- Enable RLS on the tables
ALTER TABLE appointment_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointment_conversation_messages ENABLE ROW LEVEL SECURITY;

-- Create policies for appointment_conversations
CREATE POLICY "Users can view their own appointment conversations"
    ON appointment_conversations
    FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own appointment conversations"
    ON appointment_conversations
    FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own appointment conversations"
    ON appointment_conversations
    FOR UPDATE
    TO authenticated
    USING (user_id = auth.uid());

-- Create policies for appointment_conversation_messages
CREATE POLICY "Users can view messages from their conversations"
    ON appointment_conversation_messages
    FOR SELECT
    TO authenticated
    USING (
        conversation_id IN (
            SELECT id FROM appointment_conversations
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert messages to their conversations"
    ON appointment_conversation_messages
    FOR INSERT
    TO authenticated
    WITH CHECK (
        conversation_id IN (
            SELECT id FROM appointment_conversations
            WHERE user_id = auth.uid()
        )
    );

-- Create function to record a message in a conversation
CREATE OR REPLACE FUNCTION record_conversation_message(
    p_conversation_id text,
    p_message_type text,
    p_content text,
    p_metadata jsonb DEFAULT '{}'::jsonb
)
RETURNS uuid AS $$
DECLARE
    v_conversation_uuid uuid;
    v_message_id uuid;
BEGIN
    -- Get the conversation UUID from the conversation_id
    SELECT id INTO v_conversation_uuid
    FROM appointment_conversations
    WHERE conversation_id = p_conversation_id;
    
    -- If conversation doesn't exist, return null
    IF v_conversation_uuid IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Insert the message
    INSERT INTO appointment_conversation_messages (
        conversation_id,
        message_type,
        content,
        metadata
    ) VALUES (
        v_conversation_uuid,
        p_message_type,
        p_content,
        p_metadata
    )
    RETURNING id INTO v_message_id;
    
    -- Update the conversation's updated_at timestamp
    UPDATE appointment_conversations
    SET updated_at = now()
    WHERE id = v_conversation_uuid;
    
    RETURN v_message_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to create a new conversation
CREATE OR REPLACE FUNCTION create_appointment_conversation(
    p_conversation_id text,
    p_request_id text,
    p_metadata jsonb DEFAULT '{}'::jsonb
)
RETURNS uuid AS $$
DECLARE
    v_conversation_id uuid;
BEGIN
    -- Insert the conversation
    INSERT INTO appointment_conversations (
        conversation_id,
        request_id,
        user_id,
        status,
        metadata
    ) VALUES (
        p_conversation_id,
        p_request_id,
        auth.uid(),
        'active',
        p_metadata
    )
    RETURNING id INTO v_conversation_id;
    
    RETURN v_conversation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to link a conversation to an appointment
CREATE OR REPLACE FUNCTION link_conversation_to_appointment(
    p_conversation_id text,
    p_appointment_id uuid
)
RETURNS boolean AS $$
DECLARE
    v_conversation_uuid uuid;
BEGIN
    -- Get the conversation UUID from the conversation_id
    SELECT id INTO v_conversation_uuid
    FROM appointment_conversations
    WHERE conversation_id = p_conversation_id
    AND user_id = auth.uid();
    
    -- If conversation doesn't exist or doesn't belong to the user, return false
    IF v_conversation_uuid IS NULL THEN
        RETURN false;
    END IF;
    
    -- Update the conversation with the appointment_id
    UPDATE appointment_conversations
    SET 
        appointment_id = p_appointment_id,
        status = 'completed',
        updated_at = now()
    WHERE id = v_conversation_uuid;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to complete a conversation
CREATE OR REPLACE FUNCTION complete_appointment_conversation(
    p_conversation_id text
)
RETURNS boolean AS $$
DECLARE
    v_conversation_uuid uuid;
BEGIN
    -- Get the conversation UUID from the conversation_id
    SELECT id INTO v_conversation_uuid
    FROM appointment_conversations
    WHERE conversation_id = p_conversation_id
    AND user_id = auth.uid();
    
    -- If conversation doesn't exist or doesn't belong to the user, return false
    IF v_conversation_uuid IS NULL THEN
        RETURN false;
    END IF;
    
    -- Update the conversation status
    UPDATE appointment_conversations
    SET 
        status = 'completed',
        updated_at = now()
    WHERE id = v_conversation_uuid;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to cancel a conversation
CREATE OR REPLACE FUNCTION cancel_appointment_conversation(
    p_conversation_id text
)
RETURNS boolean AS $$
DECLARE
    v_conversation_uuid uuid;
BEGIN
    -- Get the conversation UUID from the conversation_id
    SELECT id INTO v_conversation_uuid
    FROM appointment_conversations
    WHERE conversation_id = p_conversation_id
    AND user_id = auth.uid();
    
    -- If conversation doesn't exist or doesn't belong to the user, return false
    IF v_conversation_uuid IS NULL THEN
        RETURN false;
    END IF;
    
    -- Update the conversation status
    UPDATE appointment_conversations
    SET 
        status = 'cancelled',
        updated_at = now()
    WHERE id = v_conversation_uuid;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for updated_at
CREATE TRIGGER update_appointment_conversations_updated_at
    BEFORE UPDATE ON appointment_conversations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
