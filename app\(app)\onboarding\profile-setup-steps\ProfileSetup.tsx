import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { useProfileStore } from './store';
import { PersonalInfoStep } from './PersonalInfoStep';
import { AiProfilingStep } from './AiProfilingStep';
import { ProfilePictureStep } from './ProfilePictureStep';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  withDelay,
  SlideInRight,
  FadeIn,
  interpolate
} from 'react-native-reanimated';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

export function ProfileSetup() {
  const { currentStep } = useProfileStore();
  const progressAnim = useSharedValue(0);

  // Animation de la barre de progression
  useEffect(() => {
    progressAnim.value = withTiming(progressPercentage(), { duration: 600 });
  }, [currentStep]);

  // Affiche l'étape en cours avec animation
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <Animated.View
            entering={SlideInRight.duration(400)}
            style={styles.animatedStep}
          >
            <PersonalInfoStep />
          </Animated.View>
        );
      case 2:
        return (
          <Animated.View
            entering={SlideInRight.duration(400)}
            style={styles.animatedStep}
          >
            <AiProfilingStep />
          </Animated.View>
        );
      case 3:
        return (
          <Animated.View
            entering={SlideInRight.duration(400)}
            style={styles.animatedStep}
          >
            <ProfilePictureStep />
          </Animated.View>
        );
      default:
        return (
          <Animated.View
            entering={SlideInRight.duration(400)}
            style={styles.animatedStep}
          >
            <PersonalInfoStep />
          </Animated.View>
        );
    }
  };

  // Calcule le pourcentage de progression
  const progressPercentage = () => {
    return ((currentStep - 1) / 2) * 100;
  };

  // Style animé pour la barre de progression
  const progressAnimStyle = useAnimatedStyle(() => {
    return {
      width: `${progressAnim.value}%`,
    };
  });

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={true}
      alwaysBounceVertical={true}
      bounces={true}
      scrollEnabled={true}
      keyboardShouldPersistTaps="handled"
    >
      <Animated.View
        style={styles.card}
        entering={FadeIn.duration(800)}
      >
        <View style={styles.header}>
          <Animated.Text
            style={styles.title}
            entering={FadeIn.duration(600).delay(200)}
          >
            Configuration de votre profil
          </Animated.Text>

          <Animated.Text
            style={styles.subtitle}
            entering={FadeIn.duration(600).delay(400)}
          >
            {currentStep === 1 && "Étape 1 : Informations personnelles"}
            {currentStep === 2 && "Étape 2 : Évaluation personnalisée"}
            {currentStep === 3 && "Étape 3 : Photo de profil"}
          </Animated.Text>

          <View style={styles.progressBarContainer}>
            <Animated.View
              style={[styles.progressBar, progressAnimStyle]}
            />
          </View>

          <View style={styles.stepsContainer}>
            <View style={styles.stepItem}>
              <View style={[styles.stepCircle, currentStep >= 1 ? styles.activeStepCircle : {}]}>
                {currentStep > 1 ? (
                  <Ionicons name="checkmark" size={14} color="#fff" />
                ) : (
                  <Text style={styles.stepNumber}>1</Text>
                )}
              </View>
              <Text style={currentStep >= 1 ? styles.activeStep : styles.inactiveStep}>
                Information
              </Text>
            </View>

            <View style={styles.stepConnector} />

            <View style={styles.stepItem}>
              <View style={[styles.stepCircle, currentStep >= 2 ? styles.activeStepCircle : {}]}>
                {currentStep > 2 ? (
                  <Ionicons name="checkmark" size={14} color="#fff" />
                ) : (
                  <Text style={styles.stepNumber}>2</Text>
                )}
              </View>
              <Text style={currentStep >= 2 ? styles.activeStep : styles.inactiveStep}>
                Profilage
              </Text>
            </View>

            <View style={styles.stepConnector} />

            <View style={styles.stepItem}>
              <View style={[styles.stepCircle, currentStep >= 3 ? styles.activeStepCircle : {}]}>
                <Text style={styles.stepNumber}>3</Text>
              </View>
              <Text style={currentStep >= 3 ? styles.activeStep : styles.inactiveStep}>
                Photo
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.stepContainer}>
          {renderStep()}
        </View>

        <BlurView intensity={5} tint="light" style={styles.footer}>
          <Text style={styles.footerText}>
            Vos données sont strictement confidentielles et protégées conformément à notre politique de confidentialité.
          </Text>
        </BlurView>
      </Animated.View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    backgroundColor: 'transparent'
  },
  contentContainer: {
    flexGrow: 1,
    width: '100%',
    paddingVertical: 20,
    paddingHorizontal: 16,
    alignItems: 'center',
    paddingBottom: 100,
  },
  card: {
    width: '100%',
    maxWidth: 550,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  header: {
    paddingHorizontal: 24,
    paddingVertical: 24,
    backgroundColor: '#ffffff'
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#0066ff',
    textAlign: 'center',
    marginBottom: 12
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 28
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: '#e8eef7',
    borderRadius: 8,
    overflow: 'hidden',
    marginTop: 16,
    marginBottom: 24
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#0066ff',
    borderRadius: 8
  },
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 8,
    marginBottom: 8
  },
  stepItem: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#e8eef7',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8
  },
  activeStepCircle: {
    backgroundColor: '#0066ff',
  },
  stepNumber: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#666'
  },
  stepConnector: {
    height: 2,
    backgroundColor: '#e8eef7',
    flex: 1,
    marginHorizontal: 8
  },
  activeStep: {
    color: '#0066ff',
    fontWeight: '600',
    fontSize: 14
  },
  inactiveStep: {
    color: '#aaa',
    fontSize: 14
  },
  stepContainer: {
    width: '100%',
    backgroundColor: '#fafafa',
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  animatedStep: {
    width: '100%'
  },
  footer: {
    padding: 20,
    backgroundColor: 'rgba(255,255,255,0.7)',
  },
  footerText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    lineHeight: 18
  }
});

// Export par défaut pour Expo Router
export default ProfileSetup;