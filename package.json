{"name": "bomoko-mobile", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "npx expo start", "build:web": "npx expo export --platform web", "lint": "npx expo lint", "start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "npx expo start --web"}, "dependencies": {"@expo/metro-config": "~0.19.0", "@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/cli-server-api": "^12.3.3", "@react-native-community/datetimepicker": "8.2.0", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "^11.3.1", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@react-navigation/stack": "^7.0.0", "@rnmapbox/maps": "^10.1.37", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.49.1", "base64-arraybuffer": "^1.0.2", "base64-js": "^1.5.1", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "expo": "^52.0.38", "expo-auth-session": "~6.0.3", "expo-av": "~15.0.2", "expo-background-fetch": "^13.0.5", "expo-blur": "~14.0.3", "expo-build-properties": "~0.13.2", "expo-constants": "~17.0.8", "expo-device": "~7.0.2", "expo-document-picker": "~13.0.3", "expo-file-system": "~18.0.11", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image": "~2.0.6", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-location": "~18.0.7", "expo-navigation-bar": "^4.0.9", "expo-router": "^4.0.18", "expo-secure-store": "~14.0.1", "expo-sensors": "~14.0.2", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-system-ui": "~4.0.8", "expo-task-manager": "^12.0.5", "expo-web-browser": "~14.0.2", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "^0.76.7", "react-native-animatable": "^1.4.0", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "^1.11.0", "react-native-maps": "1.18.0", "react-native-maps-directions": "^1.9.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-toast-message": "^2.2.1", "react-native-url-polyfill": "^2.0.0", "react-native-volume-manager": "^2.0.8", "react-native-web": "~0.19.10", "react-native-webview": "13.12.5", "zod": "^3.22.4", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.23.9", "@types/react": "~18.2.45", "@types/uuid": "^10.0.0", "react-native-clean-project": "^4.0.3", "supabase": "^2.19.5", "typescript": "^5.3.3"}}