/*
  # Update webhook configuration

  1. Changes
    - Update webhook URLs for the chatbot agent
    - Set health check parameters
    - Reset error counters
  
  2. Security
    - Uses direct URL updates without encryption
    - Maintains existing security policies
*/

-- Update webhook URLs with correct configuration
UPDATE webhook_urls
SET 
    url = 'https://n8n-dw1u.onrender.com/webhook/chat',
    backup_url = 'https://n8n-dw1u.onrender.com/webhook/chat',
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    consecutive_failures = 0,
    last_successful_response = CURRENT_TIMESTAMP,
    health_check_enabled = true,
    max_retries = 3,
    retry_delay_base = 2000,
    health_check_interval = 300
WHERE name = 'n8n_chatbot_agent';

-- Create or replace the webhook status check function
CREATE OR REPLACE FUNCTION check_webhook_status(webhook_name TEXT)
RETURNS TABLE (
    is_available boolean,
    current_url text,
    error_details text
) AS $$
DECLARE
    webhook_record webhook_urls%ROWTYPE;
BEGIN
    -- Get webhook record
    SELECT * INTO webhook_record
    FROM webhook_urls
    WHERE name = webhook_name;

    -- Check if webhook exists
    IF webhook_record IS NULL THEN
        RETURN QUERY SELECT false, NULL::text, 'Webhook not found'::text;
        RETURN;
    END IF;

    -- Return status based on webhook health
    RETURN QUERY
    SELECT 
        webhook_record.is_active AND webhook_record.error_count < webhook_record.max_retries,
        CASE 
            WHEN webhook_record.error_count >= webhook_record.max_retries AND webhook_record.backup_url IS NOT NULL 
            THEN webhook_record.backup_url
            ELSE webhook_record.url
        END,
        webhook_record.error_message;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;