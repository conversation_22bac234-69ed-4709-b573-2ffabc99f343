/*
  # Update webhook configuration and status check

  1. Changes
    - Update webhook URLs and configuration
    - Add webhook status check function
    - Remove encryption requirements
    - Add health check improvements
  
  2. Security
    - Function is marked as SECURITY DEFINER
    - Only authenticated users can access webhook data
*/

-- Update webhook URLs with correct configuration
UPDATE webhook_urls
SET 
    url = 'https://n8n-dw1u.onrender.com/webhook-test/chat',
    backup_url = 'https://backup-n8n-dw1u.onrender.com/webhook-test/chat',
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    health_check_enabled = true,
    max_retries = 3,
    retry_delay_base = 2000,
    health_check_interval = 300
WHERE name = 'n8n_chatbot_agent';

-- Create or replace the webhook status check function
CREATE OR REPLACE FUNCTION check_webhook_status(webhook_name TEXT)
RETURNS TABLE (
    is_available boolean,
    current_url text,
    error_details text
) AS $$
DECLARE
    webhook_record webhook_urls%ROWTYPE;
BEGIN
    -- Get webhook record
    SELECT * INTO webhook_record
    FROM webhook_urls
    WHERE name = webhook_name;

    -- Check if webhook exists and is active
    IF webhook_record IS NULL THEN
        RETURN QUERY SELECT false, NULL::text, 'Webhook not found'::text;
        RETURN;
    END IF;

    -- Return status based on webhook health
    RETURN QUERY
    SELECT 
        webhook_record.is_active AND webhook_record.error_count < webhook_record.max_retries,
        CASE 
            WHEN webhook_record.error_count >= webhook_record.max_retries AND webhook_record.backup_url IS NOT NULL 
            THEN webhook_record.backup_url
            ELSE webhook_record.url
        END,
        webhook_record.error_message;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to reset webhook errors
CREATE OR REPLACE FUNCTION reset_webhook_errors(webhook_name TEXT)
RETURNS void AS $$
BEGIN
    UPDATE webhook_urls
    SET 
        error_count = 0,
        error_message = null,
        last_check_timestamp = CURRENT_TIMESTAMP,
        is_active = true
    WHERE name = webhook_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;