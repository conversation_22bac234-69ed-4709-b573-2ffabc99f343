-- Update webhook configuration with optimized settings
UPDATE webhook_configurations
SET 
    url = 'https://n8n-dw1u.onrender.com/webhook-test/emergency',
    backup_url = 'https://n8n-dw1u.onrender.com/webhook-test/emergency',
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    retry_delay_base = 1000, -- Reduced retry delay
    health_check_interval = 60, -- More frequent health checks
    metadata = jsonb_build_object(
        'version', '1.0.1',
        'status', 'healthy',
        'last_update', CURRENT_TIMESTAMP,
        'webhook_type', 'emergency',
        'timeout', 10000, -- 10 second timeout
        'content_type', 'application/json',
        'expected_response_format', jsonb_build_object(
            'status', 'string',
            'message', 'string',
            'event_id', 'string'
        )
    )
WHERE name = 'n8n_emergency_webhook';

-- Create optimized function for webhook status check
CREATE OR REPLACE FUNCTION check_webhook_configuration(webhook_name TEXT)
RETURNS TABLE (
    is_available boolean,
    current_url varchar(500),
    error_details text
) AS $$
DECLARE
    config_record webhook_configurations%ROWTYPE;
    cache_key text;
    cache_timeout interval := interval '1 minute';
BEGIN
    -- Get webhook record with caching
    SELECT * INTO config_record
    FROM webhook_configurations
    WHERE name = webhook_name
    AND (
        last_check_timestamp IS NULL OR
        last_check_timestamp < CURRENT_TIMESTAMP - interval '1 minute' OR
        NOT is_active
    );

    -- Quick return if webhook is inactive
    IF NOT config_record.is_active THEN
        RETURN QUERY SELECT 
            false::boolean, 
            NULL::varchar(500), 
            'Service temporairement indisponible'::text;
        RETURN;
    END IF;

    -- Return appropriate URL based on error count
    RETURN QUERY
    SELECT 
        (config_record.error_count < config_record.max_retries)::boolean,
        CASE 
            WHEN config_record.error_count >= config_record.max_retries AND config_record.backup_url IS NOT NULL 
            THEN config_record.backup_url
            ELSE config_record.url
        END::varchar(500),
        COALESCE(config_record.error_message, '')::text;

EXCEPTION WHEN OTHERS THEN
    -- Handle unexpected errors
    RETURN QUERY SELECT 
        false::boolean, 
        NULL::varchar(500), 
        'Erreur interne du service'::text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to reset webhook errors
CREATE OR REPLACE FUNCTION reset_webhook_errors(webhook_name TEXT)
RETURNS void AS $$
BEGIN
    UPDATE webhook_configurations
    SET 
        error_count = 0,
        error_message = null,
        is_active = true,
        last_check_timestamp = CURRENT_TIMESTAMP
    WHERE name = webhook_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;