import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Alert,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { supabase } from '../../../lib/supabase';
import { Ionicons } from '@expo/vector-icons';
import { WebView } from 'react-native-webview';

type Appointment = {
  id: string;
  professional: {
    id: string;
    type: 'doctor' | 'lawyer';
    user: {
      first_name: string;
      last_name: string;
    };
  };
  date_time: string;
};

export default function VideoCallScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const params = useLocalSearchParams();
  const appointmentId = params.id as string;

  const [appointment, setAppointment] = useState<Appointment | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [callUrl, setCallUrl] = useState<string | null>(null);

  useEffect(() => {
    loadAppointment();
    return () => {
      // Cleanup video call resources
      if (callUrl) {
        endCall();
      }
    };
  }, [appointmentId]);

  // Vérifier si une chaîne est un UUID valide
  const isValidUUID = (uuid: string) => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  };

  const loadAppointment = async () => {
    try {
      setLoading(true);
      setError(null);

      // Vérifier si l'ID est un UUID valide
      if (!isValidUUID(appointmentId)) {
        // Si ce n'est pas un UUID valide, c'est probablement un ID de test
        // Créer un rendez-vous factice pour les démonstrations
        const dummyAppointment = {
          id: appointmentId,
          date_time: new Date().toISOString(),
          professional: {
            id: 'demo-professional-id',
            type: 'doctor' as 'doctor' | 'lawyer',
            user: {
              first_name: 'Jean',
              last_name: 'Mbaka'
            }
          }
        };

        setAppointment(dummyAppointment as Appointment);
        initializeCall();
        setLoading(false);
        return;
      }

      // Utiliser une approche en deux étapes pour récupérer les détails du rendez-vous
      const { data: appointmentData, error: appointmentError } = await supabase
        .from('appointments')
        .select(`
          id,
          date_time,
          professional_id
        `)
        .eq('id', appointmentId)
        .single();

      if (appointmentError) throw appointmentError;

      // Récupérer les détails du professionnel
      const { data: professionalData, error: professionalError } = await supabase
        .from('professionals')
        .select(`
          id,
          type,
          user_id
        `)
        .eq('id', appointmentData.professional_id)
        .single();

      if (professionalError) throw professionalError;

      // Récupérer les détails de l'utilisateur (professionnel)
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select(`
          first_name,
          last_name
        `)
        .eq('id', professionalData.user_id)
        .single();

      if (userError) throw userError;

      // Combiner les données
      const formattedData = {
        id: appointmentData.id,
        date_time: appointmentData.date_time,
        professional: {
          id: professionalData.id,
          type: professionalData.type,
          user: userData
        }
      } as Appointment;

      setAppointment(formattedData);
      initializeCall();
    } catch (err) {
      console.error('Error loading appointment:', err);
      setError('Erreur lors du chargement du rendez-vous');
    } finally {
      setLoading(false);
    }
  };

  const initializeCall = async () => {
    try {
      // Initialize video call (implement your video call provider integration here)
      // For example, using Agora.io or Twilio
      setCallUrl('https://meet.jit.si/' + appointmentId);
    } catch (err) {
      console.error('Error initializing call:', err);
      setError('Erreur lors de l\'initialisation de l\'appel');
    }
  };

  const endCall = async () => {
    try {
      // End video call and cleanup resources
      setCallUrl(null);

      // Update appointment status if needed
      const { error: updateError } = await supabase.rpc('update_appointment_status', {
        p_appointment_id: appointmentId,
        p_status: 'completed'
      });

      if (updateError) throw updateError;

      router.replace('/appointments');
    } catch (err) {
      console.error('Error ending call:', err);
      Alert.alert('Erreur', 'Une erreur est survenue lors de la fin de l\'appel');
    }
  };

  const handleEndCall = () => {
    Alert.alert(
      'Terminer l\'appel',
      'Voulez-vous vraiment terminer cet appel ?',
      [
        {
          text: 'Annuler',
          style: 'cancel',
        },
        {
          text: 'Terminer',
          style: 'destructive',
          onPress: endCall,
        },
      ]
    );
  };

  if (error) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={[styles.error, { color: theme.colors.error }]}>
          {error}
        </Text>
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
          onPress={loadAppointment}>
          <Text style={styles.retryButtonText}>Réessayer</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (loading || !appointment) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={{ color: theme.colors.text }}>
          Chargement de l'appel...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={[styles.title, { color: theme.colors.text }]}>
            {`${appointment.professional.user.first_name} ${appointment.professional.user.last_name}`}
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.gray[600] }]}>
            {appointment.professional.type === 'doctor' ? 'Médecin' : 'Avocat'}
          </Text>
        </View>
        <TouchableOpacity
          style={[styles.endCallButton, { backgroundColor: theme.colors.error }]}
          onPress={handleEndCall}>
          <Ionicons name="call" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {callUrl && (
        <WebView
          source={{ uri: callUrl }}
          style={styles.webview}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          mediaPlaybackRequiresUserAction={false}
          allowsInlineMediaPlayback={true}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 16,
  },
  endCallButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    transform: [{ rotate: '135deg' }],
  },
  webview: {
    flex: 1,
    backgroundColor: '#000',
  },
  error: {
    marginBottom: 15,
    textAlign: 'center',
  },
  retryButton: {
    padding: 10,
    paddingHorizontal: 20, borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});