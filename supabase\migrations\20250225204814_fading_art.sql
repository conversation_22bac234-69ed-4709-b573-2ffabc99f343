/*
  # Medical and Legal Appointments System

  1. New Tables
    - `professionals` - Doctors and lawyers
    - `appointments` - Appointment bookings
    - `appointment_reminders` - Reminder logs
    - `video_recordings` - Secure video session recordings

  2. Security
    - Enable RLS on all tables
    - Add policies for user access
    - Add policies for professional access
*/

-- Create professionals table
CREATE TABLE professionals (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    type text NOT NULL CHECK (type IN ('doctor', 'lawyer')),
    speciality text,
    license_number text,
    availability jsonb DEFAULT '[]'::jsonb,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create appointments table
CREATE TABLE appointments (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    professional_id uuid REFERENCES professionals(id) ON DELETE CASCADE,
    date_time timestamptz NOT NULL,
    duration integer NOT NULL DEFAULT 60, -- in minutes
    status text NOT NULL CHECK (status IN ('pending', 'confirmed', 'cancelled', 'completed')) DEFAULT 'pending',
    type text NOT NULL CHECK (type IN ('in_person', 'video')),
    notes text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now(),
    metadata jsonb DEFAULT '{}'::jsonb
);

-- Create appointment reminders table
CREATE TABLE appointment_reminders (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    appointment_id uuid REFERENCES appointments(id) ON DELETE CASCADE,
    type text NOT NULL CHECK (type IN ('email', 'sms')),
    scheduled_for timestamptz NOT NULL,
    status text NOT NULL CHECK (status IN ('pending', 'sent', 'failed')) DEFAULT 'pending',
    sent_at timestamptz,
    error_message text,
    created_at timestamptz DEFAULT now()
);

-- Create video recordings table
CREATE TABLE video_recordings (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    appointment_id uuid REFERENCES appointments(id) ON DELETE CASCADE,
    storage_path text NOT NULL,
    duration integer, -- in seconds
    size bigint, -- in bytes
    created_at timestamptz DEFAULT now(),
    metadata jsonb DEFAULT '{}'::jsonb,
    CONSTRAINT valid_storage_path CHECK (storage_path ~ '^appointments/')
);

-- Enable RLS
ALTER TABLE professionals ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE appointment_reminders ENABLE ROW LEVEL SECURITY;
ALTER TABLE video_recordings ENABLE ROW LEVEL SECURITY;

-- Policies for professionals
CREATE POLICY "Professionals can view own profile"
    ON professionals
    FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Professionals can update own profile"
    ON professionals
    FOR UPDATE
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Policies for appointments
CREATE POLICY "Users can view own appointments"
    ON appointments
    FOR SELECT
    TO authenticated
    USING (
        user_id = auth.uid() OR
        professional_id IN (
            SELECT id FROM professionals WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create appointments"
    ON appointments
    FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own appointments"
    ON appointments
    FOR UPDATE
    TO authenticated
    USING (
        user_id = auth.uid() OR
        professional_id IN (
            SELECT id FROM professionals WHERE user_id = auth.uid()
        )
    );

-- Policies for video recordings
CREATE POLICY "Access to video recordings"
    ON video_recordings
    FOR ALL
    TO authenticated
    USING (
        appointment_id IN (
            SELECT id FROM appointments
            WHERE user_id = auth.uid() OR
                  professional_id IN (
                      SELECT id FROM professionals WHERE user_id = auth.uid()
                  )
        )
    );

-- Functions
CREATE OR REPLACE FUNCTION create_appointment(
    p_professional_id uuid,
    p_date_time timestamptz,
    p_duration integer DEFAULT 60,
    p_type text DEFAULT 'video',
    p_notes text DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
    v_appointment_id uuid;
BEGIN
    -- Insert appointment
    INSERT INTO appointments (
        user_id,
        professional_id,
        date_time,
        duration,
        type,
        notes
    ) VALUES (
        auth.uid(),
        p_professional_id,
        p_date_time,
        p_duration,
        p_type,
        p_notes
    )
    RETURNING id INTO v_appointment_id;

    -- Create reminder for 24h before
    INSERT INTO appointment_reminders (
        appointment_id,
        type,
        scheduled_for
    ) VALUES (
        v_appointment_id,
        'email',
        p_date_time - interval '24 hours'
    );

    RETURN v_appointment_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update appointment status
CREATE OR REPLACE FUNCTION update_appointment_status(
    p_appointment_id uuid,
    p_status text,
    p_notes text DEFAULT NULL
)
RETURNS boolean AS $$
BEGIN
    UPDATE appointments
    SET 
        status = p_status,
        notes = COALESCE(p_notes, notes),
        updated_at = now()
    WHERE id = p_appointment_id
    AND (
        user_id = auth.uid() OR
        professional_id IN (
            SELECT id FROM professionals WHERE user_id = auth.uid()
        )
    );

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for updated_at
CREATE TRIGGER update_professionals_updated_at
    BEFORE UPDATE ON professionals
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_appointments_updated_at
    BEFORE UPDATE ON appointments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();