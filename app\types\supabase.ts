export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          first_name: string;
          last_name: string;
          avatar_url: string;
        };
      };
      forum_categories: {
        Row: {
          id: string;
          name: string;
          description: string;
          icon: string;
          color: string;
          slug: string;
          order_index: number;
        };
      };
      forum_topics: {
        Row: {
          id: string;
          title: string;
          content: string;
          category_id: string;
          user_id: string;
          created_at: string;
          updated_at: string;
          is_pinned: boolean;
          is_locked: boolean;
          is_anonymous: boolean;
        };
      };
      forum_replies: {
        Row: {
          id: string;
          topic_id: string;
          content: string;
          user_id: string;
          created_at: string;
          updated_at: string;
        };
      };
      forum_reactions: {
        Row: {
          id: string;
          target_id: string;
          target_type: string;
          reaction_type: string;
          user_id: string;
          created_at: string;
        };
      };
    };
  };
};

// Export par défaut pour Expo Router
export default Database;