/**
 * Agent AI de secours pour simuler les réponses de N8N
 * Utilisé lorsque le webhook N8N n'est pas disponible ou configuré
 */

// Types pour les messages et les demandes
interface AppointmentRequest {
  reason: string;
  type: string;
  dateTime: string;
  notes?: string;
}

interface ConversationRequest {
  action: string;
  conversation_id: string;
  request_id: string;
  user_id: string;
  user_email?: string;
  user_profile?: any;
  appointment_request?: AppointmentRequest;
  message?: string;
  input?: {
    message: string;
    preferredDateTime?: string;
    appointmentType?: string;
    conversationId?: string;
  };
}

interface AgentResponse {
  message: string;
  conversation_id: string;
  appointment_created?: boolean;
  appointment_details?: {
    id: string;
    date_time: string;
    type: string;
    location?: string;
    notes?: string;
    professional?: {
      id: string;
      title?: string;
      first_name: string;
      last_name: string;
      speciality?: string;
    };
  };
}

// Stockage des conversations en mémoire
const conversations: Record<string, {
  messages: { role: 'user' | 'assistant', content: string }[];
  state: 'initial' | 'collecting_info' | 'confirming' | 'completed';
  appointmentRequest: AppointmentRequest;
  questionCount: number;
}> = {};

/**
 * Traite une demande de conversation et renvoie une réponse
 */
export async function processFallbackRequest(request: ConversationRequest): Promise<AgentResponse> {
  // Extraire les informations de la demande
  const { action, conversation_id, message, input } = request;
  
  // Initialiser la réponse
  const response: AgentResponse = {
    message: '',
    conversation_id
  };
  
  // Traiter selon l'action
  if (action === 'initialize_conversation') {
    return handleInitializeConversation(request);
  } else if (action === 'send_message') {
    return handleSendMessage(request);
  } else if (action === 'cancel_conversation') {
    return {
      message: 'Conversation annulée',
      conversation_id
    };
  }
  
  // Action non reconnue
  return {
    message: 'Action non reconnue',
    conversation_id
  };
}

/**
 * Gère l'initialisation d'une conversation
 */
function handleInitializeConversation(request: ConversationRequest): AgentResponse {
  const { conversation_id, appointment_request } = request;
  
  // Stocker les informations de la demande
  conversations[conversation_id] = {
    messages: [],
    state: 'initial',
    appointmentRequest: appointment_request || {
      reason: '',
      type: 'medical',
      dateTime: new Date().toISOString()
    },
    questionCount: 0
  };
  
  // Générer un message de bienvenue
  const welcomeMessage = `Bonjour, je suis votre assistant pour la prise de rendez-vous. 
  
J'ai bien reçu votre demande concernant : "${appointment_request?.reason}".

Pouvez-vous me donner plus de détails sur votre situation afin que je puisse vous orienter vers le professionnel le plus adapté ?`;
  
  // Ajouter le message à l'historique
  conversations[conversation_id].messages.push({
    role: 'assistant',
    content: welcomeMessage
  });
  
  // Passer à l'état de collecte d'informations
  conversations[conversation_id].state = 'collecting_info';
  
  return {
    message: welcomeMessage,
    conversation_id
  };
}

/**
 * Gère l'envoi d'un message dans une conversation existante
 */
function handleSendMessage(request: ConversationRequest): AgentResponse {
  const { conversation_id, message, input } = request;
  const userMessage = message || input?.message || '';
  
  // Vérifier si la conversation existe
  if (!conversations[conversation_id]) {
    return {
      message: 'Conversation non trouvée. Veuillez initialiser une nouvelle conversation.',
      conversation_id
    };
  }
  
  // Récupérer l'état de la conversation
  const conversation = conversations[conversation_id];
  
  // Ajouter le message de l'utilisateur à l'historique
  conversation.messages.push({
    role: 'user',
    content: userMessage
  });
  
  // Traiter selon l'état de la conversation
  let responseMessage = '';
  
  if (conversation.state === 'collecting_info') {
    conversation.questionCount++;
    
    // Après 2 questions, proposer un rendez-vous
    if (conversation.questionCount >= 2) {
      conversation.state = 'confirming';
      
      // Déterminer le type de professionnel en fonction du type de rendez-vous
      const professionalType = conversation.appointmentRequest.type === 'medical' 
        ? 'médecin' 
        : 'avocat';
      
      // Déterminer une spécialité en fonction du motif
      const speciality = conversation.appointmentRequest.type === 'medical'
        ? 'Médecine générale'
        : 'Droit des victimes';
      
      // Formater la date et l'heure
      const dateTime = new Date(conversation.appointmentRequest.dateTime);
      const formattedDate = dateTime.toLocaleDateString('fr-FR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
      const formattedTime = dateTime.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
      });
      
      responseMessage = `Merci pour ces informations. Sur la base de ce que vous m'avez dit, je vous propose un rendez-vous avec un ${professionalType} spécialisé en ${speciality}.
      
Le rendez-vous pourrait avoir lieu le ${formattedDate} à ${formattedTime}.

Est-ce que cela vous convient ?`;
    } else {
      // Poser une question supplémentaire
      const questions = [
        "Pouvez-vous me préciser depuis quand vous rencontrez ce problème ?",
        "Avez-vous déjà consulté un professionnel pour ce sujet ?",
        "Préférez-vous une consultation en personne ou par vidéo ?",
        "Y a-t-il des jours ou des horaires qui vous conviendraient mieux ?"
      ];
      
      responseMessage = questions[Math.floor(Math.random() * questions.length)];
    }
  } else if (conversation.state === 'confirming') {
    // Vérifier si l'utilisateur confirme le rendez-vous
    const positiveResponses = ['oui', 'ok', 'bien', 'parfait', 'convient', 'd\'accord', 'entendu'];
    const isConfirming = positiveResponses.some(word => userMessage.toLowerCase().includes(word));
    
    if (isConfirming) {
      conversation.state = 'completed';
      
      // Créer un rendez-vous fictif
      const appointmentId = `app_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
      const professionalType = conversation.appointmentRequest.type === 'medical' 
        ? 'médecin' 
        : 'avocat';
      const speciality = conversation.appointmentRequest.type === 'medical'
        ? 'Médecine générale'
        : 'Droit des victimes';
      
      responseMessage = `Parfait ! J'ai créé un rendez-vous pour vous. Vous recevrez bientôt une confirmation par email et notification.`;
      
      // Retourner les détails du rendez-vous
      return {
        message: responseMessage,
        conversation_id,
        appointment_created: true,
        appointment_details: {
          id: appointmentId,
          date_time: conversation.appointmentRequest.dateTime,
          type: conversation.appointmentRequest.type,
          location: 'Centre Bomoko, Kinshasa',
          notes: conversation.appointmentRequest.notes,
          professional: {
            id: `prof_${Math.floor(Math.random() * 1000)}`,
            title: professionalType === 'médecin' ? 'Dr.' : 'Me.',
            first_name: professionalType === 'médecin' ? 'Jean' : 'Marie',
            last_name: professionalType === 'médecin' ? 'Mbaka' : 'Mutombo',
            speciality: speciality
          }
        }
      };
    } else {
      // L'utilisateur n'est pas satisfait de la proposition
      responseMessage = `Je comprends. Pouvez-vous me préciser ce qui ne vous convient pas ? Préférez-vous un autre jour, un autre horaire ou un autre type de professionnel ?`;
      
      // Revenir à l'état de collecte d'informations
      conversation.state = 'collecting_info';
    }
  } else {
    // État non reconnu, revenir à l'état initial
    conversation.state = 'initial';
    responseMessage = "Je suis désolé, mais j'ai perdu le fil de notre conversation. Pouvez-vous me rappeler comment je peux vous aider ?";
  }
  
  // Ajouter la réponse à l'historique
  conversation.messages.push({
    role: 'assistant',
    content: responseMessage
  });
  
  return {
    message: responseMessage,
    conversation_id
  };
}
