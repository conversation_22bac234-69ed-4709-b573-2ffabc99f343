/*
  # Fix webhook configuration and status check

  1. Changes
    - Updates webhook configuration with correct URLs and settings
    - Recreates webhook status check function with proper return types
    - Ensures proper type casting for all returned values

  2. Security
    - Function is marked as SECURITY DEFINER to ensure proper access control
*/

-- Drop existing function if it exists
DROP FUNCTION IF EXISTS check_webhook_status(text);

-- Create webhook status check function with proper return types
CREATE OR REPLACE FUNCTION check_webhook_status(webhook_name TEXT)
RETURNS TABLE (
    is_available boolean,
    current_url varchar(500),
    error_details text
) AS $$
DECLARE
    webhook_record webhook_urls%ROWTYPE;
BEGIN
    -- Get webhook record
    SELECT * INTO webhook_record
    FROM webhook_urls
    WHERE name = webhook_name;

    -- Check if webhook exists
    IF webhook_record IS NULL THEN
        RETURN QUERY SELECT 
            false::boolean, 
            NULL::varchar(500), 
            'Webhook not found'::text;
        RETURN;
    END IF;

    -- Return status based on webhook health
    RETURN QUERY
    SELECT 
        (webhook_record.is_active AND webhook_record.error_count < webhook_record.max_retries)::boolean,
        CASE 
            WHEN webhook_record.error_count >= webhook_record.max_retries AND webhook_record.backup_url IS NOT NULL 
            THEN webhook_record.backup_url::varchar(500)
            ELSE webhook_record.url::varchar(500)
        END,
        COALESCE(webhook_record.error_message, '')::text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;