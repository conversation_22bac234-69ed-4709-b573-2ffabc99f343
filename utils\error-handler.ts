// Error handling utilities for network and update-related issues
import { Alert, Platform } from 'react-native';

export interface NetworkError {
  type: 'network' | 'update' | 'timeout' | 'unknown';
  message: string;
  originalError?: Error;
  timestamp: Date;
}

export class ErrorHandler {
  private static instance: <PERSON><PERSON>r<PERSON>andler;
  private errorLog: NetworkError[] = [];

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle network-related errors including update failures
   */
  handleNetworkError(error: Error | string, context?: string): void {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const networkError: NetworkError = {
      type: this.categorizeError(errorMessage),
      message: errorMessage,
      originalError: typeof error === 'object' ? error : undefined,
      timestamp: new Date()
    };

    this.logError(networkError, context);
    this.handleErrorBasedOnType(networkError, context);
  }

  /**
   * Categorize error based on message content
   */
  private categorizeError(message: string): NetworkError['type'] {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('failed to download remote update') ||
        lowerMessage.includes('Failed to download remote update') ||
        lowerMessage.includes('update') ||
        lowerMessage.includes('expo updates') ||
        lowerMessage.includes('ioexception') ||
        lowerMessage.includes('java.io.ioexception')) {
      return 'update';
    }
    
    if (lowerMessage.includes('network') ||
        lowerMessage.includes('connection') ||
        lowerMessage.includes('timeout') ||
        lowerMessage.includes('fetch')) {
      return 'network';
    }
    
    if (lowerMessage.includes('timeout')) {
      return 'timeout';
    }
    
    return 'unknown';
  }

  /**
   * Handle error based on its type
   */
  private handleErrorBasedOnType(error: NetworkError, context?: string): void {
    switch (error.type) {
      case 'update':
        this.handleUpdateError(error, context);
        break;
      case 'network':
        this.handleNetworkConnectivityError(error, context);
        break;
      case 'timeout':
        this.handleTimeoutError(error, context);
        break;
      default:
        this.handleGenericError(error, context);
    }
  }

  /**
   * Handle update-related errors
   */
  private handleUpdateError(error: NetworkError, context?: string): void {
    console.warn('Update Error:', error.message, context ? `Context: ${context}` : '');
    
    // Don't show alerts for update errors in development
    if (__DEV__) {
      console.log('Update error suppressed in development mode');
      return;
    }

    // In production, show a user-friendly message
    Alert.alert(
      'Mise à jour',
      'Impossible de vérifier les mises à jour. L\'application continuera de fonctionner normalement.',
      [{ text: 'OK' }]
    );
  }

  /**
   * Handle network connectivity errors
   */
  private handleNetworkConnectivityError(error: NetworkError, context?: string): void {
    console.warn('Network Error:', error.message, context ? `Context: ${context}` : '');
    
    // Only show network errors for critical operations
    if (context && context.includes('critical')) {
      Alert.alert(
        'Erreur de connexion',
        'Vérifiez votre connexion internet et réessayez.',
        [{ text: 'OK' }]
      );
    }
  }

  /**
   * Handle timeout errors
   */
  private handleTimeoutError(error: NetworkError, context?: string): void {
    console.warn('Timeout Error:', error.message, context ? `Context: ${context}` : '');
    
    if (context && context.includes('critical')) {
      Alert.alert(
        'Délai d\'attente dépassé',
        'L\'opération a pris trop de temps. Veuillez réessayer.',
        [{ text: 'Réessayer' }]
      );
    }
  }

  /**
   * Handle generic errors
   */
  private handleGenericError(error: NetworkError, context?: string): void {
    console.warn('Generic Error:', error.message, context ? `Context: ${context}` : '');
    
    if (__DEV__) {
      console.error('Full error details:', error);
    }
  }

  /**
   * Log error for debugging
   */
  private logError(error: NetworkError, context?: string): void {
    this.errorLog.push(error);
    
    // Keep only last 50 errors
    if (this.errorLog.length > 50) {
      this.errorLog = this.errorLog.slice(-50);
    }

    // Log to console with context
    console.log(`[${error.type.toUpperCase()}] ${error.message}`, {
      context,
      timestamp: error.timestamp,
      platform: Platform.OS
    });
  }

  /**
   * Get recent errors for debugging
   */
  getRecentErrors(count: number = 10): NetworkError[] {
    return this.errorLog.slice(-count);
  }

  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }

  /**
   * Check if there are recent update errors
   */
  hasRecentUpdateErrors(withinMinutes: number = 5): boolean {
    const cutoff = new Date(Date.now() - withinMinutes * 60 * 1000);
    return this.errorLog.some(error => 
      error.type === 'update' && error.timestamp > cutoff
    );
  }
}

// Global error handler for unhandled promise rejections
export const setupGlobalErrorHandler = (): void => {
  const errorHandler = ErrorHandler.getInstance();

  // Handle unhandled promise rejections
  if (typeof global !== 'undefined' && global.HermesInternal) {
    // React Native with Hermes
    global.addEventListener?.('unhandledrejection', (event) => {
      if (event.reason) {
        errorHandler.handleNetworkError(event.reason, 'unhandled_promise_rejection');
      }
    });
  }

  // Handle React Native errors
  if (typeof ErrorUtils !== 'undefined') {
    const originalHandler = ErrorUtils.getGlobalHandler();
    ErrorUtils.setGlobalHandler((error, isFatal) => {
      errorHandler.handleNetworkError(error, isFatal ? 'fatal_error' : 'non_fatal_error');
      originalHandler(error, isFatal);
    });
  }
};

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();
