-- Create SOS notifications table
CREATE TABLE IF NOT EXISTS sos_notifications (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    sos_event_id uuid REFERENCES sos_events(id) ON DELETE CASCADE,
    type text NOT NULL CHECK (type IN ('location', 'audio', 'status')),
    status text NOT NULL CHECK (status IN ('pending', 'sent', 'failed')),
    recipient_type text NOT NULL CHECK (recipient_type IN ('telegram', 'webhook', 'email')),
    recipient text NOT NULL,
    content jsonb NOT NULL,
    sent_at timestamptz,
    error_message text,
    created_at timestamptz DEFAULT now(),
    metadata jsonb DEFAULT '{}'::jsonb
);

-- Enable RLS
ALTER TABLE sos_notifications ENABLE ROW LEVEL SECURITY;

-- Create policy for notifications
CREATE POLICY "Users can manage their own SOS notifications"
    ON sos_notifications
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM sos_events
            WHERE sos_events.id = sos_event_id
            AND sos_events.user_id = auth.uid()
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM sos_events
            WHERE sos_events.id = sos_event_id
            AND sos_events.user_id = auth.uid()
        )
    );

-- Create function to send SOS notification
CREATE OR REPLACE FUNCTION create_sos_notification(
    p_sos_event_id uuid,
    p_type text,
    p_recipient_type text,
    p_recipient text,
    p_content jsonb,
    p_metadata jsonb DEFAULT '{}'::jsonb
)
RETURNS uuid AS $$
DECLARE
    new_notification_id uuid;
BEGIN
    -- Verify user owns the SOS event
    IF NOT EXISTS (
        SELECT 1 FROM sos_events
        WHERE id = p_sos_event_id
        AND user_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'Unauthorized';
    END IF;

    -- Create notification
    INSERT INTO sos_notifications (
        sos_event_id,
        type,
        status,
        recipient_type,
        recipient,
        content,
        metadata
    ) VALUES (
        p_sos_event_id,
        p_type,
        'pending',
        p_recipient_type,
        p_recipient,
        p_content,
        p_metadata
    )
    RETURNING id INTO new_notification_id;

    RETURN new_notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;