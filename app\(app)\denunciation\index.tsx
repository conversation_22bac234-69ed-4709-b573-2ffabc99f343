import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Platform,
  Alert,
  Image,
  ActivityIndicator,
  Switch,
  KeyboardAvoidingView,
} from 'react-native';
import { router } from 'expo-router';
import { supabase } from '../../../lib/supabase';
import { useAuth } from '../../../context/auth';
import { useTheme } from '../../../context/theme';
import { useLocation } from '../../../context/location';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import * as FileSystem from 'expo-file-system';
import * as Location from 'expo-location';
import { Audio } from 'expo-av';
import { decode } from 'base64-arraybuffer';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import Animated, { FadeInUp } from 'react-native-reanimated';
import * as sweetAlert from '../../utils/sweetAlert';

// Types de violence disponibles
const violenceTypes = [
  { label: 'Physique', value: 'physique' },
  { label: 'Psychologique', value: 'psychologique' },
  { label: 'Sexuelle', value: 'sexuelle' },
  { label: 'Verbale', value: 'verbale' },
  { label: 'Économique', value: 'économique' },
];

// Type pour les médias
type MediaItem = {
  uri: string;
  type: 'image' | 'video' | 'audio';
  name: string;
  uploadProgress?: number;
  uploaded?: boolean;
  publicUrl?: string;
};

export default function DenunciationScreen() {
  const { user } = useAuth();
  const { theme } = useTheme();
  const [message, setMessage] = useState('');
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [location, setLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [locationAddress, setLocationAddress] = useState<string | null>(null);
  const [violenceType, setViolenceType] = useState<string | null>(null);
  const [isAnonymous, setIsAnonymous] = useState(true);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [locationPermission, setLocationPermission] = useState(false);

  // État pour l'enregistrement audio
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [showViolenceTypeSelector, setShowViolenceTypeSelector] = useState(false);

  // Demander les permissions nécessaires au chargement
  useEffect(() => {
    (async () => {
      // Demander la permission de localisation
      const { status: locationStatus } = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(locationStatus === 'granted');

      if (locationStatus === 'granted') {
        try {
          const location = await Location.getCurrentPositionAsync({});
          const coords = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          };
          setLocation(coords);

          // Récupérer l'adresse lisible
          const address = await getReadableAddress(coords);
          setLocationAddress(address);
        } catch (err) {
          console.error('Erreur lors de la récupération de la localisation:', err);
        }
      }

      // Demander la permission d'accès à la caméra
      await ImagePicker.requestCameraPermissionsAsync();

      // Demander la permission d'accès à la galerie
      await ImagePicker.requestMediaLibraryPermissionsAsync();

      // Demander la permission d'enregistrement audio
      await Audio.requestPermissionsAsync();
    })();
  }, []);

  // Fonction pour prendre une photo
  const takePhoto = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: [ImagePicker.MediaType.IMAGE],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];

        // Vérifier la taille de l'image
        const fileInfo = await FileSystem.getInfoAsync(asset.uri);
        if (fileInfo.size && fileInfo.size > 10 * 1024 * 1024) {
          Alert.alert('Fichier trop volumineux', 'L\'image ne doit pas dépasser 10 MB.');
          return;
        }

        // Ajouter l'image à la liste des médias
        setMediaItems(prev => [
          ...prev,
          {
            uri: asset.uri,
            type: 'image',
            name: `photo_${Date.now()}.jpg`
          }
        ]);
      }
    } catch (err) {
      console.error('Erreur lors de la prise de photo:', err);
      setError('Erreur lors de la prise de photo');
    }
  };

  // Fonction pour sélectionner une vidéo
  const pickVideo = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: [ImagePicker.MediaType.VIDEO],
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];

        // Vérifier la taille de la vidéo
        const fileInfo = await FileSystem.getInfoAsync(asset.uri);
        if (fileInfo.size && fileInfo.size > 50 * 1024 * 1024) {
          Alert.alert('Fichier trop volumineux', 'La vidéo ne doit pas dépasser 50 MB.');
          return;
        }

        // Ajouter la vidéo à la liste des médias
        setMediaItems(prev => [
          ...prev,
          {
            uri: asset.uri,
            type: 'video',
            name: `video_${Date.now()}.mp4`
          }
        ]);
      }
    } catch (err) {
      console.error('Erreur lors de la sélection de la vidéo:', err);
      setError('Erreur lors de la sélection de la vidéo');
    }
  };

  // Fonction pour démarrer/arrêter l'enregistrement audio
  const toggleAudioRecording = async () => {
    try {
      if (isRecording) {
        // Arrêter l'enregistrement
        setIsRecording(false);
        await stopRecording();
      } else {
        // Démarrer l'enregistrement
        await startRecording();
      }
    } catch (err) {
      console.error('Erreur lors de l\'enregistrement audio:', err);
      setError('Erreur lors de l\'enregistrement audio');
    }
  };

  // Fonction pour démarrer l'enregistrement audio
  const startRecording = async () => {
    try {
      // Configurer l'audio
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      // Créer un nouvel enregistrement avec des options personnalisées
      const recordingOptions = {
        ...Audio.RecordingOptionsPresets.HIGH_QUALITY,
        android: {
          ...Audio.RecordingOptionsPresets.HIGH_QUALITY.android,
          extension: '.m4a',
          outputFormat: Audio.AndroidOutputFormat.MPEG_4,
          audioEncoder: Audio.AndroidAudioEncoder.AAC,
        },
        ios: {
          ...Audio.RecordingOptionsPresets.HIGH_QUALITY.ios,
          extension: '.m4a',
          outputFormat: Audio.IOSOutputFormat.MPEG4AAC,
        }
      };

      const { recording } = await Audio.Recording.createAsync(recordingOptions);

      setRecording(recording);
      setIsRecording(true);
      setRecordingDuration(0);

      // Démarrer un timer pour suivre la durée
      const interval = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

      // Stocker l'intervalle dans une référence pour pouvoir le nettoyer
      recording._interval = interval;
    } catch (err) {
      console.error('Erreur lors du démarrage de l\'enregistrement:', err);
      setError('Impossible de démarrer l\'enregistrement');
    }
  };

  // Fonction pour arrêter l'enregistrement audio
  const stopRecording = async () => {
    if (!recording) return;

    try {
      // Arrêter le timer
      if (recording._interval) {
        clearInterval(recording._interval);
      }

      // Arrêter l'enregistrement
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();

      // Réinitialiser l'état d'enregistrement
      setRecording(null);
      setIsRecording(false);

      if (uri) {
        // Ajouter l'enregistrement à la liste des médias
        setMediaItems(prev => [
          ...prev,
          {
            uri,
            type: 'audio',
            name: `audio_${Date.now()}.m4a`
          }
        ]);
      }

      // Réinitialiser le mode audio
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
      });
    } catch (err) {
      console.error('Erreur lors de l\'arrêt de l\'enregistrement:', err);
      setError('Erreur lors de l\'arrêt de l\'enregistrement');
    }
  };

  // Fonction pour supprimer un média
  const removeMedia = (index: number) => {
    setMediaItems(prev => prev.filter((_, i) => i !== index));
  };

  // Fonction pour formater la durée d'enregistrement
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // Fonction pour vérifier la connexion réseau
  const checkNetworkConnection = async (): Promise<boolean> => {
    try {
      // Tenter une requête simple pour vérifier la connexion
      const response = await fetch('https://www.google.com', { method: 'HEAD', timeout: 5000 });
      return response.ok;
    } catch (error) {
      console.error('Erreur de connexion réseau:', error);
      return false;
    }
  };

  // Fonction pour télécharger un fichier vers Supabase Storage avec retry
  const uploadFile = async (item: MediaItem, retryCount = 0): Promise<string | null> => {
    try {
      // Vérifier la connexion réseau avant de tenter le téléchargement
      const isConnected = await checkNetworkConnection();
      if (!isConnected) {
        throw new Error('Pas de connexion Internet. Veuillez vérifier votre connexion et réessayer.');
      }

      // Lire le fichier en base64
      const fileBase64 = await FileSystem.readAsStringAsync(item.uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Générer un nom de fichier unique
      const fileExt = item.uri.split('.').pop();
      const fileName = `${uuidv4()}.${fileExt}`;
      const filePath = `${user?.id || 'anonymous'}/${fileName}`;

      // Convertir la base64 en ArrayBuffer pour le téléchargement
      const fileBuffer = decode(fileBase64);

      // Déterminer le type de contenu approprié
      let contentType = 'application/octet-stream';
      if (item.type === 'image') {
        contentType = `image/${fileExt === 'jpg' ? 'jpeg' : fileExt}`;
      } else if (item.type === 'video') {
        contentType = 'video/mp4';
      } else if (item.type === 'audio') {
        // Utiliser le bon MIME type en fonction de l'extension
        switch (fileExt) {
          case 'm4a':
            contentType = 'audio/x-m4a'; // Utiliser le MIME type supporté par le bucket
            break;
          case 'mp3':
            contentType = 'audio/mpeg';
            break;
          case 'wav':
            contentType = 'audio/wav';
            break;
          case 'aac':
            contentType = 'audio/aac';
            break;
          case 'ogg':
            contentType = 'audio/ogg';
            break;
          default:
            contentType = 'audio/x-m4a'; // Fallback pour les fichiers audio
        }
      }

      // Télécharger le fichier vers Supabase Storage
      const { data, error } = await supabase.storage
        .from('denunciation_media')
        .upload(filePath, fileBuffer, {
          contentType: contentType,
        });

      if (error) {
        console.error("Erreur lors du téléchargement:", error);

        // Si c'est une erreur réseau et qu'on n'a pas dépassé le nombre max de tentatives
        if (error.message?.includes('Network') && retryCount < 3) {
          console.log(`Nouvelle tentative de téléchargement (${retryCount + 1}/3)...`);
          // Attendre un peu avant de réessayer
          await new Promise(resolve => setTimeout(resolve, 2000));
          return uploadFile(item, retryCount + 1);
        }

        throw new Error(`Erreur lors du téléchargement: ${error.message}`);
      }

      // Récupérer l'URL publique du fichier téléchargé
      const { data: urlData } = supabase.storage
        .from('denunciation_media')
        .getPublicUrl(filePath);

      return urlData.publicUrl;
    } catch (err) {
      console.error('Erreur lors du téléchargement du fichier:', err);

      // Afficher un message d'erreur plus convivial
      const errorMessage = err instanceof Error ? err.message : 'Erreur inconnue';
      if (errorMessage.includes('Network request failed')) {
        setError('Problème de connexion Internet. Veuillez vérifier votre connexion et réessayer.');
      } else {
        setError(`Erreur lors du téléchargement: ${errorMessage}`);
      }

      return null;
    }
  };

  // Fonction pour obtenir l'adresse lisible à partir des coordonnées
  const getReadableAddress = async (coords: { latitude: number; longitude: number }): Promise<string | null> => {
    try {
      const addressResponse = await Location.reverseGeocodeAsync({
        latitude: coords.latitude,
        longitude: coords.longitude
      });

      if (addressResponse && addressResponse.length > 0) {
        const loc = addressResponse[0];

        // Construction d'une adresse plus complète
        const addressParts = [
          loc.name,                   // Nom du lieu (si disponible)
          loc.streetNumber,           // Numéro de rue
          loc.street,                 // Nom de la rue/avenue
          loc.district || loc.subregion, // Quartier ou sous-région
          loc.city,                   // Ville
          loc.region,                 // Région/Province
          loc.country                 // Pays
        ];

        // Filtrer les parties vides et les joindre
        const formattedAddress = addressParts
          .filter(Boolean)
          .join(', ');

        return formattedAddress || 'Adresse inconnue';
      }
      return null;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'adresse:', error);
      return null;
    }
  };

  // Fonction pour soumettre le formulaire avec retry
  const handleSubmit = async (retryCount = 0) => {
    // Validation de base
    if (!message.trim() && mediaItems.length === 0) {
      setError('Veuillez fournir un message ou des médias');
      return;
    }

    if (!violenceType) {
      setError('Veuillez sélectionner un type de violence');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      // Vérifier la connexion réseau
      const isConnected = await checkNetworkConnection();
      if (!isConnected) {
        throw new Error('Pas de connexion Internet. Veuillez vérifier votre connexion et réessayer.');
      }

      // Télécharger tous les fichiers médias
      const mediaUrls: string[] = [];
      const mediaTypes: string[] = [];

      for (const item of mediaItems) {
        const publicUrl = await uploadFile(item);
        if (publicUrl) {
          mediaUrls.push(publicUrl);
          mediaTypes.push(item.type);
        }
      }

      // Récupérer l'adresse lisible si les coordonnées sont disponibles
      let addressText = null;
      if (location) {
        addressText = await getReadableAddress(location);
      }

      // Créer l'enregistrement de dénonciation
      const { data, error } = await supabase
        .from('denunciations')
        .insert({
          user_id: isAnonymous ? null : user?.id,
          message: message.trim(),
          media_urls: mediaUrls,
          media_types: mediaTypes,
          latitude: location?.latitude,
          longitude: location?.longitude,
          address: addressText,
          anonymous: isAnonymous,
          violence_type: violenceType,
        })
        .select();

      if (error) {
        console.error('Erreur lors de l\'insertion dans la base de données:', error);

        // Si c'est une erreur réseau et qu'on n'a pas dépassé le nombre max de tentatives
        if ((error.message?.includes('Network') || error.message?.includes('network')) && retryCount < 3) {
          console.log(`Nouvelle tentative d'envoi (${retryCount + 1}/3)...`);
          // Attendre un peu avant de réessayer
          await new Promise(resolve => setTimeout(resolve, 2000));
          setSubmitting(false);
          return handleSubmit(retryCount + 1);
        }

        throw error;
      }

      // Afficher un message de succès
      sweetAlert.default.showSuccessAlert({
        title: 'Signalement envoyé',
        message: 'Votre signalement a été envoyé avec succès. Nous vous remercions pour votre contribution.'
      });

      // Rediriger vers la page de succès
      router.push('/denunciation/success');
    } catch (err) {
      console.error('Erreur lors de la soumission:', err);

      // Afficher un message d'erreur plus convivial
      const errorMessage = err instanceof Error ? err.message : 'Erreur inconnue';
      if (errorMessage.includes('Network request failed') || errorMessage.includes('connexion Internet')) {
        setError('Problème de connexion Internet. Veuillez vérifier votre connexion et réessayer.');
      } else {
        setError('Erreur lors de la soumission du signalement. Veuillez réessayer.');
      }

      sweetAlert.default.showErrorAlert({
        title: 'Erreur',
        message: errorMessage.includes('Network') || errorMessage.includes('connexion') ?
          'Problème de connexion Internet. Veuillez vérifier votre connexion et réessayer.' :
          'Une erreur est survenue lors de l\'envoi de votre signalement. Veuillez réessayer.'
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
    >
      <ScrollView
        style={[styles.container, { backgroundColor: theme.colors.background }]}
        contentContainerStyle={styles.contentContainer}
      >
        {error && (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: theme.colors.error }]}>
              {error}
            </Text>
          </View>
        )}

        <Animated.View
          style={styles.formCard}
          entering={FadeInUp.duration(800).delay(200)}
        >
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Décrivez la situation
          </Text>

          <TextInput
            style={[
              styles.messageInput,
              {
                backgroundColor: theme.colors.surface,
                color: theme.colors.text,
                borderColor: theme.colors.border
              }
            ]}
            placeholder="Décrivez la situation de violence que vous souhaitez signaler..."
            placeholderTextColor={theme.colors.gray[400]}
            multiline
            numberOfLines={6}
            textAlignVertical="top"
            value={message}
            onChangeText={setMessage}
            editable={!submitting}
          />

          <View style={styles.mediaButtonsContainer}>
            <TouchableOpacity
              style={[styles.mediaButton, { backgroundColor: theme.colors.bomoko.blue }]}
              onPress={takePhoto}
              disabled={submitting}
            >
              <Ionicons name="camera" size={24} color="white" />
              <Text style={styles.mediaButtonText}>Photo</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.mediaButton,
                { backgroundColor: isRecording ? theme.colors.error : theme.colors.bomoko.purple }
              ]}
              onPress={toggleAudioRecording}
              disabled={submitting}
            >
              <Ionicons
                name={isRecording ? "stop-circle" : "mic"}
                size={24}
                color="white"
              />
              <Text style={styles.mediaButtonText}>
                {isRecording ? `${formatDuration(recordingDuration)}` : 'Audio'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.mediaButton, { backgroundColor: theme.colors.bomoko.pink }]}
              onPress={pickVideo}
              disabled={submitting}
            >
              <Ionicons name="videocam" size={24} color="white" />
              <Text style={styles.mediaButtonText}>Vidéo</Text>
            </TouchableOpacity>
          </View>

          {mediaItems.length > 0 && (
            <View style={styles.mediaPreviewContainer}>
              <Text style={[styles.mediaPreviewTitle, { color: theme.colors.text }]}>
                Médias sélectionnés ({mediaItems.length})
              </Text>

              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.mediaPreviewScroll}
              >
                {mediaItems.map((item, index) => (
                  <View key={index} style={styles.mediaPreviewItem}>
                    {item.type === 'image' ? (
                      <Image source={{ uri: item.uri }} style={styles.mediaPreviewImage} />
                    ) : (
                      <View style={[styles.mediaPreviewPlaceholder, {
                        backgroundColor: item.type === 'video'
                          ? theme.colors.bomoko.pink
                          : theme.colors.bomoko.purple
                      }]}>
                        <Ionicons
                          name={item.type === 'video' ? 'videocam' : 'musical-note'}
                          size={24}
                          color="white"
                        />
                      </View>
                    )}
                    <TouchableOpacity
                      style={styles.mediaRemoveButton}
                      onPress={() => removeMedia(index)}
                    >
                      <Ionicons name="close-circle" size={22} color={theme.colors.error} />
                    </TouchableOpacity>
                  </View>
                ))}
              </ScrollView>
            </View>
          )}
        </Animated.View>

        <Animated.View
          style={styles.formCard}
          entering={FadeInUp.duration(800).delay(400)}
        >
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>
            Détails supplémentaires
          </Text>

          <View style={styles.optionContainer}>
            <Text style={[styles.optionLabel, { color: theme.colors.text }]}>
              Type de violence
            </Text>
            <TouchableOpacity
              style={[
                styles.selector,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border
                }
              ]}
              onPress={() => setShowViolenceTypeSelector(!showViolenceTypeSelector)}
            >
              <Text style={[
                styles.selectorText,
                {
                  color: violenceType
                    ? theme.colors.text
                    : theme.colors.gray[400]
                }
              ]}>
                {violenceType
                  ? violenceTypes.find(t => t.value === violenceType)?.label
                  : 'Sélectionner un type de violence'}
              </Text>
              <Ionicons
                name={showViolenceTypeSelector ? "chevron-up" : "chevron-down"}
                size={20}
                color={theme.colors.gray[400]}
              />
            </TouchableOpacity>

            {showViolenceTypeSelector && (
              <View style={[
                styles.optionsDropdown,
                {
                  backgroundColor: theme.colors.surface,
                  borderColor: theme.colors.border
                }
              ]}>
                {violenceTypes.map((type) => (
                  <TouchableOpacity
                    key={type.value}
                    style={[
                      styles.optionItem,
                      violenceType === type.value && {
                        backgroundColor: theme.colors.primary + '20'
                      }
                    ]}
                    onPress={() => {
                      setViolenceType(type.value);
                      setShowViolenceTypeSelector(false);
                    }}
                  >
                    <Text style={[
                      styles.optionItemText,
                      { color: theme.colors.text }
                    ]}>
                      {type.label}
                    </Text>
                    {violenceType === type.value && (
                      <Ionicons name="checkmark" size={20} color={theme.colors.primary} />
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>

          <View style={styles.optionContainer}>
            <Text style={[styles.optionLabel, { color: theme.colors.text }]}>
              Localisation
            </Text>
            <View style={[
              styles.locationContainer,
              {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.border
              }
            ]}>
              {locationPermission ? (
                location ? (
                  <Text style={[styles.locationText, { color: theme.colors.text }]}>
                    {locationAddress ? locationAddress : 'Position GPS capturée'}
                  </Text>
                ) : (
                  <Text style={[styles.locationText, { color: theme.colors.warning }]}>
                    Impossible de récupérer la position
                  </Text>
                )
              ) : (
                <TouchableOpacity
                  style={styles.locationButton}
                  onPress={async () => {
                    const { status } = await Location.requestForegroundPermissionsAsync();
                    setLocationPermission(status === 'granted');

                    if (status === 'granted') {
                      try {
                        const location = await Location.getCurrentPositionAsync({});
                        const coords = {
                          latitude: location.coords.latitude,
                          longitude: location.coords.longitude,
                        };
                        setLocation(coords);

                        // Récupérer l'adresse lisible
                        const address = await getReadableAddress(coords);
                        setLocationAddress(address);
                      } catch (err) {
                        console.error('Erreur lors de la récupération de la localisation:', err);
                      }
                    }
                  }}
                >
                  <Text style={[styles.locationButtonText, { color: theme.colors.primary }]}>
                    Activer la localisation
                  </Text>
                </TouchableOpacity>
              )}
              <Ionicons
                name="location"
                size={20}
                color={location ? theme.colors.success : theme.colors.gray[400]}
              />
            </View>
          </View>

          <View style={styles.switchContainer}>
            <Text style={[styles.switchLabel, { color: theme.colors.text }]}>
              Rester anonyme
            </Text>
            <Switch
              value={isAnonymous}
              onValueChange={setIsAnonymous}
              trackColor={{ false: theme.colors.gray[300], true: theme.colors.primary }}
              thumbColor={Platform.OS === 'ios' ? 'white' : isAnonymous ? theme.colors.primary : theme.colors.gray[100]}
              ios_backgroundColor={theme.colors.gray[300]}
              disabled={submitting}
            />
          </View>

          <TouchableOpacity
            style={[
              styles.submitButton,
              { backgroundColor: theme.colors.bomoko.blue },
              submitting && { opacity: 0.7 }
            ]}
            onPress={handleSubmit}
            disabled={submitting}
          >
            {submitting ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <>
                <Ionicons name="send" size={20} color="white" style={styles.submitIcon} />
                <Text style={styles.submitButtonText}>Envoyer le signalement</Text>
              </>
            )}
          </TouchableOpacity>
        </Animated.View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 40,
  },
  errorContainer: {
    backgroundColor: '#FFEBEE',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
  },
  formCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  messageInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    minHeight: 120,
  },
  mediaButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  mediaButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  mediaButtonText: {
    color: 'white',
    fontWeight: '500',
    marginLeft: 8,
  },
  mediaPreviewContainer: {
    marginTop: 16,
  },
  mediaPreviewTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  mediaPreviewScroll: {
    paddingBottom: 8,
  },
  mediaPreviewItem: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 8,
    position: 'relative',
  },
  mediaPreviewImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  mediaPreviewPlaceholder: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  mediaRemoveButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: 'white',
    borderRadius: 12,
  },
  optionContainer: {
    marginBottom: 16,
  },
  optionLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
  },
  selectorText: {
    fontSize: 16,
  },
  optionsDropdown: {
    borderWidth: 1,
    borderRadius: 8,
    marginTop: 4,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  optionItemText: {
    fontSize: 16,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
  },
  locationText: {
    fontSize: 16,
  },
  locationButton: {
    flex: 1,
  },
  locationButtonText: {
    fontSize: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
  },
  submitIcon: {
    marginRight: 8,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
