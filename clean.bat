@echo off
echo Nettoyage de l'application...

echo Arrêt des processus Metro en cours...
npx kill-port 8081 19000 19001 19002

echo Suppression des dossiers de cache...
rmdir /s /q node_modules
rmdir /s /q .expo
rmdir /s /q .rn-cli.cache
rmdir /s /q android\build
rmdir /s /q ios\build
rmdir /s /q android\.gradle
rmdir /s /q ios\Pods

echo Suppression des fichiers de verrouillage...
del /f yarn.lock
del /f package-lock.json

echo Nettoyage du cache npm...
npm cache clean --force

echo Nettoyage du cache Watchman...
watchman watch-del-all

echo Réinstallation des dépendances...
npm install

echo Nettoyage du cache Metro...
start /b npx react-native start --reset-cache --no-interactive
timeout /t 10 /nobreak
taskkill /f /im node.exe

echo Nettoyage terminé. Vous pouvez maintenant redémarrer l'application avec 'npx expo start --clear'.
