-- Create webhook_configurations table if it doesn't exist
CREATE TABLE IF NOT EXISTS webhook_configurations (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HAR(100) NOT NULL UNIQUE,
    url VARCHAR(500) NOT NULL,
    backup_url VARCHAR(500),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    max_retries INTEGER DEFAULT 3,
    retry_delay_base INTEGER DEFAULT 2000,
    health_check_interval INTEGER DEFAULT 300,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    last_check_timestamp TIMESTAMPTZ,
    last_error_timestamp TIMESTAMPTZ,
    error_count INTEGER DEFAULT 0,
    error_message TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    CONSTRAINT valid_urls CHECK (
        url ~ '^https://' AND 
        (backup_url IS NULL OR backup_url ~ '^https://')
    )
);

-- Enable RLS
ALTER TABLE webhook_configurations ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON> policies
CREATE POLICY "webhook_read_policy"
    ON webhook_configurations
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "webhook_update_policy"
    ON webhook_configurations
    FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Update webhook configuration with correct URL and settings
INSERT INTO webhook_configurations (
    name,
    url,
    backup_url,
    description,
    is_active,
    max_retries,
    retry_delay_base,
    health_check_interval,
    metadata
) VALUES (
    'n8n_chatbot_agent',
    'https://n8n-dw1u.onrender.com/webhook-test/chat',
    'https://n8n-dw1u.onrender.com/webhook-test/chat',
    'Configuration for N8N chatbot webhook',
    true,
    3,
    2000,
    300,
    jsonb_build_object(
        'version', '1.0.0',
        'status', 'healthy',
        'last_update', CURRENT_TIMESTAMP,
        'webhook_type', 'chat',
        'content_type', 'application/json',
        'expected_response_format', jsonb_build_object(
            'output', 'string',
            'status', 'string',
            'metadata', 'object'
        )
    )
)
ON CONFLICT (name) 
DO UPDATE SET
    url = EXCLUDED.url,
    backup_url = EXCLUDED.backup_url,
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    metadata = EXCLUDED.metadata;

-- Create function to validate webhook response format
CREATE OR REPLACE FUNCTION validate_webhook_response(response jsonb)
RETURNS boolean AS $$
BEGIN
    RETURN (
        response ? 'output' AND 
        jsonb_typeof(response->'output') = 'string' AND
        response ? 'status'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;