/*
  # Update webhook URL policies and permissions

  1. Changes
    - Drop existing policies
    - Create new policies for URL updates
    - Add function for URL validation
  
  2. Security
    - Allows authenticated users to update URLs
    - Validates URLs against allowed patterns
*/

-- Drop existing policies
DROP POLICY IF EXISTS "Allow read access for authenticated users" ON webhook_urls;
DROP POLICY IF EXISTS "Allow all access for admins" ON webhook_urls;
DROP POLICY IF EXISTS "Allow URL updates for authenticated users" ON webhook_urls;
DROP POLICY IF EXISTS "Allow health status updates" ON webhook_urls;

-- Create new policies
CREATE POLICY "webhook_urls_read_policy"
    ON webhook_urls
    FOR SELECT
    TO authenticated
    USING (true);

CREATE POLICY "webhook_urls_update_policy"
    ON webhook_urls
    FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Update webhook URLs
UPDATE webhook_urls
SET 
    url = 'https://n8n-dw1u.onrender.com/webhook/chat',
    backup_url = 'https://n8n-dw1u.onrender.com/webhook/chat',
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    consecutive_failures = 0,
    last_successful_response = CURRENT_TIMESTAMP
WHERE name = 'n8n_chatbot_agent';