import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { router } from 'expo-router';
import { supabase } from '../../../lib/supabase';
import { useTheme } from '../../../context/theme';
import { useAuth } from '../../../context/auth';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';

type Conversation = {
  id: string;
  created_at: string;
  updated_at: string;
  last_message?: {
    content: string;
    role: string;
    created_at: string;
  } | null;
};

export default function ConversationsScreen() {
  const { theme } = useTheme();
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  useEffect(() => {
    loadConversations();
  }, []);

  const loadConversations = async () => {
    try {
      setError(null);
      if (!refreshing) setLoading(true);

      // Récupérer les conversations de l'utilisateur
      const { data: conversationsData, error: conversationsError } = await supabase
        .from('chat_conversations')
        .select('*')
        .eq('user_id', user?.id)
        .order('updated_at', { ascending: false });

      if (conversationsError) throw conversationsError;

      // Récupérer le dernier message pour chaque conversation
      const formattedConversations: Conversation[] = [];

      for (const conversation of conversationsData || []) {
        // Récupérer le dernier message de la conversation
        const { data: lastMessageData, error: lastMessageError } = await supabase
          .from('chat_messages')
          .select('*')
          .eq('conversation_id', conversation.id)
          .order('created_at', { ascending: false })
          .limit(1);

        if (lastMessageError) {
          console.error('Error fetching last message:', lastMessageError);
          continue;
        }

        const lastMessage = lastMessageData && lastMessageData.length > 0 ? lastMessageData[0] : null;

        formattedConversations.push({
          id: conversation.id,
          created_at: conversation.created_at,
          updated_at: conversation.updated_at,
          last_message: lastMessage ? {
            content: lastMessage.content,
            role: lastMessage.role,
            created_at: lastMessage.created_at
          } : null
        });
      }

      setConversations(formattedConversations);
    } catch (err: any) {
      console.error('Error loading conversations:', err);
      setError(`Erreur: ${err.message || 'Impossible de charger les conversations'}`);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    loadConversations();
  };

  const handleNewChat = () => {
    router.push('/assessment/chat');
  };

  const handleConversationPress = (conversationId: string) => {
    router.push({
      pathname: '/assessment/chat',
      params: { id: conversationId }
    });
  };

  const handleDeleteConversation = async (conversationId: string) => {
    try {
      setDeletingId(conversationId);

      // Supprimer d'abord les messages associés à la conversation
      const { error: messagesError } = await supabase
        .from('chat_messages')
        .delete()
        .eq('conversation_id', conversationId);

      if (messagesError) throw messagesError;

      // Puis supprimer la conversation elle-même
      const { error: conversationError } = await supabase
        .from('chat_conversations')
        .delete()
        .eq('id', conversationId);

      if (conversationError) throw conversationError;

      // Mettre à jour l'état local pour refléter la suppression
      setConversations(conversations.filter(conv => conv.id !== conversationId));

    } catch (err: any) {
      console.error('Erreur lors de la suppression:', err);
      setError(`Erreur: ${err.message || 'Impossible de supprimer la conversation'}`);
    } finally {
      setDeletingId(null);
    }
  };

  const formatDate = (date: string) => {
    const now = new Date();
    const messageDate = new Date(date);

    if (messageDate.toDateString() === now.toDateString()) {
      return format(messageDate, 'HH:mm');
    } else if (messageDate.getFullYear() === now.getFullYear()) {
      return format(messageDate, 'd MMM');
    }
    return format(messageDate, 'd MMM yyyy');
  };

  const renderConversation = ({ item }: { item: Conversation }) => (
    <TouchableOpacity
      style={[styles.conversationItem, { backgroundColor: theme.colors.surface }]}
      onPress={() => handleConversationPress(item.id)}>
      <View style={styles.conversationIcon}>
        <Ionicons name="chatbubbles-outline" size={24} color={theme.colors.primary} />
      </View>
      <View style={styles.conversationContent}>
        <View style={styles.conversationHeader}>
          <Text style={[styles.conversationTitle, { color: theme.colors.text }]}>
            Conversation du {formatDate(item.created_at)}
          </Text>
          {item.last_message && (
            <Text style={[styles.conversationTime, { color: theme.colors.gray[500] }]}>
              {formatDate(item.last_message.created_at)}
            </Text>
          )}
        </View>
        {item.last_message ? (
          <Text
            style={[styles.lastMessage, { color: theme.colors.gray[600] }]}
            numberOfLines={2}>
            {item.last_message.role === 'assistant' ? 'Assistant: ' : 'Vous: '}
            {item.last_message.content}
          </Text>
        ) : (
          <Text style={[styles.lastMessage, { color: theme.colors.gray[600], fontStyle: 'italic' }]}>
            Aucun message
          </Text>
        )}
      </View>
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={(e) => {
          e.stopPropagation();
          handleDeleteConversation(item.id);
        }}
        disabled={deletingId === item.id}>
        {deletingId === item.id ? (
          <ActivityIndicator size="small" color={theme.colors.error} />
        ) : (
          <Ionicons name="trash-outline" size={22} color={theme.colors.error} />
        )}
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={[styles.header, { borderBottomColor: theme.colors.gray[200] }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.push('/')}>
          <Ionicons
            name="arrow-back"
            size={24}
            color={theme.colors.text}
          />
        </TouchableOpacity>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          Conversations
        </Text>
      </View>

      {error ? (
        <View style={styles.centerContent}>
          <Text style={[styles.error, { color: theme.colors.error }]}>{error}</Text>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: theme.colors.primary }]}
            onPress={loadConversations}>
            <Text style={styles.retryButtonText}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : loading && !refreshing ? (
        <View style={styles.centerContent}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={[styles.loadingText, { color: theme.colors.text }]}>
            Chargement des conversations...
          </Text>
        </View>
      ) : conversations.length === 0 ? (
        <View style={styles.centerContent}>
          <Text style={[styles.emptyText, { color: theme.colors.gray[600] }]}>
            Aucune conversation
          </Text>
          <TouchableOpacity
            style={[styles.newChatButton, { backgroundColor: theme.colors.primary }]}
            onPress={handleNewChat}>
            <Text style={styles.newChatButtonText}>Démarrer une conversation</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={conversations}
          renderItem={renderConversation}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.list}
          onRefresh={handleRefresh}
          refreshing={refreshing}
        />
      )}

      <TouchableOpacity
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleNewChat}>
        <Ionicons name="add" size={24} color="white" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    borderBottomWidth: 1,
  },
  backButton: {
    marginRight: 15,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  error: {
    marginBottom: 15,
    textAlign: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  retryButton: {
    padding: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyText: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
  newChatButton: {
    padding: 15,
    borderRadius: 8,
  },
  newChatButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  list: {
    padding: 10,
  },
  conversationItem: {
    flexDirection: 'row',
    padding: 15,
    borderRadius: 12,
    marginBottom: 10,
    alignItems: 'center',
  },
  conversationIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  conversationContent: {
    flex: 1,
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  conversationTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  conversationTime: {
    fontSize: 12,
  },
  lastMessage: {
    fontSize: 14,
    lineHeight: 20,
  },
  fab: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  deleteButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 5,
  },
});