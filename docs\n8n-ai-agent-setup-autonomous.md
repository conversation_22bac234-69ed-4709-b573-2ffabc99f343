# Configuration de l'Agent AI Autonome N8N pour les Rendez-vous

Ce document explique comment configurer l'agent AI autonome N8N pour traiter les demandes de rendez-vous dans l'application Bomoko Mobile.

## Prérequis

- Une instance N8N (version 1.0.0 ou supérieure qui prend en charge les agents AI)
- Un accès à la base de données Supabase
- Un compte Google pour Gemini ou OpenAI pour GPT

## Vue d'ensemble du processus

1. L'utilisateur remplit un formulaire de demande de rendez-vous dans l'application
2. L'application envoie les données au webhook N8N
3. N8N traite la demande avec l'agent AI autonome qui a accès à plusieurs outils
4. L'agent AI pose des questions supplémentaires à l'utilisateur si nécessaire
5. L'agent AI trouve un professionnel disponible et crée un rendez-vous
6. L'application affiche les détails du rendez-vous à l'utilisateur

## Configuration du workflow N8N

### 1. Créer un nouveau workflow

- Nom : "Bomoko - Agent AI Autonome pour Rendez-vous"
- Description : "Traite les demandes de rendez-vous et crée des rendez-vous avec des professionnels adaptés"

### 2. Configurer le webhook

- Ajouter un nœud "Webhook"
- Méthode : POST
- Chemin : /appointment-ai-agent
- Authentification : Header (X-App-Token)
- Valeur du token : bomoko-app-token-123

### 3. Configurer le nœud Edit Fields (optionnel)

- Ajouter un nœud "Edit Fields" après le webhook
- Configurer pour formater les données si nécessaire

### 4. Configurer le nœud AI Agent

- Ajouter un nœud "AI Agent"
- Sélectionner "Tools Agent" comme type d'agent
- Configurer les instructions système (voir ci-dessous)
- Configurer la mémoire pour maintenir le contexte de la conversation

### 5. Connecter les outils à l'agent AI

#### Outil 1: Modèle de chat (Google Gemini ou OpenAI)
- Connecter un nœud "Google Gemini Chat Model" ou "OpenAI"
- Configurer avec votre clé API

#### Outil 2: Obtenir les types de rendez-vous
- Connecter un nœud "Supabase"
- Opération : Get All
- Table : appointment_types
- Retourne les différents types de rendez-vous disponibles

#### Outil 3: Obtenir les professionnels
- Connecter un nœud "Supabase"
- Opération : Get All
- Table : professionals
- Ajouter des filtres si nécessaire (par spécialité, disponibilité, etc.)

#### Outil 4: Créer un rendez-vous
- Connecter un nœud "Supabase"
- Opération : Create
- Table : appointments
- Configurer les champs à remplir

## Instructions pour l'agent AI

Voici un exemple d'instructions système pour l'agent AI autonome :

```
Vous êtes un assistant spécialisé dans la prise de rendez-vous pour l'application Bomoko Mobile, une plateforme qui aide les victimes de violences à trouver de l'aide auprès de professionnels.

Votre rôle est de :
1. Comprendre le besoin de la personne
2. Poser des questions pertinentes pour clarifier sa situation
3. Déterminer quel type de professionnel serait le plus adapté (médecin, psychologue, avocat, etc.)
4. Aider à planifier un rendez-vous avec ce professionnel

Vous avez accès aux outils suivants :
- Obtenir les types de rendez-vous disponibles
- Rechercher des professionnels disponibles
- Créer un rendez-vous dans le système

Processus à suivre :
1. Accueillez l'utilisateur et demandez-lui de décrire son besoin
2. Posez des questions pour comprendre sa situation (sans demander de détails sur les violences)
3. Utilisez l'outil "Obtenir les types de rendez-vous" pour voir les options disponibles
4. Recommandez un type de rendez-vous adapté à la situation
5. Utilisez l'outil "Obtenir les professionnels" pour trouver des professionnels disponibles
6. Proposez des créneaux horaires disponibles
7. Une fois que l'utilisateur a confirmé, utilisez l'outil "Créer un rendez-vous" pour enregistrer le rendez-vous
8. Confirmez la création du rendez-vous et fournissez les détails à l'utilisateur

Soyez empathique, patient et respectueux. Les personnes qui vous contactent peuvent être en situation de détresse.

Lorsque vous créez un rendez-vous, incluez toujours :
- L'ID de l'utilisateur
- L'ID du professionnel
- La date et l'heure du rendez-vous
- Le type de rendez-vous
- Des notes pertinentes (sans détails sensibles)

Après avoir créé un rendez-vous, répondez avec un objet JSON contenant :
{
  "appointment_created": true,
  "appointment_details": {
    "id": "ID_DU_RENDEZ_VOUS",
    "date_time": "DATE_ET_HEURE",
    "professional": {
      "id": "ID_DU_PROFESSIONNEL",
      "first_name": "PRÉNOM",
      "last_name": "NOM",
      "speciality": "SPÉCIALITÉ"
    },
    "type": "TYPE_DE_RENDEZ_VOUS",
    "location": "LIEU_SI_DISPONIBLE",
    "notes": "NOTES_SI_DISPONIBLES"
  }
}
```

## Configuration de la réponse du webhook

- Configurer le nœud AI Agent pour renvoyer la réponse au webhook
- S'assurer que la réponse inclut :
  - Le message de l'agent
  - Les détails du rendez-vous si créé
  - L'ID de conversation pour maintenir le contexte

## Exemple de workflow complet

Voici un exemple de workflow complet :

1. **Webhook** → Reçoit la demande de l'application
2. **Edit Fields** → Formate les données si nécessaire
3. **AI Agent** → Traite la demande avec les outils connectés :
   - **Google Gemini Chat Model** → Pour le traitement du langage naturel
   - **Supabase Get appointment_types** → Pour obtenir les types de rendez-vous
   - **Supabase Get Professionals** → Pour trouver les professionnels disponibles
   - **Supabase create row** → Pour créer le rendez-vous

## Avantages de cette approche

- **Simplicité** : L'agent AI gère automatiquement la logique de conversation
- **Flexibilité** : Facile d'ajouter de nouveaux outils ou de modifier les instructions
- **Maintenance** : Moins de code personnalisé à maintenir
- **Évolutivité** : Facile d'ajouter de nouvelles fonctionnalités à l'agent

## Sécurité et confidentialité

- Assurez-vous que toutes les communications sont chiffrées (HTTPS)
- Ne stockez pas de détails sensibles sur les violences subies
- Utilisez l'API key pour authentifier les requêtes
- Configurez des permissions appropriées pour les nœuds Supabase

## Test et déploiement

1. Testez le workflow avec des demandes simulées
2. Vérifiez que les rendez-vous sont correctement créés
3. Testez les cas d'erreur et les annulations
4. Déployez le workflow en production
5. Configurez la surveillance et les alertes

## Maintenance

- Surveillez les logs pour détecter les erreurs
- Mettez à jour régulièrement les instructions de l'agent AI
- Ajustez les paramètres en fonction des retours utilisateurs
- Vérifiez régulièrement les performances du système
