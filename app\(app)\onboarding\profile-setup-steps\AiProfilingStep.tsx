import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator, ScrollView, Alert, Platform, FlatList } from 'react-native';
import { useProfileStore } from './store';
import { Ionicons } from '@expo/vector-icons';

// Questions prédéfinies pour le profilage
const predefinedQuestions = [
  "Pouvez-vous décrire une situation difficile que vous avez récemment vécue ?",
  "Vous sentez-vous en sécurité à votre domicile ou lieu de résidence actuel ?",
  "Avez-vous déjà été menacé(e) ou avez-vous peur de quelqu'un de votre entourage ?",
  "Avez-vous besoin d'une assistance immédiate ou d'un refuge sécurisé ?",
  "Sur une échelle de 1 à 10, comment évalueriez-vous votre niveau d'anxiété actuel ?"
];

// Réponses prédéfinies pour chaque question
const predefinedAnswers = [
  // Question 1: Situation difficile
  [
    "Je n'ai pas vécu de situation particulièrement difficile récemment",
    "J'ai eu quelques conflits mineurs avec mes proches",
    "J'ai traversé des difficultés financières récemment",
    "J'ai vécu une période de stress intense au travail/études",
    "J'ai eu des problèmes de santé qui m'ont inquiété",
    "J'ai vécu une rupture ou une perte relationnelle"
  ],
  // Question 2: Sécurité au domicile
  [
    "Oui, je me sens tout à fait en sécurité chez moi",
    "Généralement oui, bien que parfois certaines situations m'inquiètent",
    "Je ne me sens pas totalement en sécurité en ce moment",
    "Non, j'ai des inquiétudes constantes concernant ma sécurité",
    "Je suis temporairement dans un environnement qui n'est pas sécurisé"
  ],
  // Question 3: Menaces
  [
    "Non, je ne me sens pas menacé(e) par qui que ce soit",
    "Pas directement, mais certaines relations me rendent anxieux/anxieuse",
    "J'ai eu des conflits qui m'ont fait sentir en danger par le passé",
    "Oui, je crains parfois pour ma sécurité à cause d'une personne spécifique",
    "Je suis actuellement dans une situation où je me sens menacé(e)"
  ],
  // Question 4: Assistance
  [
    "Non, je n'ai pas besoin d'assistance immédiate",
    "J'aimerais parler à un conseiller, mais ce n'est pas urgent",
    "J'aurais besoin d'informations sur les ressources disponibles",
    "Je traverse une période difficile et pourrais avoir besoin d'aide bientôt",
    "Oui, j'aurais besoin d'une assistance assez rapidement"
  ],
  // Question 5: Niveau d'anxiété
  [
    "1-2: Très faible, je me sens calme et serein(e)",
    "3-4: Légèrement anxieux/anxieuse mais gérable",
    "5-6: Niveau modéré d'anxiété qui affecte mon quotidien",
    "7-8: Anxiété élevée qui perturbe significativement ma vie",
    "9-10: Anxiété extrême, difficulté à fonctionner normalement"
  ]
];

// Simuler une réponse de l'IA (dans un cas réel, cela appellerait une API)
const simulateAiResponse = async (question: string, answer: string): Promise<string> => {
  // Simulation d'un délai de réponse
  await new Promise(resolve => setTimeout(resolve, 1500));

  // Logique simple de classification basée sur des mots-clés
  const lowRiskKeywords = ['bien', 'sécurité', 'calme', 'stable', 'support', 'pas menacé', 'ne me sens pas menacé', 'faible', 'très faible'];
  const mediumRiskKeywords = ['stress', 'inquiet', 'tension', 'peur', 'problème', 'modéré', 'inquiètent', 'pas totalement'];
  const highRiskKeywords = ['danger', 'menace', 'mort', 'violence', 'urgence', 'suicide', 'élevée', 'extrême', 'actuellement dans une situation'];

  const answerLower = answer.toLowerCase();

  let riskLevel = 'low';
  let riskScore = 0;

  // Compter les occurrences de mots-clés
  highRiskKeywords.forEach(keyword => {
    if (answerLower.includes(keyword)) riskScore += 3;
  });

  mediumRiskKeywords.forEach(keyword => {
    if (answerLower.includes(keyword)) riskScore += 1;
  });

  lowRiskKeywords.forEach(keyword => {
    if (answerLower.includes(keyword)) riskScore -= 1;
  });

  // Déterminer le niveau de risque
  if (riskScore >= 3) {
    riskLevel = 'high';
  } else if (riskScore >= 1) {
    riskLevel = 'medium';
  }

  const responses = {
    high: "Notre évaluation indique que vous pourriez être en situation de risque élevé. Nous vous recommandons vivement de contacter immédiatement nos services d'urgence ou les autorités locales. Un conseiller spécialisé sera également assigné à votre dossier pour un suivi prioritaire.",
    medium: "Votre situation semble présenter certains facteurs de risque. Nous vous suggérons de prendre rendez-vous avec l'un de nos conseillers dans les prochains jours pour discuter de votre situation et des ressources disponibles.",
    low: "D'après notre évaluation préliminaire, votre situation ne présente pas de danger immédiat. Nous sommes néanmoins à votre disposition pour vous offrir du soutien et des ressources adaptées à vos besoins."
  };

  return responses[riskLevel as keyof typeof responses];
};

export function AiProfilingStep() {
  const { profile, updateProfile, setCurrentStep, addAiResponse } = useProfileStore();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [currentAnswer, setCurrentAnswer] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [finalEvaluation, setFinalEvaluation] = useState<string | null>(null);
  const [riskLevel, setRiskLevel] = useState<'low' | 'medium' | 'high' | null>(null);
  const [showAnswerChoices, setShowAnswerChoices] = useState(false);

  const currentQuestion = predefinedQuestions[currentQuestionIndex];
  const answersForCurrentQuestion = predefinedAnswers[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === predefinedQuestions.length - 1;

  const handlePrevious = () => {
    setCurrentStep(1);
  };

  const handleNext = () => {
    if (finalEvaluation) {
      setCurrentStep(3);
    }
  };

  const handleSelectAnswer = (answer: string) => {
    setCurrentAnswer(answer);
    setShowAnswerChoices(false);
  };

  const handleSubmitAnswer = async () => {
    if (!currentAnswer.trim()) return;

    setIsProcessing(true);

    try {
      // Enregistrer la question et la réponse
      addAiResponse(currentQuestion, currentAnswer);

      // Si c'est la dernière question, générer une évaluation finale
      if (isLastQuestion) {
        const evaluation = await simulateAiResponse(currentQuestion, currentAnswer);
        setFinalEvaluation(evaluation);

        // Déterminer le niveau de risque basé sur l'évaluation
        let detectedRiskLevel: 'low' | 'medium' | 'high' = 'low';
        if (evaluation.includes('risque élevé')) {
          detectedRiskLevel = 'high';
        } else if (evaluation.includes('facteurs de risque')) {
          detectedRiskLevel = 'medium';
        }

        setRiskLevel(detectedRiskLevel);
        updateProfile({
          riskLevel: detectedRiskLevel,
          aiEvaluation: evaluation
        });
      } else {
        // Passer à la question suivante
        setCurrentQuestionIndex(prev => prev + 1);
        setCurrentAnswer('');
      }
    } catch (error) {
      console.error('Erreur lors du traitement de la réponse:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <Text style={styles.cardTitle}>Évaluation personnalisée</Text>
        <Text style={styles.cardDescription}>
          {finalEvaluation
            ? "Votre évaluation est terminée"
            : "Veuillez répondre aux questions pour nous aider à mieux comprendre votre situation"}
        </Text>
      </View>

      <View style={styles.cardContent}>
        {finalEvaluation ? (
          <>
            <View style={[
              styles.alert,
              riskLevel === 'high' ? styles.alertHigh :
              riskLevel === 'medium' ? styles.alertMedium :
              styles.alertLow
            ]}>
              <View style={styles.alertHeader}>
                <Ionicons
                  name={riskLevel === 'high' ? 'warning' : 'information-circle'}
                  size={20}
                  color={
                    riskLevel === 'high' ? '#ef4444' :
                    riskLevel === 'medium' ? '#f59e0b' :
                    '#22c55e'
                  }
                />
                <Text style={styles.alertTitle}>
                  {riskLevel === 'high' ? "Niveau de risque élevé" :
                   riskLevel === 'medium' ? "Niveau de risque modéré" :
                   "Niveau de risque faible"}
                </Text>
              </View>
              <Text style={styles.alertDescription}>
                {finalEvaluation}
              </Text>
            </View>

            <Text style={styles.helpText}>
              Merci d'avoir complété cette évaluation. Ces informations nous aideront à vous offrir une assistance adaptée à vos besoins.
            </Text>
          </>
        ) : (
          <>
            <View style={styles.questionBox}>
              <Text style={styles.questionCount}>
                Question {currentQuestionIndex + 1}/{predefinedQuestions.length}
              </Text>
              <Text style={styles.questionText}>{currentQuestion}</Text>
            </View>

            {showAnswerChoices ? (
              <ScrollView style={styles.answersContainer}>
                {answersForCurrentQuestion.map((answer, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.answerOption}
                    onPress={() => handleSelectAnswer(answer)}
                  >
                    <Text style={styles.answerOptionText}>{answer}</Text>
                    <Ionicons name="chevron-forward" size={16} color="#999" />
                  </TouchableOpacity>
                ))}
              </ScrollView>
            ) : (
              <View style={styles.inputContainer}>
                <TouchableOpacity
                  style={styles.answerField}
                  onPress={() => setShowAnswerChoices(true)}
                >
                  {currentAnswer ? (
                    <Text style={styles.selectedAnswerText}>{currentAnswer}</Text>
                  ) : (
                    <View style={styles.placeholderContainer}>
                      <Ionicons name="list" size={20} color="#999" />
                      <Text style={styles.placeholderText}>
                        Choisir parmi les réponses suggérées
                      </Text>
                    </View>
                  )}
                  <Ionicons name="chevron-down" size={20} color="#999" />
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.submitButton,
                    (!currentAnswer.trim() || isProcessing) && styles.buttonDisabled
                  ]}
                  onPress={handleSubmitAnswer}
                  disabled={!currentAnswer.trim() || isProcessing}
                >
                  {isProcessing ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <View style={styles.buttonContent}>
                      <Ionicons name="send" size={16} color="#fff" style={styles.buttonIcon} />
                      <Text style={styles.buttonText}>
                        {isLastQuestion ? "Finaliser l'évaluation" : "Répondre"}
                      </Text>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
            )}
          </>
        )}
      </View>

      <View style={styles.cardFooter}>
        <TouchableOpacity
          style={styles.secondaryButton}
          onPress={handlePrevious}
        >
          <Text style={styles.secondaryButtonText}>Précédent</Text>
        </TouchableOpacity>

        {finalEvaluation && (
          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleNext}
          >
            <Text style={styles.primaryButtonText}>Suivant</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#fff',
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    margin: 16,
    overflow: 'hidden'
  },
  cardHeader: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: '#fafafa'
  },
  cardTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8
  },
  cardDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20
  },
  cardContent: {
    padding: 20
  },
  cardFooter: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  alert: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16
  },
  alertHigh: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderLeftWidth: 4,
    borderLeftColor: '#ef4444'
  },
  alertMedium: {
    backgroundColor: 'rgba(245, 158, 11, 0.1)',
    borderLeftWidth: 4,
    borderLeftColor: '#f59e0b'
  },
  alertLow: {
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    borderLeftWidth: 4,
    borderLeftColor: '#22c55e'
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8
  },
  alertTitle: {
    fontWeight: '600',
    fontSize: 16,
    marginLeft: 8
  },
  alertDescription: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20
  },
  helpText: {
    fontSize: 14,
    color: '#666',
    marginTop: 16
  },
  questionBox: {
    padding: 16,
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 12,
    marginBottom: 16,
    backgroundColor: '#fafafa'
  },
  questionCount: {
    fontWeight: '600',
    marginBottom: 8,
    fontSize: 14,
    color: '#666'
  },
  questionText: {
    fontSize: 18,
    color: '#333',
    fontWeight: '500'
  },
  inputContainer: {
    marginBottom: 16
  },
  answersContainer: {
    maxHeight: 300,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 12,
  },
  answerOption: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  answerOptionText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
    paddingRight: 8
  },
  answerField: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f9f9f9',
    minHeight: 56
  },
  placeholderContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  placeholderText: {
    color: '#999',
    fontSize: 16,
    marginLeft: 8
  },
  selectedAnswerText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
    paddingRight: 8
  },
  submitButton: {
    backgroundColor: '#0066ff',
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  buttonDisabled: {
    opacity: 0.5
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  buttonIcon: {
    marginRight: 8
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16
  },
  primaryButton: {
    backgroundColor: '#0066ff',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  primaryButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16
  },
  secondaryButton: {
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20
  },
  secondaryButtonText: {
    color: '#333',
    fontWeight: '600',
    fontSize: 16
  }
});

// Export par défaut pour Expo Router
export default AiProfilingStep;