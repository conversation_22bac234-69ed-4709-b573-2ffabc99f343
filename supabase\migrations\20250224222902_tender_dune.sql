-- Drop existing function if it exists
DROP FUNCTION IF EXISTS check_webhook_configuration(text);

-- Create improved webhook configuration check function
CREATE OR REPLACE FUNCTION check_webhook_configuration(webhook_name TEXT)
RETURNS TABLE (
    is_available boolean,
    current_url varchar(500),
    error_details text
) AS $$
DECLARE
    config_record webhook_configurations%ROWTYPE;
BEGIN
    -- Get webhook record with error handling
    BEGIN
        SELECT * INTO config_record
        FROM webhook_configurations
        WHERE name = webhook_name;

        -- Check if webhook exists
        IF config_record IS NULL THEN
            RETURN QUERY SELECT 
                false::boolean, 
                NULL::varchar(500), 
                'Configuration non disponible'::text;
            RETURN;
        END IF;

        -- Check if webhook is active
        IF NOT config_record.is_active THEN
            RETURN QUERY SELECT 
                false::boolean, 
                NULL::varchar(500), 
                'Service temporairement désactivé'::text;
            RETURN;
        END IF;

        -- Check error count and backup URL
        IF config_record.error_count >= config_record.max_retries THEN
            IF config_record.backup_url IS NOT NULL AND config_record.backup_url != '' THEN
                -- Use backup URL
                RETURN QUERY SELECT 
                    true::boolean,
                    config_record.backup_url::varchar(500),
                    ''::text;
            ELSE
                -- No backup available
                RETURN QUERY SELECT 
                    false::boolean,
                    NULL::varchar(500),
                    'Service temporairement indisponible'::text;
            END IF;
            RETURN;
        END IF;

        -- Return primary URL if everything is OK
        RETURN QUERY SELECT 
            true::boolean,
            config_record.url::varchar(500),
            ''::text;

    EXCEPTION WHEN OTHERS THEN
        -- Handle unexpected errors
        RETURN QUERY SELECT 
            false::boolean, 
            NULL::varchar(500), 
            'Erreur interne du service: ' || SQLERRM::text;
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update webhook configurations with correct URLs
UPDATE webhook_configurations
SET 
    url = 'https://n8n-dw1u.onrender.com/webhook-test/chat',
    backup_url = 'https://n8n-dw1u.onrender.com/webhook-test/chat',
    is_active = true,
    error_count = 0,
    error_message = null,
    last_check_timestamp = CURRENT_TIMESTAMP,
    metadata = jsonb_build_object(
        'version', '1.0.1',
        'status', 'healthy',
        'last_update', CURRENT_TIMESTAMP,
        'webhook_type', 'chat',
        'content_type', 'application/json',
        'expected_response_format', jsonb_build_object(
            'output', 'string',
            'status', 'string',
            'metadata', 'object'
        )
    )
WHERE name = 'n8n_chatbot_agent';