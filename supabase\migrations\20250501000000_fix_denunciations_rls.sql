/*
  # Fix RLS policies for denunciations table

  1. Changes
    - Update RLS policies for denunciations table to allow anonymous submissions
    - Ensure proper access control for denunciations

  2. Security
    - Maintain existing RLS policies
    - Fix policy for anonymous denunciations
*/

-- Drop existing policy for inserting denunciations
DROP POLICY IF EXISTS "Users can create denunciations" ON denunciations;

-- Create new policy that allows anonymous submissions
CREATE POLICY "Users can create denunciations"
    ON denunciations
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

-- Drop existing policy for viewing denunciations if it exists
DROP POLICY IF EXISTS "Users can view all their denunciations" ON denunciations;
DROP POLICY IF EXISTS "Users can view their own denunciations" ON denunciations;

-- Create policy for viewing denunciations
-- This policy allows users to view their own denunciations, including anonymous ones
CREATE POLICY "Users can view all their denunciations"
    ON denunciations
    FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());
