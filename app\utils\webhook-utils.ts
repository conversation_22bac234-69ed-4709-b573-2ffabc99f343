import { supabase } from '../../lib/supabase';

/**
 * Récupère l'URL du webhook depuis la table webhook_configurations
 * @param name Nom du webhook à récupérer
 * @returns URL du webhook ou null si non trouvé
 */
export async function getWebhookUrl(name: string = 'RDV AI WEBHOOK URL'): Promise<string | null> {
  try {
    // Récupérer la configuration du webhook depuis la base de données
    const { data: webhookConfig, error: webhookError } = await supabase
      .from('webhook_configurations')
      .select('url, backup_url, is_active, api_key, backup_api_key')
      .eq('name', name)
      .single();
    
    if (webhookError || !webhookConfig) {
      console.error('Erreur lors de la récupération du webhook:', webhookError?.message || 'Configuration non trouvée');
      return null;
    }
    
    if (!webhookConfig.is_active) {
      console.warn(`Le webhook ${name} est désactivé.`);
      return null;
    }
    
    // URL fallback si l'URL principale est vide
    const webhookUrl = webhookConfig.url && webhookConfig.url !== 'https://' 
      ? webhookConfig.url 
      : webhookConfig.backup_url;
    
    if (!webhookUrl || webhookUrl === 'https://') {
      console.error(`URL du webhook ${name} non configurée`);
      return null;
    }
    
    return webhookUrl;
  } catch (error) {
    console.error('Erreur lors de la récupération du webhook:', error);
    return null;
  }
}

/**
 * Récupère la configuration complète du webhook depuis la table webhook_configurations
 * @param name Nom du webhook à récupérer
 * @returns Configuration du webhook ou null si non trouvé
 */
export async function getWebhookConfig(name: string = 'RDV AI WEBHOOK URL'): Promise<{
  url: string | null;
  backup_url: string | null;
  is_active: boolean;
  api_key: string | null;
  backup_api_key: string | null;
} | null> {
  try {
    // Récupérer la configuration du webhook depuis la base de données
    const { data: webhookConfig, error: webhookError } = await supabase
      .from('webhook_configurations')
      .select('url, backup_url, is_active, api_key, backup_api_key')
      .eq('name', name)
      .single();
    
    if (webhookError || !webhookConfig) {
      console.error('Erreur lors de la récupération du webhook:', webhookError?.message || 'Configuration non trouvée');
      return null;
    }
    
    return webhookConfig;
  } catch (error) {
    console.error('Erreur lors de la récupération du webhook:', error);
    return null;
  }
}

/**
 * Envoie une requête au webhook
 * @param url URL du webhook
 * @param data Données à envoyer
 * @returns Réponse du webhook ou null si erreur
 */
export async function sendWebhookRequest(url: string, data: any): Promise<any> {
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-App-Token': 'bomoko-app-token-123',
      },
      body: JSON.stringify(data)
    });
    
    // Lire le corps de la réponse même si elle n'est pas OK
    const responseText = await response.text();
    
    // Essayer de parser la réponse comme JSON
    try {
      const responseData = JSON.parse(responseText);
      return { ok: response.ok, data: responseData };
    } catch (parseError) {
      return { ok: response.ok, data: { message: responseText } };
    }
  } catch (error) {
    console.error('Erreur lors de l\'envoi de la requête au webhook:', error);
    return { ok: false, error };
  }
}
